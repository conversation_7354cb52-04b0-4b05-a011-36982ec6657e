from yolact import Yolact
from utils.augmentations import BaseTransform, FastBaseTransform
from utils import timer
from layers.output_utils import postprocess, undo_image_transformation
from data import cfg, set_cfg
from collections import Counter
import torch
import os
import cv2
def prep_display(dets_out, img, h, w, undo_transform=True,score_thred=0.2,mask_alpha=0.45,class_names=['live','dead']):
    """
    Note: If undo_transform=False then im_h and im_w are allowed to be None.
    """
    if undo_transform:
        img_numpy = undo_image_transformation(img, w, h)
        # img_gpu = torch.Tensor(img_numpy).cuda()
        img_gpu = torch.Tensor(img_numpy)
    else:
        img_gpu = img / 255.0
        h, w, _ = img.shape

    with timer.env('Postprocess'):
        t = postprocess(dets_out, w, h, visualize_lincomb = False,
                        crop_masks=True,
                        score_threshold=score_thred)
    with timer.env('Copy'):
        # Masks are drawn on the GPU, so don't copy
        idx = t[1].argsort(0, descending=True)
        masks = t[3][idx]
        classes, scores, boxes = [x[:].cpu().numpy() for x in t[:3]]

    num_dets_to_consider = classes.shape[0]
    for j in range(num_dets_to_consider):
        if scores[j] < score_thred:
            num_dets_to_consider = j
            break

    if num_dets_to_consider == 0:
        # No detections found so just output the original image
        return (img_gpu * 255).byte().cpu().numpy()
    print(num_dets_to_consider)

    # Quick and dirty lambda for selecting the color for a particular index
    # Also keeps track of a per-gpu color cache for maximum speed
    def live_and_dead_color(j):
        _class = class_names[classes[j]]
        if _class == 'live':
            color = (0, 0, 255)
        elif _class == 'dead':
            color = (0, 255, 0)
        return color
    # First, draw the masks on the GPU where we can do it really fast
    # Beware: very fast but possibly unintelligible mask-drawing code ahead
    # I wish I had access to OpenGL or Vulkan but alas, I guess Pytorch tensor operations will have to suffice
    if num_dets_to_consider>0:
        # After this, mask is of size [num_dets, h, w, 1]
        masks = masks[:num_dets_to_consider, :, :, None]
        # pdb.set_trace()
        # Prepare the RGB images for each mask given their color (size [num_dets, h, w, 1])
        # colors = torch.cat([get_color(j, on_gpu=img.device.index).view(1, 1, 1, 3) for j in range(num_dets_to_consider)], dim=0)
        colors = torch.cat([torch.FloatTensor(live_and_dead_color(j)).view(1, 1, 1, 3) for j in range(num_dets_to_consider)], dim=0)
        # for j in range num_dets_to_consider
        masks_color = masks.repeat(1, 1, 1, 3) * colors * mask_alpha

        # This is 1 everywhere except for 1-mask_alpha where the mask is
        inv_alph_masks = masks * (-mask_alpha) + 1

        # I did the math for this on pen and paper. This whole block should be equivalent to:
        #    for j in range(num_dets_to_consider):
        #        img_gpu = img_gpu * inv_alph_masks[j] + masks_color[j]
        masks_color_summand = masks_color[0]
        if num_dets_to_consider > 1:
            inv_alph_cumul = inv_alph_masks[:(num_dets_to_consider-1)].cumprod(dim=0)
            masks_color_cumul = masks_color[1:] * inv_alph_cumul
            masks_color_summand += masks_color_cumul.sum(dim=0)

        img_gpu = img_gpu * inv_alph_masks.prod(dim=0) + masks_color_summand

    # Then draw the stuff that needs to be done on the cpu
    # Note, make sure this is a uint8 tensor or opencv will not anti alias text for whatever reason
    img_numpy = (img_gpu * 255).byte().cpu().numpy()
    result={cfg.dataset.class_names[k]:v for k,v in Counter(classes).items()}
    print(result)
    if len(result.keys())>1:
        cv2.putText(img_numpy, str("live %s dead %s" %(result['live'],result['dead'])), (50, 50), cv2.FONT_HERSHEY_DUPLEX, 1.5, [255, 255, 0])
    else:
        cv2.putText(img_numpy, str("live %s" % (result['live'])), (50, 50),
                    cv2.FONT_HERSHEY_DUPLEX, 1.5, [255, 255, 0])
    if num_dets_to_consider>0:
        for j in reversed(range(num_dets_to_consider)):
            x1, y1, x2, y2 = boxes[j, :]
            score = scores[j]
            cv2.rectangle(img_numpy, (x1, y1), (x2, y2), live_and_dead_color(j), 1)
            # _class =class_names[classes[j]]
            # text_str = '%s: %.2f' % (_class, score)
            #
            # font_face = cv2.FONT_HERSHEY_DUPLEX
            # font_scale = 0.6
            # font_thickness = 1
            #
            # text_w, text_h = cv2.getTextSize(text_str, font_face, font_scale, font_thickness)[0]
            #
            # text_pt = (x1, y1 - 3)
            # text_color = [255, 255, 255]
            #
            # cv2.rectangle(img_numpy, (x1, y1), (x1 + text_w, y1 - text_h - 4), color, -1)
            # cv2.putText(img_numpy, text_str, text_pt, font_face, font_scale, text_color, font_thickness, cv2.LINE_AA)

    return img_numpy
if __name__ == '__main__':
    set_cfg('yolact_cell_config')
    #set_dataset('cell_dataset')
    net = Yolact()
    net.load_weights('weights/yolact_tb_resnet101_2499_10000.pth')
    net.eval()
    net.detect.use_fast_nms = True
    net.detect.cross_class_nms = True
    print('weights Done.')
    # frame = torch.from_numpy(cv2.imread('1a1.jpg')).float()
    # batch = FastBaseTransform()(frame.unsqueeze(0))
    # preds = net(batch)
    # img_numpy = prep_display(preds, frame, None, None, undo_transform=False)
    # cv2.imwrite('outputs2.jpg', img_numpy)
    for i in os.listdir('Images2'):
        image = cv2.imread('Images2/'+i)
        print(i)
        h, w = image.shape[:2]
        with torch.no_grad():
            image = cv2.resize(image, (550, 550))
            frame = torch.from_numpy(image).float()
            batch = FastBaseTransform()(frame.unsqueeze(0))
            preds = net(batch)
            img_numpy = prep_display(preds, frame, None, None, undo_transform=False)
            cv2.imwrite('outputs2/'+i, img_numpy)