('F:\\pack\\serveris\\build\\cell_count\\PYZ-00.pyz',
 [('pkg_resources',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('typing',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\typing.py',
   'PYMODULE'),
  ('contextlib',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('__future__',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\__future__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('uuid',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\uuid.py',
   'PYMODULE'),
  ('hashlib',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\pickle.py',
   'PYMODULE'),
  ('doctest',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\doctest.py',
   'PYMODULE'),
  ('argparse',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\argparse.py',
   'PYMODULE'),
  ('gettext',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\gettext.py',
   'PYMODULE'),
  ('unittest',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('signal',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\signal.py',
   'PYMODULE'),
  ('unittest.main',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('fnmatch',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\fnmatch.py',
   'PYMODULE'),
  ('unittest.suite',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.async_case',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('selectors',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\selectors.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\ssl.py', 'PYMODULE'),
  ('calendar',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\calendar.py',
   'PYMODULE'),
  ('base64',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\getopt.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.events',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('contextvars',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('concurrent.futures',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\queue.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('bisect',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\bisect.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\runpy.py',
   'PYMODULE'),
  ('importlib.util',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('configparser',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('pathlib',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\pathlib.py',
   'PYMODULE'),
  ('urllib.parse',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.utils',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._encoded_words',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email.header',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.quoprimime',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.charset',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\quopri.py',
   'PYMODULE'),
  ('email.errors',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\csv.py', 'PYMODULE'),
  ('tokenize',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\token.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.abc',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\secrets.py',
   'PYMODULE'),
  ('hmac',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib.response',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('email.message',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._policybase',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('uu', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\uu.py', 'PYMODULE'),
  ('optparse',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\optparse.py',
   'PYMODULE'),
  ('decimal',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('numbers',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\numbers.py',
   'PYMODULE'),
  ('multiprocessing',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.result',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('difflib',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\difflib.py',
   'PYMODULE'),
  ('_compat_pickle',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('struct',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\struct.py',
   'PYMODULE'),
  ('random',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\random.py',
   'PYMODULE'),
  ('ctypes.util',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('socket',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\socket.py',
   'PYMODULE'),
  ('subprocess',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('shutil',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('lzma',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\bz2.py', 'PYMODULE'),
  ('pdb', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\tty.py', 'PYMODULE'),
  ('glob',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\glob.py',
   'PYMODULE'),
  ('code',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\codeop.py',
   'PYMODULE'),
  ('dis', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\dis.py', 'PYMODULE'),
  ('opcode',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\opcode.py',
   'PYMODULE'),
  ('bdb', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\cmd.py', 'PYMODULE'),
  ('threading',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('datetime',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('pprint',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\pprint.py',
   'PYMODULE'),
  ('copy',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\copy.py',
   'PYMODULE'),
  ('string',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\string.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('distutils.util',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.debug',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.file_util',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('py_compile',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('_osx_support',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.spawn',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.errors',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._typing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_typing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('sysconfig',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\sysconfig.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('imp', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\imp.py', 'PYMODULE'),
  ('posixpath',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\posixpath.py',
   'PYMODULE'),
  ('genericpath',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\genericpath.py',
   'PYMODULE'),
  ('ntpath',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\ntpath.py',
   'PYMODULE'),
  ('inspect',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\inspect.py',
   'PYMODULE'),
  ('ast', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\ast.py', 'PYMODULE'),
  ('textwrap',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('tempfile',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('email.parser',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('plistlib',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\plistlib.py',
   'PYMODULE'),
  ('platform',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\platform.py',
   'PYMODULE'),
  ('pkgutil',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('stat',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\stat.py',
   'PYMODULE'),
  ('zipimport',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipfile',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('tracemalloc',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('xlwt',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\__init__.py',
   'PYMODULE'),
  ('xlwt.Column',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\Column.py',
   'PYMODULE'),
  ('xlwt.Row',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\Row.py',
   'PYMODULE'),
  ('xlwt.compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\compat.py',
   'PYMODULE'),
  ('xlwt.Cell',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\Cell.py',
   'PYMODULE'),
  ('xlwt.Worksheet',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\Worksheet.py',
   'PYMODULE'),
  ('xlwt.Workbook',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\Workbook.py',
   'PYMODULE'),
  ('xlwt.CompoundDoc',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\CompoundDoc.py',
   'PYMODULE'),
  ('xlwt.ExcelFormula',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\ExcelFormula.py',
   'PYMODULE'),
  ('xlwt.ExcelFormulaLexer',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\ExcelFormulaLexer.py',
   'PYMODULE'),
  ('xlwt.ExcelFormulaParser',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\ExcelFormulaParser.py',
   'PYMODULE'),
  ('xlwt.ExcelMagic',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\ExcelMagic.py',
   'PYMODULE'),
  ('xlwt.UnicodeUtils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\UnicodeUtils.py',
   'PYMODULE'),
  ('xlwt.antlr',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\antlr.py',
   'PYMODULE'),
  ('xlwt.Bitmap',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\Bitmap.py',
   'PYMODULE'),
  ('xlwt.Utils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\Utils.py',
   'PYMODULE'),
  ('xlwt.Style',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\Style.py',
   'PYMODULE'),
  ('xlwt.Formatting',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\Formatting.py',
   'PYMODULE'),
  ('xlwt.BIFFRecords',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\xlwt\\BIFFRecords.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('os', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\os.py', 'PYMODULE'),
  ('PIL.Image',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('fractions',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\fractions.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('PIL',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('windnd',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\windnd\\__init__.py',
   'PYMODULE'),
  ('windnd.windnd',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\windnd\\windnd.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('requests',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('json',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('idna',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname._implementation',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('ipaddress',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.request',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('dummy_threading',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.models',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('chardet',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.enums',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('urllib3',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('pandas',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.arrays',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('distutils.version',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('pandas.core.indexes.numeric',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\numeric.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.ops.roperator',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\ops\\roperator.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util.testing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\util\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_testing.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.machar',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\machar.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.financial',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\financial.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.dual',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\dual.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.fcompiler',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\fcompiler\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.config_compiler',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\config_compiler.py',
   'PYMODULE'),
  ('numpy.distutils.command',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.egg_info',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.extern',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.extension',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.install_scripts',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools.command.easy_install',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\easy_install.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\config.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('distutils.cmd',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.package_index',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\package_index.py',
   'PYMODULE'),
  ('setuptools.ssl_support',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\ssl_support.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.sandbox',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\sandbox.py',
   'PYMODULE'),
  ('site',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\PyInstaller\\fake-modules\\site.py',
   'PYMODULE'),
  ('distutils.command.build_scripts',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('distutils.command',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.install',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\install.py',
   'PYMODULE'),
  ('distutils.command.install_scripts',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._typing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_typing.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools.namespaces',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\namespaces.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_distutils_hack',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.develop',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\develop.py',
   'PYMODULE'),
  ('setuptools.command.develop',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\develop.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_clib',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\install_clib.py',
   'PYMODULE'),
  ('numpy.distutils.command.bdist_rpm',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('distutils.command.bdist_rpm',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools.command.bdist_rpm',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('numpy.distutils.command.install',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools.command.install',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\setuptools\\command\\install.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_headers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('distutils.command.install_headers',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_data',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\install_data.py',
   'PYMODULE'),
  ('distutils.command.install_data',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\install_data.py',
   'PYMODULE'),
  ('numpy.distutils.command.sdist',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_scripts',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_clib',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('distutils.command.build_clib',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_ext',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('numpy.distutils.system_info',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\system_info.py',
   'PYMODULE'),
  ('numpy.f2py',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py_testing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\f2py_testing.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_src',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\build_src.py',
   'PYMODULE'),
  ('numpy.distutils.conv_template',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\conv_template.py',
   'PYMODULE'),
  ('numpy.distutils.from_template',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\from_template.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\fileinput.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_py',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('distutils.command.build_py',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('numpy.distutils.command.build',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('numpy.distutils.command.config',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\config.py',
   'PYMODULE'),
  ('numpy.distutils.command.autodist',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\command\\autodist.py',
   'PYMODULE'),
  ('numpy.distutils.mingw32ccompiler',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\mingw32ccompiler.py',
   'PYMODULE'),
  ('distutils.msvccompiler',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.cygwinccompiler',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('distutils.command.config',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\command\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'F:\\Program Files (x86)\\Python\\Python38\\lib\\cgi.py', 'PYMODULE'),
  ('distutils.dist',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'F:\\Program Files '
   '(x86)\\Python\\Python38\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('numpy.distutils.core',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\core.py',
   'PYMODULE'),
  ('numpy.distutils.numpy_distribution',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\numpy_distribution.py',
   'PYMODULE'),
  ('numpy.distutils.extension',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\extension.py',
   'PYMODULE'),
  ('numpy.distutils.fcompiler.environment',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\fcompiler\\environment.py',
   'PYMODULE'),
  ('numpy.distutils.exec_command',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\exec_command.py',
   'PYMODULE'),
  ('numpy.distutils.misc_util',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\misc_util.py',
   'PYMODULE'),
  ('curses',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\curses\\has_key.py',
   'PYMODULE'),
  ('numpy.distutils.__config__',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\__config__.py',
   'PYMODULE'),
  ('numpy.distutils.npy_pkg_config',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\npy_pkg_config.py',
   'PYMODULE'),
  ('numpy.distutils.unixccompiler',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('numpy.distutils.ccompiler',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('numpy.distutils._shell_utils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\_shell_utils.py',
   'PYMODULE'),
  ('pipes',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\pipes.py',
   'PYMODULE'),
  ('numpy.distutils.lib2def',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\lib2def.py',
   'PYMODULE'),
  ('numpy.distutils.log',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\distutils\\log.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.compat.chainmap',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\compat\\chainmap.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_utils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.parser',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.tz',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.easter',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil._common',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('six',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\parsers.py',
   'PYMODULE'),
  ('pandas.io.date_converters',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\date_converters.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlwt',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\excel\\_xlwt.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.api.types',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.window',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.indexers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\window\\indexers.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.plotting',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.compat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\compat.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.core.series',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('dataclasses',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.core.aggregation',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\aggregation.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\strings.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexers.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.common',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas._typing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._config.config',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pytz',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.testing',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.io.json',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.api',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.html',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.api',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.api',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._libs',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('cv2',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\cv2\\__init__.py',
   'PYMODULE'),
  ('cv2.version',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\cv2\\version.py',
   'PYMODULE'),
  ('cv2.data',
   'c:\\users\\<USER>\\.virtualenvs\\serveris-n3rmsivj\\lib\\site-packages\\cv2\\data\\__init__.py',
   'PYMODULE'),
  ('tkinter.font',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'F:\\Program Files (x86)\\Python\\Python38\\lib\\tkinter\\constants.py',
   'PYMODULE')])
