import imutils.paths
import math
import os
from tqdm import tqdm

import time
from PIL import ImageFile
from PIL import Image
ImageFile.LOAD_TRUNCATED_IMAGES = True
Image.MAX_IMAGE_PIXELS = None
import numpy as np
import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
import cv2
import logging
from GPU_Predictor_dynamic import YoloSegTRT, Colors

log=logging.getLogger("rmq_main.intestine")

class YOLOV5_SEG(YoloSegTRT):
    def __init__(self, model_file_path):
        super().__init__(model_file_path)
    def slice_detect(self, input_img,
                     conf_thres=0.1,
                     iou_thres=0.1,
                     max_shape=1280,
                     slice_pred_thed_max=4128,
                     gray_thred=0,
                     sharpne_thresh=0,
                     output_img_path="sdasd.jpg",
                     data_save_path="sdasd.csv",
                     fcs_save_path="sdasd.csv",
                     ):

        durations_in_seconds = dict()
        self.max_shape = max_shape
        print("conf_thres:", conf_thres, "iou_thres:", iou_thres, "max_shape:", self.max_shape)
        self.read_image(input_img, jiaodi_corp=True, slice_pred_thed_max=slice_pred_thed_max)
        end_det, end_segments = self.salice_instance_predict(self.jiaodi_img, conf_thres, iou_thres, max_shape,
                                                             slice_pred_thed_max, durations_in_seconds)
        print(durations_in_seconds)
        infotime = time.time()
        # 绘图
        result_img = self.get_info(self.jiaodi_img, end_det, end_segments, self.crop_bbox, output_img_path,
                                   data_save_path, fcs_save_path, gray_thred,
                                   sharpne_thresh)
        durations_in_seconds["get_info"] = time.time() - infotime
        try:
            self.jyh_img[self.crop_bbox[1]:self.crop_bbox[3],
            self.crop_bbox[0]:self.crop_bbox[2]] = result_img  # 改变原图的抠图部分
            cv2.imencode('.jpg', self.jyh_img)[1].tofile(output_img_path)
        except Exception as e:
            log.error(f"结果保存失败{e}")
        log.info(durations_in_seconds)
        return 0

    def get_info(self, img, det, segments, crop_bbox,output_img_path, data_save_path, fcs_save_path, gray_thred=0,
                 sharpne_thresh=0, fix_size=1280, line_thrkness=-1):
        analysis_result_title = ','.join(['细胞编号', '属性值', '通道', 'X坐标', 'Y坐标', '团属性', '面积',
                                        '周长', '长轴', '短轴', '圆度', '边缘锐利度', '边缘差异率', '平均灰度',
                                        '累计灰度',
                                        '最大灰度', '最小灰度', '灰度差', '背景差异', '正圆率', '亮度','轮廓']) + "\n"
        analysis_result = ""
        color_mask = np.zeros(img.shape, dtype=np.uint8)
        img_copy_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        Laplacian_img = cv2.Laplacian(img_copy_gray, cv2.CV_64F)

        if max(img.shape) > fix_size:
            scale = max(img.shape) / fix_size
            resized_img = cv2.resize(img, None, fx=1 / scale, fy=1 / scale)  # 缩小
            Laplacian_img = cv2.resize(Laplacian_img, None, fx=1 / scale, fy=1 / scale)
        else:
            resized_img = img
            scale = 1
        del img_copy_gray
        mask_save_path = output_img_path[:-4]
        if not os.path.exists(mask_save_path):
            os.makedirs(mask_save_path)
        if len(det):
            boxes, scores, class_ids = det[:, :4], det[:, 4], det[:, 5]
            color = Colors()
            #custom_color = color(random.randint(0, 255))
            custom_color = (0, 100, 0)
            for cell_num, (box, score, class_id) in enumerate(zip(boxes, scores, class_ids)):
                #start_time=time.time()
                #custom_color = color(random.randint(0, 255))
                x1, y1, x2, y2 = box.astype(int)
                # 缩小的bbox
                scale_x1, scale_y1, scale_x2, scale_y2 = list(map(int, [i / scale for i in [x1, y1, x2, y2]]))
                if scale_x1 - scale_x2 == 0 or scale_y1 - scale_y2 == 0:
                    continue
                ctype = 1
                local_channel = 0
                center_x = int((x1 + x2) / 2)
                center_y = int((y1 + y2) / 2)
                radius = int(((x2 - x1) + (y2 - y1)) / 4)
                group_type = ""  # 团属性
                major_axis_length = max((x2 - x1), (y2 - y1))
                minor_axis_length = min((x2 - x1), (y2 - y1))
                if len(segments[cell_num]) < 3:
                    continue
                bbox_x, bbox_y, bbox_w, bbox_h = cv2.boundingRect(segments[cell_num])
                area = int(cv2.contourArea(segments[cell_num] / scale))  # scale放大倍数
                mask = np.zeros(resized_img.shape[:2], dtype=np.uint8)
                mask = cv2.drawContours(mask, np.array([segments[cell_num] / scale]).astype(int), -1, 1, -1)  # 绘图
                circle_mask = self.find_cricel_mask(mask, segments[cell_num])
                mask_crop = cv2.bitwise_and(resized_img, resized_img, mask=mask.astype(np.uint8))
                if max(mask_crop.shape) <= 640:
                    mask_scale = 1
                else:
                    mask_scale = max(mask_crop.shape) / 640
                mask_crop_reize = cv2.resize(mask_crop, (640, 640))
                edge_diff = cv2.Laplacian(mask_crop_reize, cv2.CV_64F).var()*mask_scale  # 整体锐利度

                # x, y, w, h = cv2.boundingRect(segments[cell_num]/scale)
                crop_img = mask_crop[scale_y1:scale_y2, scale_x1:scale_x2]  # resize的bbox
                crop_mask_gray = cv2.cvtColor(crop_img, cv2.COLOR_BGR2GRAY)
                ret, thresh = cv2.threshold(crop_mask_gray, gray_thred, 255, cv2.THRESH_BINARY)
                pxilecount = cv2.countNonZero(thresh)
                mean, std = cv2.meanStdDev(Laplacian_img * circle_mask, mask=circle_mask)
                sharpne = std[0][0]  # 边缘锐利度 轮廓模糊度
                if sharpne <= sharpne_thresh:
                    continue
                avg_gray = np.mean(crop_mask_gray)  # 平均灰度
                sum_gray = np.sum(crop_mask_gray)  # 累计灰度
                min_gray = int(np.min(crop_mask_gray))  # 最小灰度
                max_gray = int(np.max(crop_mask_gray))  # 最大灰度
                diff_gray = pxilecount / area if area != 0 else 0  # 灰度差
                diff_gray = 1 if diff_gray > 1 else diff_gray
                background_diff = ""
                luminance = np.mean(cv2.cvtColor(crop_img, cv2.COLOR_BGR2HSV)[:, :, 2])  # 亮度
                perimeter = cv2.arcLength(segments[cell_num], True)  # 周长
                origin_area = int(cv2.contourArea(segments[cell_num])) # 原始面积
                roundness = (4 * math.pi * origin_area) / perimeter ** 2 if perimeter != 0 else 0  # 圆度
                roundness = 1 if roundness > 1 else roundness
                try:
                    centre, axes, angle = cv2.fitEllipse(segments[cell_num])  # 椭圆拟合
                    MAJ = np.argmax(axes)  # this is MAJor axis, 1 or 0
                    MIN = 1 - MAJ  # 0 or 1, minor axis
                    Eccentricity = np.sqrt(1 - (axes[MIN] / axes[MAJ]) ** 2)
                    cricle_rate = Eccentricity  # 正圆率
                except Exception as e:
                    cricle_rate = 0  # 正圆率
                del circle_mask, mask_crop, mask, crop_mask_gray, crop_img
                # 绘图
                try:
                    # img = cv2.drawContours(img,np.array([segments[cell_num]]).astype(int), -1,custom_color, 2)
                    color_mask = cv2.drawContours(color_mask, np.array([segments[cell_num]]).astype(int), -1,
                                                  custom_color, line_thrkness)  # all zeros np draw cont 浅绿
                    #生成mask
                    save_mask = np.zeros(img.shape[:2], dtype=np.uint8)
                    save_mask = cv2.drawContours(save_mask, np.array([segments[cell_num]]).astype(int), -1,1, -1)[bbox_y:bbox_y+bbox_h, bbox_x:bbox_x+bbox_w]  # all zeros np draw cont 浅绿
                    cv2.imencode('.png', save_mask)[1].tofile(os.path.join(mask_save_path,f"{cell_num+1}.png"))
                    del save_mask
                except Exception as e:
                    log.error("drawContours error",exc_info=True)

                res = [ctype, local_channel, center_x + crop_bbox[0], center_y + crop_bbox[1], group_type,
                       int(origin_area), int(perimeter),
                       major_axis_length, minor_axis_length, roundness, sharpne, edge_diff,
                       avg_gray, int(sum_gray * scale * scale), max_gray, min_gray, diff_gray, background_diff, cricle_rate,
                       luminance,f'{bbox_x+ crop_bbox[0]}|{bbox_y+ crop_bbox[1]}|{bbox_x+bbox_w+ crop_bbox[0]}|{bbox_y+bbox_h+ crop_bbox[1]}|#006400|21|0']
                analysis_result += str(cell_num + 1) + "," + ",".join(map(str, res)) + "\n"
                #print("cost_time",time.time()-start_time)
        #start_time = time.time()
        color_mask = (color_mask * 0.7).astype(np.uint8)
        #img = cv2.add(img, color_mask)  # 合并zeros np 带轮廓（mask or ellipse）
        img=cv2.add(self.jyh_img_crop, color_mask)
        del color_mask, Laplacian_img, resized_img
        #print("cost_time2", time.time() - start_time)
        end_analysis_result = analysis_result_title + analysis_result
        try:
            if os.path.exists(data_save_path):
                os.remove(data_save_path)
            with open(data_save_path, 'a+', encoding='utf-8') as f:
                f.write(end_analysis_result)
        except Exception as e:
            log.error(f"data_save_path {data_save_path} not exists", exc_info=True)
        try:
            if fcs_save_path != '':
                if os.path.exists(fcs_save_path):
                    os.remove(fcs_save_path)
                with open(fcs_save_path, 'a+', encoding='utf-8') as f:
                    f.write(end_analysis_result)
        except Exception as e:
            log.error(f"fcs_save_path {fcs_save_path} not exists", exc_info=True)
        return img

if __name__ == "__main__":
    #v5_seg_detector = YOLOV5_SEG("maskrcnn_model/xiaochang_huizong117_2048_seg.engine")
    v5_seg_detector = YOLOV5_SEG(r"maskrcnn_model/fei_mouse_AI0403/ori/best.engine")
    # ####单张图测试
    # ####单张图测试
    in_path = r"D:\B项目数据1103\Castor 小肠类器官4x 标图集 20221103\xiaochang_huizong"
    out_path = r'H:\test'
    for img_path in tqdm(imutils.paths.list_images(in_path)):
        print(img_path)
        v5_seg_detector.slice_detect(input_img=img_path,
                                     conf_thres=0.1,
                                     iou_thres=0.1,
                                     max_shape=1280,
                                     slice_pred_thed_max=4128,
                                     gray_thred=0,
                                     sharpne_thresh=0,
                                     output_img_path=os.path.join(out_path, img_path.split(os.sep)[-1]),
                                     data_save_path=os.path.join(out_path, img_path.split(os.sep)[-1][:-4] + ".csv"),
                                     fcs_save_path=os.path.join(out_path, img_path.split(os.sep)[-1][:-4] + ".csv"))
        break
    # break

    # ####多图测试
    # image_dir = '/home/<USER>/PycharmProjects/xiaochang/1208'
    # files = os.listdir(image_dir)
    # for file in tqdm(files):
    #     in_path = f'{image_dir}/{file}'
    #     print(f'--------------------{in_path}--------------------------')
    #     out_path = '/home/<USER>/PycharmProjects/xiaochang/1208_result3/7'
    #     os.makedirs(out_path, exist_ok=True)
    #     v5_seg_detector.slice_detect(in_path, output_img_path=out_path, perform_full_pred=True,
    #                                  slice_pred_thed_max=4000)
