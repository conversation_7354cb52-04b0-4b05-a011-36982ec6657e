import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2,50))
from multiprocessing import freeze_support
import pefile
from websocket import WebSocketApp
import json
from loguru import logger
import threading
import time
os.environ['PATH'] = os.environ['PATH'] + f';{os.path.abspath(os.getcwd())}' #一定要放在导入cuda,tensorrt前
from transfection_infer_cupy import TransfectionInfer as InferEngine
import yaml
import traceback
def set_log(app_name):
    # logger.add(f"{app_name}_Run_LOG.txt", rotation="10 MB",
    # retention="10 days", encoding="utf-8"   # 保留最近10天的日志文件
    # )
    logger.add(f"{app_name}_Run_LOG.txt",filter=lambda record: "Keep" not in record["message"], rotation="20 MB",
               retention="10 days", encoding="utf-8"   # 保留最近10天的日志文件
               )
    logger.add(f"{app_name}_heartbeat.txt",filter=lambda record: "Keep" in record["message"], rotation="50 MB",
        retention = "10 days", encoding="utf-8"   # 保留最近10天的日志文件
        )
    return logger

class Predictor(object):
    def __init__(self, app_name="App_name"):
        self.APP_name = app_name
        self.config = None
        self.predicter = None
        self.log=set_log(self.APP_name)
        self.APP_EXE = f"mq_main_{self.APP_name}.exe"
        self.root_url = 'ws://localhost:12318/ws'  # 这里输入websocket的url
        self.config_yaml = f"{self.APP_name}_APP/config.yaml"
        self.get_version(self.APP_EXE)
        self.ws = None
        self.isConnected = False
        self.isStarted = False
        self.timeOut = 10
        self.wslock = threading.RLock()
        self.msgId = 0
        self.msgList = {}
        self.current_tid_cost_time = None
        self.isBusy = False

    def get_version(self, path):
        def loword_opera(dword):
            return dword & 0x0000ffff

        def hiword_opera(dword):
            return dword >> 16

        try:
            pe = pefile.PE(path)
            ms = pe.VS_FIXEDFILEINFO[0].ProductVersionMS
            ls = pe.VS_FIXEDFILEINFO[0].ProductVersionLS
            version = f'{hiword_opera(ms)}.{loword_opera(ms)}.{hiword_opera(ls)}.{loword_opera(ls)}'
            self.log.info(f"{self.APP_name} AI算法版本:{version}")
        except Exception as e:
            version = f"获取 {self.APP_name} AI算法版本信息出错:{e}"
            self.log.error(f"获取{self.APP_name} AI算法版本信息出错:{e}")
        return version
    def load_config(self):
        try:
            if self.config is None:
                with open(self.config_yaml, encoding='utf-8') as f:
                    self.config = yaml.load(f, Loader=yaml.FullLoader)
                self.log.info(f"load {self.config_yaml} success\n {self.config}")
        except Exception as e:
            error_message = traceback.format_exc()
            self.log.error(f"load config yaml fail {error_message}")

    def load_model(self):
        try:
            if self.predicter is None:
                self.predicter = InferEngine(bf_model_path=self.config[f"{self.APP_name}"]["bf_engine"],
                                             fl_model_path=self.config[f"{self.APP_name}"]["fl_engine"])
                self.predicter.root_url = self.root_url
                self.log.info(f"load model success")
        except Exception as e:
            error_message = traceback.format_exc()
            self.log.error(f"load model fail {error_message}")

    def monitor_model(self, msg_id,task_id, cut_info):
        if self.predicter is None:
            self.load_config()
            self.load_model()
        self.predicter.cut_info = cut_info
        self.predicter.task_id = task_id
        self.predicter.msg_id = msg_id
        self.predicter.ws = self.ws
        self.predicter.send_response=self.send_response
        self.current_tid_cost_time = {task_id: 600}


    def start_msg(self, msg):
        exc_start_time = time.time()
        message = msg.get("Data")
        msg_id = msg.get("ID")
        task_id = message.get("tid")
        status = "success"
        return_msg = ""
        predictors = self.predicter
        log=self.log
        config = self.config
        try:
            exp_type = message["exp_type"]
            try:
                cut_info = message["cut_info"]
            except:
                cut_info = None
                log.info(f"received message not found cut_info,use default value {cut_info}")
            self.monitor_model(msg_id=msg_id,task_id=task_id, cut_info=cut_info)
            if exp_type == "version":
                return_msg = self.get_version(self.APP_EXE)
            elif exp_type == self.APP_name:
                log.info(f"{exp_type}-{task_id}")
                bf_image_path = message["BF_input_path"]
                try:
                    bf_save_image_path = message["BF_save_path"]
                except Exception as e:
                    bf_save_image_path = None
                    log.info(f"BF_input_path not found,use default config value None {e}")
                try:
                    bf_labelme_path = message["BF_labelme_path"]
                except Exception as e:
                    bf_labelme_path = ""
                    log.info(f"BF_labelme_path not found,use default config value ''")
                try:
                    BF_mask_path = message["BF_mask_path"]
                except Exception as e:
                    BF_mask_path = None
                    log.info(f"BF_mask_path not found,use default config value None {e}")
                fl1_image_path = message["FL1_input_path"]
                try:
                    fl1_save_image_path = message["FL1_save_path"]
                except Exception as e:
                    fl1_save_image_path = None
                    log.info(f"FL1_input_path not found,use default config value None {e}")
                try:
                    fl1_labelme_path = message["FL1_labelme_path"]
                except Exception as e:
                    fl1_labelme_path = ""
                    log.info(f"FL1_labelme_path not found,use default config value ''")
                try:
                    FL1_mask_path = message["FL1_mask_path"]
                except Exception as e:
                    FL1_mask_path = None
                    log.info(f"FL1_mask_path not found,use default config value None {e}")
                fl2_image_path = message["FL2_input_path"]
                try:
                    fl2_labelme_path = message["FL2_labelme_path"]
                except Exception as e:
                    fl2_labelme_path = ""
                    log.info(f"FL2_labelme_path not found,use default config value ''")
                try:
                    fl2_save_image_path = message["FL2_save_path"]
                except Exception as e:
                    fl2_save_image_path = None
                    log.info(f"FL2_input_path not found,use default config value None {e}")
                try:
                    FL2_mask_path = message["FL2_mask_path"]
                except Exception as e:
                    FL2_mask_path = None
                    log.info(f"FL2_mask_path not found,use default config value None {e}")
                try:
                    fl3_image_path = message["FL3_input_path"]
                except Exception as e:
                    fl3_image_path = None
                    log.info(f"FL3_input_path not found,use default config value None {e}")
                try:
                    fl3_labelme_path = message["FL3_labelme_path"]
                except Exception as e:
                    fl3_labelme_path = ""
                    log.info(f"FL3_labelme_path not found,use default config value ''")
                try:
                    fl3_save_image_path = message["FL3_save_path"]
                except Exception as e:
                    fl3_save_image_path = None
                    log.info(f"FL3_input_path not found,use default config value None {e}")
                try:
                    FL3_mask_path = message["FL3_mask_path"]
                except Exception as e:
                    FL3_mask_path = None
                    log.info(f"FL3_mask_path not found,use default config value None {e}")
                try:
                    fl4_image_path = message["FL4_input_path"]
                except Exception as e:
                    fl4_image_path = None
                    log.info(f"FL4_input_path not found,use default config value None {e}")
                try:
                    fl4_labelme_path = message["FL4_labelme_path"]
                except Exception as e:
                    fl4_labelme_path = ""
                    log.info(f"FL4_labelme_path not found,use default config value ''")
                try:
                    fl4_save_image_path = message["FL4_save_path"]
                except Exception as e:
                    fl4_save_image_path = None
                    log.info(f"FL4_input_path not found,use default config value None {e}")
                try:
                    FL4_mask_path = message["FL4_mask_path"]
                except Exception as e:
                    FL4_mask_path = None
                    log.info(f"FL4_mask_path not found,use default config value None {e}")
                merge_save_path = message["merge_save_path"]
                data_save_path = message["data_save_path"]
                try:
                    fcs_save_path = message["fcs_save_path"]
                except Exception as e:
                    fcs_save_path = ""
                    log.info(f"fcs_save_path not found,use default config value {fcs_save_path}")
                try:
                    BF_cut_patch = int(message["BF_cut_patch"])
                except Exception as e:
                    BF_cut_patch = config[f"{self.APP_name}"]["cut_patch_number"]
                    log.info(f"BF_cut_patch not found,use default value {BF_cut_patch}")
                try:
                    FL1_cut_patch = int(message["FL1_cut_patch"])
                except Exception as e:
                    FL1_cut_patch = config[f"{self.APP_name}"]["cut_patch_number"]
                    log.info(f"FL1_cut_patch not found,use default value {FL1_cut_patch}")
                try:
                    FL2_cut_patch = int(message["FL2_cut_patch"])
                except Exception as e:
                    FL2_cut_patch = config[f"{self.APP_name}"]["cut_patch_number"]
                    log.info(f"FL2_cut_patch not found,use default value {FL2_cut_patch}")
                try:
                    FL3_cut_patch = int(message["FL3_cut_patch"])
                except Exception as e:
                    FL3_cut_patch = config[f"{self.APP_name}"]["cut_patch_number"]
                    log.info(f"FL3_cut_patch not found,use default value {FL3_cut_patch}")
                try:
                    FL4_cut_patch = int(message["FL4_cut_patch"])
                except Exception as e:
                    FL4_cut_patch = config[f"{self.APP_name}"]["cut_patch_number"]
                    log.info(f"FL4_cut_patch not found,use default value {FL4_cut_patch}")
                try:
                    FL1_HSV_tred = int(message["FL1_HSV_tred"])
                except Exception as e:
                    FL1_HSV_tred = config[f"{self.APP_name}"]["HSV_tred"]
                    log.info(f"FL1_HSV_tred not found,use default value {FL1_HSV_tred}")
                try:
                    FL2_HSV_tred = int(message["FL2_HSV_tred"])
                except Exception as e:
                    FL2_HSV_tred = config[f"{self.APP_name}"]["HSV_tred"]
                    log.info(f"FL2_HSV_tred not found,use default value {FL2_HSV_tred}")
                try:
                    FL3_HSV_tred = int(message["FL3_HSV_tred"])
                except Exception as e:
                    FL3_HSV_tred = config[f"{self.APP_name}"]["HSV_tred"]
                    log.info(f"FL3_HSV_tred not found,use default value {FL3_HSV_tred}")
                try:
                    FL4_HSV_tred = int(message["FL4_HSV_tred"])
                except Exception as e:
                    FL4_HSV_tred = config[f"{self.APP_name}"]["HSV_tred"]
                    log.info(f"FL4_HSV_tred not found,use default value {FL4_HSV_tred}")
                try:
                    computer_mode = message["computer_mode"]
                    if computer_mode not in [1, 2]:
                        computer_mode = config[f"{self.APP_name}"]["computer_mode"]
                        log.info(f"computer_mode not Corret,use default value {computer_mode}")
                except Exception as e:
                    computer_mode = config[f"{self.APP_name}"]["computer_mode"]
                    log.info(f"computer_mode not found,use default value {computer_mode}")
                try:
                    custom_color = [[0, 0, 0, 128, 128, 0], [0, 0, 0, 0, 255, 0], [0, 0, 0, 0, 0, 255],
                                    [0, 0, 0, 255, 0, 0], [0, 0, 0, 0, 0, 255]]
                except Exception as e:
                    custom_color = None
                    log.info(f"custom_color not found,use defule {custom_color}")
                try:
                    predictors.image_predict(
                        input_image_path=[bf_image_path, fl1_image_path, fl2_image_path, fl3_image_path, fl4_image_path],
                        image_save_path=[bf_save_image_path, fl1_save_image_path, fl2_save_image_path, fl3_save_image_path,
                         fl4_save_image_path, merge_save_path],
                        labelme_path=[bf_labelme_path, fl1_labelme_path, fl2_labelme_path, fl3_labelme_path, fl4_labelme_path],
                        mask_save_path=[BF_mask_path, FL1_mask_path, FL2_mask_path, FL3_mask_path, FL4_mask_path],
                        data_save_path=data_save_path, fcs_save_path=fcs_save_path,
                        cut_patch=[BF_cut_patch, FL1_cut_patch, FL2_cut_patch, FL3_cut_patch, FL4_cut_patch],
                        hsv_tred=[FL1_HSV_tred, FL2_HSV_tred, FL3_HSV_tred, FL4_HSV_tred],
                        computer_mode=computer_mode, custom_color=custom_color)
                except:
                    error_message = traceback.format_exc()
                    return_msg = str(error_message)
                    status = "fail"
                    log.error(f"[{self.APP_name} error] - 任务ID: {task_id}: {error_message}")
            else:
                status = "fail"
                return_msg = f"实验类型 {exp_type} 不存在,本实验应用为 {self.APP_name} 请检查是否匹配"
        except:
            error_message = traceback.format_exc()
            return_msg = str(error_message)
            status = "fail"
            log.error(f"[{self.APP_name} error] - 任务ID: {task_id}: {error_message}")
        elapsed_time = time.time() - exc_start_time
        log.info(f"[{self.APP_name}] - 任务ID: {task_id}-算法执行总耗时: {int(elapsed_time // 60)} 分 {int(elapsed_time % 60)} 秒")
        self.send_response("MsgDone", msg_id, {"tid": task_id, "status": status, "msg": return_msg})

    def on_open(self, ws):
        self.log.info(f'与 {self.root_url} WS连接建立!')
        self.isConnected = True
        # 登录（注册）
        self.send_msg("SignIn", {"AppName":"AICounter", "Service": f"AICounter.{self.APP_name}", "KeepAlive": True})

    def on_message(self, ws, message):
        # 对收到的message进行解析
        print(f"Receive:{message}!")
        rmsg = json.loads(message)
        msgtype = rmsg.get('Type')
        data = rmsg.get('Data')

        # 这是登录消息的回应,服务端会带一个服务端设定的超时时间TimeOut,小于这个间隔发心跳包,下面的代码中用的是 TimeOut/2
        if msgtype == "SignIn":
            self.log.info(f"Receive:{message}!")
            timeOut = data.get('TimeOut')
            self.timeOut = timeOut
            if self.isStarted == False:
                self.isStarted = True
                # 登录完成开始发心跳包
                self.start_keep()
                self.load_config()
                self.load_model()


        # 新的任务消息
        elif msgtype == "NewMsg":
            self.log.info(f"Receive:{message}!")
            if self.isBusy:
                self.deny_msg(rmsg)
            else:
                self.on_msg(rmsg)

        # 对消息开始的回应
        elif msgtype == "MsgStartResponse":
            self.log.info(f"Receive:{message}!")
            self.on_msg_start(rmsg)

        # 对消息时间设置的回应
        elif msgtype == "MsgTimeResponse":
            self.log.info(f"Receive:{message}!")
            self.on_msg_time(rmsg)

        # 对消息完成的回应
        elif msgtype == "MsgDoneResponse":
            self.log.info(f"Receive:{message}!")
            self.on_msg_done(rmsg)

    def on_error(self, ws, error):
        self.log.error(f"WebSocket connection error: {error}")
        self.ws_error(ws)

    def on_close(self, ws, close_status_code, close_msg):
        self.log.info(f'关闭连接! 状态码: {close_status_code}, 消息: {close_msg}')
        self.isConnected = False
        self.close(ws)
        time.sleep(3)
        self.start()

    def start(self):
        self.log.info(f"尝试连接到 {self.root_url}...")
        ws = WebSocketApp(
            self.root_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
        )
        self.ws = ws
        self.ws.run_forever()

    def close(self, ws):
        self.wslock.acquire()
        if ws is not None:
            try:
                ws.close(status=1000, reason="Normal closure")  # 显式关闭连接
            except Exception as e:
                self.log.error(f"显式关闭连接时出错: {e}")
            ws.on_open = None
            ws.on_message = None
            ws.on_error = None
            ws.on_close = None
            self.log.info("连接注销")
        self.wslock.release()

    def ws_error(self, ws):
        if ws == self.ws:
            self.isConnected = False
            self.ws = None
            self.start()
        self.close(ws)

    def send_msg(self, type, data):
        self.msgId += 1
        msg = {"ID": f"{self.msgId}", "Type": type, "Data": data}
        msgLine = json.dumps(msg)
        self.ws.send(msgLine)
        self.log.info(f"Send:{msgLine}")

    def send_response(self, type, id, data):
        msg = {"ID": f"{id}", "Type": type, "Data": data}
        if type=="MsgTime":
            self.current_tid_cost_time=data
        msgLine = json.dumps(msg)
        self.ws.send(msgLine)
        self.log.info(f"Send:{msgLine}")

    def start_keep(self):
        keepThread = threading.Thread(target=self.sendKeep, args=())
        keepThread.start()

    def sendKeep(self):
        while self.isStarted:
            time.sleep(float(self.timeOut / 2))
            self.wslock.acquire()
            try:
                if self.ws is not None and self.isConnected:
                    self.send_msg("Keep", {})
            except Exception as e:
                self.log.error(f"发送keep时出错: {e}")
                if self.isConnected:
                    self.ws_error(self.ws)
            finally:
                self.wslock.release()

    def on_msg(self, msg):
        data = msg.get("Data")
        msg_id = msg.get("ID")
        tid = data.get("tid")
        self.msgList[msg_id] = msg
        # 01 收到消息后,回复开始处理
        self.send_response("MsgStart", msg_id, {"tid": tid})
        self.isBusy = True
    def deny_msg(self, msg):
        data = msg.get("Data")
        msg_id = msg.get("ID")
        tid = data.get("tid")
        self.msgList[msg_id] = msg
        # 01 收到消息后,回复开始处理
        self.send_response("MsgDeny", msg_id, {"tid": tid,"reson":"已有有任务在处理",
                                               "items":[self.current_tid_cost_time]})
        self.isBusy = False

    def on_msg_start(self, resp):
        self.isBusy = True
        try:
            msg_id = resp.get("ID")
            msg = self.msgList[msg_id]
            if msg is None:
                self.log.error(f"收到的消息没有对应{msg_id}")
            else:
                ok = resp.get("Data").get("Result")
                if ok:
                    # 开始处理消息
                    self.start_msg(msg)
                else:
                    # 如果服务端返回为false,则不能处理消息
                    del self.msgList[msg_id]
                    self.log.info(f"消息处理取消,服务端返回false[{msg_id}]")
        except Exception as e:
            self.log.error(f"on_msg_start 处理消息时出错:{e}")
        finally:
            self.isBusy = False



    def on_msg_time(self, resp):
        msg_id = resp.get("ID")
        msg = self.msgList[msg_id]
        if msg is None:
            self.log.error(f"收到的消息没有对应{msg_id}")
        else:
            ok = resp.get("Data").get("Result")
            if ok:
                self.log.info(f"设置消息时间成功:[{msg_id}]")
            else:
                self.log.error(f"设置消息时间失败:[{msg_id}]")

    def on_msg_done(self, resp):
        msg_id = resp.get("ID")
        msg = self.msgList[msg_id]
        if msg is None:
            self.log.error(f"收到的消息没有对应{msg_id}")
        else:
            ok = resp.get("Data").get("Result")
            self.log.info(f"收到消息完成回应:[{msg_id}]-{ok}")
            del self.msgList[msg_id]


if __name__ == '__main__':
    freeze_support()
    APP_name = "transfection"
    Predictor(APP_name).start()


# pyinstaller -D mq_main_singleclone.spec --noconfirm
