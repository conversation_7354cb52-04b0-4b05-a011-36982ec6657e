import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2,50))
from multiprocessing import freeze_support
import pefile
from websocket import WebSocketApp
import json
from loguru import logger
import threading
from threading import Lock
from concurrent.futures import ThreadPoolExecutor # 导入
import time
os.environ['PATH'] = os.environ['PATH'] + f';{os.path.abspath(os.getcwd())}' #一定要放在导入cuda,tensorrt前
from common_polyclone_bg_infer import Yolov5Seg as InferEngine
import yaml
import traceback
def set_log(app_name):
    # logger.add(f"{app_name}_Run_LOG.txt", rotation="10 MB",
    # retention="10 days", encoding="utf-8"   # 保留最近10天的日志文件
    # )
    logger.add(f"{app_name}_Run_LOG.txt",filter=lambda record: "Keep" not in record["message"], rotation="100 MB",
               retention="10 days", encoding="utf-8"   # 保留最近10天的日志文件
               )
    logger.add(f"{app_name}_heartbeat.txt",filter=lambda record: "Keep" in record["message"], rotation="150 MB",
        retention = "10 days", encoding="utf-8"   # 保留最近10天的日志文件
        )
    return logger

class Predictor(object):
    def __init__(self, app_name="App_name"):
        self.APP_name = app_name
        self.config = None
        self.predicter = None
        self.log=set_log(self.APP_name)
        self.APP_EXE = f"mq_main_{self.APP_name}.exe"
        self.root_url = 'ws://localhost:12318/ws'  # 这里输入websocket的url
        self.config_yaml = f"{self.APP_name}_APP/config.yaml"
        self.get_version(self.APP_EXE)
        self.ws = None
        self.isConnected = False
        self.isStarted = False
        self.timeOut = 10
        self.lock = Lock()
        self.ws_send_lock = Lock()
        self.msgId = 0
        self.msgList = {}
        self.processing_msg_ids = set()  # 追踪已经提交到线程池或正在等待服务器同意处理的 msg_id
        self.active_tasks = 0 # 当前正在处理的任务数
        self.max_concurrent_tasks = 1
        self.task_executor = None # 在 load_config 中初始化
        self.log.info(f"Predictor initialized.")

    def get_version(self, path):
        def loword_opera(dword):
            return dword & 0x0000ffff

        def hiword_opera(dword):
            return dword >> 16

        try:
            pe = pefile.PE(path)
            ms = pe.VS_FIXEDFILEINFO[0].ProductVersionMS
            ls = pe.VS_FIXEDFILEINFO[0].ProductVersionLS
            version = f'{hiword_opera(ms)}.{loword_opera(ms)}.{hiword_opera(ls)}.{loword_opera(ls)}'
            self.log.info(f"{self.APP_name} AI算法版本:{version}")
        except Exception as e:
            version = f"获取 {self.APP_name} AI算法版本信息出错:{e}"
            self.log.error(f"获取{self.APP_name} AI算法版本信息出错:{e}")
        return version
    
    def load_config(self):
        try:
            if self.config is None:
                with open(self.config_yaml, encoding='utf-8') as f:
                    self.config = yaml.load(f, Loader=yaml.FullLoader)
                self.log.info(f"load {self.config_yaml} success\n {self.config}")
            try:
                # 使用 get 方法并提供默认值
                new_max_workers = self.config.get(self.APP_name, {}).get("max_workers", self.max_concurrent_tasks)
                # 只有在线程池尚未初始化或最大工作线程数变化时才重新创建
                if self.task_executor is None or self.task_executor._max_workers != new_max_workers:
                    self.max_concurrent_tasks = new_max_workers
                    # 如果旧的线程池存在，先关闭
                    if self.task_executor:
                        self.log.info("Shutting down old task executor before creating new one...")
                        # 这里可以选择 wait=True 来等待当前任务完成，或者 wait=False 立即关闭（可能中断任务）
                        # 考虑到配置更新可能不频繁，wait=True 更安全，但可能会阻塞 config 加载
                        # 简单起见，先用 wait=False (可能中断)，如果需要更平滑的过渡，需要更复杂的逻辑
                        try:
                            self.task_executor.shutdown(wait=False, cancel_futures=True)
                            self.log.info("Old task executor shut down.")
                        except:
                            self.log.error("Error shutting down old task executor",exc_info=True)
                    if self.max_concurrent_tasks > 1:
                        # 创建新的线程池
                        self.task_executor = ThreadPoolExecutor(max_workers=self.max_concurrent_tasks,thread_name_prefix='PredictWorker')
                        self.log.info(f"{self.APP_name} task executor created/updated with max_workers: {self.max_concurrent_tasks}")
                    else:
                        self.log.info(f"{self.APP_name} configured for single-threaded execution. Task executor will not be used.")
                else:
                     self.log.info(f"Max workers unchanged ({self.max_concurrent_tasks}), keeping existing task executor.")
            except Exception as e:
                self.log.error(
                    f"Error setting max_workers from config: {e}. Using default value {self.max_concurrent_tasks}. Task executor might not be initialized correctly.")
        except FileNotFoundError:
            self.log.error(f"Config file not found: {self.config_yaml}")
        except:
            self.log.error("load config yaml fail",exc_info=True)
            
    def load_model(self):
        try:
            if self.predicter is None:
                self.log.info("Loading model...")
                # 确保 config 已经被加载
                if self.config is None:
                    self.load_config()
                self.predicter = InferEngine(model_file_path=self.config.get(self.APP_name, {}).get("engine"))
                self.predicter.root_url = self.root_url
                self.predicter.ws = self.ws
                self.predicter.send_response = self.send_response
                self.predicter.max_concurrent_tasks=self.max_concurrent_tasks
                self.log.info(f"load model success")
        except:
            self.log.error(f"load model fail",exc_info=True)

   
    def start_msg(self, msg):
        exc_start_time = time.time()
        message = msg.get("Data")
        msg_id = msg.get("ID",None)
        task_id = message.get("tid",None)
        status = "success"
        return_msg = ""  # 初始化返回消息
        predictors = self.predicter
        log=self.log
        config = self.config
        def get_param(param_name, default_value,  log_info=True):
            try:
                return message[param_name]
            except KeyError:
                if log_info:
                    log.info(f"[{self.APP_name}] - Task {task_id}: '{param_name}' not found in message, using default config value '{default_value}'.")
                return default_value
            except Exception as e:
                log.error(f"[{self.APP_name} error] - Task {task_id}: Error getting parameter '{param_name}': {e}")
            return None
        # --- 检查模型是否加载 ---
        if self.predicter is None:
             log.error(f"[{self.APP_name} error] - Task {task_id}: Predictor not loaded. Cannot process.")
             status = "fail"
             return_msg = "Internal error: Predictor not loaded."
             self.send_response("MsgDone", msg_id, {"tid": task_id, "status": status, "msg": return_msg})
             return # 提前退出
        try:
            exp_type = message.get("exp_type",None)
            if exp_type is None:
                status = "fail"
                return_msg = f"[{self.APP_name} error] - Task {task_id}: 'exp_type' field missing in message data."
                log.error(return_msg)
                self.send_response("MsgDone", msg_id, {"tid": task_id, "status": status, "msg": return_msg})
                return
            if exp_type == "version":
                return_msg = self.get_version(self.APP_EXE)
            elif exp_type == self.APP_name:
                log.info(f"{exp_type}-{task_id}")
                cut_info = get_param("cut_info", None)
                input_image_path = get_param("input_image_path",None)
                input_labelme_path = get_param("input_labelme_path", "")
                image_save_path = get_param("image_save_path", None)
                mask_save_path = get_param("mask_save_path", None)
                data_save_path = get_param("data_save_path",None)
                fcs_save_path = get_param("fcs_save_path", None)
                conf_thrd = get_param("conf_thrd",self.config.get(self.APP_name, {}).get("conf_thrd", 0.1))
                iou_thrd = get_param("iou_thrd",self.config.get(self.APP_name, {}).get("iou_thrd", 0.1))
                max_shape = config.get(self.APP_name, {}).get("input_size", 960)
                try:
                    predictors.detect(input_image_path, image_save_path,
                                            data_save_path, fcs_save_path,
                                            conf_threshold=conf_thrd,
                                            iou_threshold=iou_thrd, max_shape=max_shape,
                                            mask_save_path=mask_save_path,
                                            input_labelme_path=input_labelme_path,
                        cut_info=cut_info,
                        msg_id=msg_id,
                        task_id=task_id)
                except:
                    error_message = traceback.format_exc()
                    return_msg = str(error_message)
                    status = "fail"
                    log.error(f"[{self.APP_name} error] - 任务ID: {task_id}: {error_message}")
            else:
                status = "fail"
                return_msg = f"实验类型 {exp_type} 不存在,本实验应用为 {self.APP_name} 请检查是否匹配"
        except:
            error_message = traceback.format_exc()
            return_msg = str(error_message)
            status = "fail"
            log.error(f"[{self.APP_name} error] - 任务ID: {task_id}: {error_message}")
        elapsed_time = time.time() - exc_start_time
        log.info(f"[{self.APP_name}] - Task ID: {task_id} (ID:{msg_id}) - 算法执行总耗时: {int(elapsed_time // 60)} 分 {int(elapsed_time % 60)} 秒. Status: {status}")
        self.send_response("MsgDone", msg_id, {"tid": task_id, "status": status, "msg": return_msg})


        
    def on_open(self, ws):
        self.log.info(f'与 {self.root_url} WS连接建立!')
        self.isConnected = True
        # 登录（注册）
        self.send_msg("SignIn", {"AppName":"AICounter", "Service": f"AICounter.{self.APP_name}", "KeepAlive": True})

    def on_message(self, ws, message):
        # 对收到的message进行解析
        print(f"Receive:{message}!")
        rmsg = json.loads(message)
        msgtype = rmsg.get('Type')
        data = rmsg.get('Data')
        msg_id = rmsg.get("ID",None)  # 获取ID，用于日志
        # 这是登录消息的回应,服务端会带一个服务端设定的超时时间TimeOut,小于这个间隔发心跳包,下面的代码中用的是 TimeOut/2
        if msgtype == "SignIn":
            self.log.info(f"Receive SignIn (ID:{msg_id}): {message}!")
            timeOut = data.get('TimeOut')
            self.timeOut = timeOut
            if not self.isStarted:
                self.isStarted = True
                # 登录完成开始发心跳包
                self.start_keep()
                if self.predicter is None:
                    self.load_config()
                    self.load_model()
                    
        # 新的任务消息
        elif msgtype == "NewMsg":
            self.log.info(f"Receive NewMsg (ID:{msg_id}): {message}!")
            tid = data.get("tid", None)
            with self.lock: # 检查并发数需要加锁
                if msg_id in self.processing_msg_ids:
                    # 已经收到并正在处理或等待处理这个 msg_id，直接忽略
                    self.log.warning(f"Ignoring duplicate NewMsg (ID:{msg_id}). Already in processing state.")
                    # 可以选择回复 MsgDeny，通知服务器这是重复消息
                    self.deny_msg(rmsg, reason="Duplicate message ID received")
                    return  # 忽略该重复消息
                # 检查当前活跃（已提交到线程池）的任务数是否达到上限
                if self.active_tasks < self.max_concurrent_tasks:
                    # 接受任务，将其加入等待处理/已提交的集合
                    self.msgList[msg_id] = rmsg  # 将消息存储到 msgList
                    self.processing_msg_ids.add(msg_id)  # 标记为正在处理或等待服务器同意
                    self.log.info(f"Task {tid} accepted. Active tasks: {self.active_tasks}/{self.max_concurrent_tasks}")
                    self.send_response("MsgStart", msg_id, {"tid": tid})
                else:
                    # 任务限制达到，拒绝
                    self.log.warning(f"Task limit reached ({self.active_tasks}/{self.max_concurrent_tasks}). Denying task {tid} (msg_id {msg_id}).")
                    self.deny_msg(rmsg, reason="Task limit reached")

        # 对消息开始的回应
        elif msgtype == "MsgStartResponse":
            self.log.info(f"Receive MsgStartResponse (ID:{msg_id}): {message}!")
            self.on_msg_start(rmsg)

        # 对消息时间设置的回应
        elif msgtype == "MsgTimeResponse":
            self.log.info(f"Receive MsgTimeResponse (ID:{msg_id}): {message}!")
            self.on_msg_time(rmsg)

        # 对消息完成的回应
        elif msgtype == "MsgDoneResponse":
            self.log.info(f"Receive MsgDoneResponse (ID:{msg_id}): {message}!")
            self.on_msg_done(rmsg)
        # 其他类型的消息可以忽略或根据需要处理
        else:
            pass
            #self.log.warning(f"Received unknown message type (ID:{msg_id}): {msgtype}")

    def on_error(self, ws, error):
        self.log.error(f"WebSocket connection error", exception=error)
        # 考虑添加重连逻辑或更健壮的错误处理
        self.isConnected = False # 标记连接断开


    def on_close(self, ws, close_status_code, close_msg):
        self.log.info(f'关闭连接! 状态码: {close_status_code}, 消息: {close_msg}')
        self.isConnected = False
        self.isStarted = False # 需要重新 SignIn
        self.log.info("Attempting to reconnect in 3 seconds...")
        time.sleep(3)
        self.start()

    def start(self):
        # 确保在重连前清理旧的 ws 对象
        if self.ws:
             try:
                 self.ws.close() # 尝试优雅关闭旧连接
             except Exception as e:
                 self.log.warning(f"Error closing old websocket during restart: {e}")
        self.ws = None # 清理旧的 ws 对象
        self.log.info(f"尝试连接到 {self.root_url}...")
        ws = WebSocketApp(
            self.root_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
        )
        self.ws = ws
        # run_forever 会阻塞，直到连接关闭或出错
        try:
            self.ws.run_forever()
        except Exception as e:
             self.log.error(f"WebSocket run_forever failed: {e}. Attempting restart...")
             self.isConnected = False
             self.isStarted = False
             time.sleep(3)
             self.start() # 重启连接

    def send_msg(self, type, data):
        with self.lock: # msgId 自增需要保护
            self.msgId += 1
            msg_id = self.msgId # 本次操作的ID
        msg = {"ID": f"{msg_id}", "Type": type, "Data": data}
        msgLine = json.dumps(msg)
        if self.ws and self.isConnected:
            with self.ws_send_lock: # 使用锁保护 send() 调用
                self.ws.send(msgLine)
                self.log.info(f"Send (ID:{msg_id}): {msgLine}")

    def send_response(self, type, id, data):
        msg = {"ID": f"{id}", "Type": type, "Data": data}
        msgLine = json.dumps(msg)
        if self.ws and self.isConnected:
            try:
                with self.ws_send_lock:  # 使用锁保护 send() 调用
                    self.ws.send(msgLine)
                    self.log.info(f"Send Response (ID:{id}): {msgLine}")
            except Exception as e:
                 self.log.error(f"Send Response failed (ID:{id}): {e}")
                 self.isConnected = False
        else:
             self.log.warning(f"Send Response failed (ID:{id}): WebSocket not connected or ws object is None.")

    def start_keep(self):
        # 检查是否已经有 KeepAliveThread 在运行
        for t in threading.enumerate():
            if t.name == "KeepAliveThread" and t.is_alive():
                 self.log.info("KeepAliveThread already running.")
                 return # 如果已经在运行，则不启动新的线程
        keepThread = threading.Thread(target=self.sendKeep, args=(), name="KeepAliveThread", daemon=True)
        keepThread.start()
        self.log.info("KeepAliveThread started.")

    def sendKeep(self):
        while True: # 使用 True 循环，依赖 isStarted 控制退出
            if not self.isStarted or not self.isConnected:
                 self.log.info("Stopping KeepAlive thread.")
                 break # 如果未启动或连接断开，退出循环
            # 使用 timeOut 的一半或稍小的值
            sleep_duration = max(1.0, self.timeOut / 2.0 - 1.0) # 至少睡1秒
            time.sleep(sleep_duration)
            if self.isConnected: # 再次检查连接状态，避免在 sleep 后连接断开时发送
                 try:
                     self.send_msg("Keep", {}) # 发送心跳
                 except Exception as e:
                     self.log.error(f"Error sending KeepAlive: {e}")
                     # 发送失败 send_msg 内部会设置 isConnected = False，循环会退出


    def deny_msg(self, msg, reason=""):
        # 收到 NewMsg 时拒绝任务
        data = msg.get("Data")
        msg_id = msg.get("ID",None)
        tid = data.get("tid",None)
        # 从 processing_msg_ids 中移除，因为它没有被服务器接受或我们主动拒绝了
        with self.lock:
            if msg_id in self.processing_msg_ids: 
                self.processing_msg_ids.discard(msg_id) # 从处理集合中移除
                # 在 NewMsg 阶段没有增加 active_tasks，所以这里也不需要减少
                self.log.info(f"Task {tid} (ID:{msg_id}) denied locally. Reason: {reason}. Messages pending/processing: {len(self.processing_msg_ids)}")
                # 移除 msgList 中的消息（如果存在）
                if msg_id in self.msgList:
                     del self.msgList[msg_id]
            else:
                 # 这可能是重复消息被拒绝，active_tasks 没有增加
                 self.log.warning(f"Attempted to deny MsgID {msg_id} but it was not found in processing_msg_ids. Reason: {reason}")
        self.send_response("MsgDeny", msg_id, {
            "tid": tid,
            "reason": reason,
            "items": [] # 根据需要填充拒绝的具体项
        })

    def on_msg_start(self, resp):
        # 收到服务器的 MsgStartResponse
        msg_id=None
        tid=None
        task_exception = None # 用于记录同步任务的异常
        task_result = None # 用于记录同步任务的结果
        try:
            msg_id = resp.get("ID")
            resp_data = resp.get("Data", {})
            ok = resp_data.get("Result", False)
            tid = resp_data.get("tid", None)
            msg = None
            with self.lock:  # 访问 msgList 和 processing_msg_ids 需要锁
                # 首先检查 MsgID 是否在我们的 pending/processing 集合中
                if msg_id not in self.processing_msg_ids:
                     self.log.warning(f"Received MsgStartResponse (ID:{msg_id}, tid:{tid}) for an unknown or already processed message. Ignoring.")
                     # 如果收到的 MsgStartResponse 是 Result=True，但 MsgID 已经不在 processing_msg_ids 中，
                     # 这意味着这个任务可能已经被之前的 MsgStartResponse (Result=True) 触发并完成了，或者被 Deny 了。
                     # 无论如何，都不需要再次提交任务或调整 active_tasks。
                     return # 忽略该响应
                # 如果 MsgID 在 processing_msg_ids 中
                msg = self.msgList.get(msg_id) # 获取消息内容
                if msg is None:
                    # 这不应该发生，因为 NewMsg 接受时会将消息存储到 msgList
                    self.log.error(f"Received MsgStartResponse (ID:{msg_id}, tid:{tid}) but message not found in msgList. This indicates a state inconsistency. Cleaning up.")
                    # 从 processing_msg_ids 中移除以清理状态
                    self.processing_msg_ids.discard(msg_id)
                    # active_tasks 未增加，无需减少
                    return
            # 处理服务器的回应
            if ok:
                # 服务器同意开始
                self.log.info(f"Server approved task start (ID:{msg_id}, tid:{tid}).")
                with self.lock:
                    self.active_tasks += 1  # 无论同步异步，都先增加 active_tasks
                    self.log.info(f"Task {tid} (ID:{msg_id}) processing started. Active tasks: {self.active_tasks}/{self.max_concurrent_tasks}")
                if self.max_concurrent_tasks == 1 and self.task_executor is None:
                    # --- 单线程同步执行 ---
                    self.log.info(f"Executing task {tid} (ID:{msg_id}) synchronously in main thread.")
                    try:
                        # 直接调用，self.start_msg 内部会发送 MsgDone
                        self.start_msg(msg) # self.start_msg 现在应该返回一个类似 future.result() 的东西或 None
                    except Exception as e:
                        task_exception = e # 捕获同步执行中的异常
                        # start_msg 内部的异常应该已经被它自己处理并发送 MsgDone 了
                        # 但如果 start_msg 本身在发送 MsgDone 前就崩溃了，这里需要一个保障
                        error_message = traceback.format_exc()
                        self.log.error(f"Synchronous task {tid} (ID:{msg_id}) failed with unhandled exception: {error_message}")
                        # 确保发送MsgDone，如果start_msg没有发送的话
                        # 检查是否已发送过MsgDone比较复杂，这里简化为再次发送，服务器应能处理重复的MsgDone确认
                        # self.send_response("MsgDone", msg_id, {"tid": tid, "status": "fail", "msg": f"Unhandled exception: {e}"})
                    finally:
                        # --- 手动调用清理回调 ---
                        # 模拟 future 对象，只需要一个 exception() 方法
                        class SynchronousFuture:
                            def __init__(self, _exception=None, _result=None):
                                self._exception = _exception
                                self._result = _result
                            def exception(self):
                                return self._exception
                            def result(self): # 可选，如果回调需要结果
                                if self._exception:
                                     raise self._exception
                                return self._result

                        pseudo_future = SynchronousFuture(_exception=task_exception, _result=task_result)
                        self._task_done_callback_with_id(pseudo_future, msg_id, tid)
                elif self.task_executor:
                    # --- 多线程异步执行 ---
                    self.log.info(f"Submitting task {tid} (ID:{msg_id}) to task executor.")
                    try:
                        future = self.task_executor.submit(self.start_msg, msg)
                        future.add_done_callback(lambda f: self._task_done_callback_with_id(f, msg_id, tid))
                    except:
                        # 提交到线程池失败，也需要清理 active_tasks
                        with self.lock:
                            self.active_tasks -=1 # 回滚增加的 active_tasks
                        error_message = traceback.format_exc()
                        self.log.error(f"Failed to submit task {tid} (ID:{msg_id}) to executor: {error_message}")
                        status = "fail"
                        return_msg = f"Internal error: Failed to submit task. {error_message}"
                        with self.lock:
                            self.processing_msg_ids.discard(msg_id)
                            if msg_id in self.msgList: del self.msgList[msg_id]
                        self.send_response("MsgDone", msg_id, {"tid": tid, "status": status, "msg": return_msg})
                else:
                    # max_concurrent_tasks > 1 但 task_executor is None，这是配置错误
                    self.log.error(f"Configuration error: max_concurrent_tasks is {self.max_concurrent_tasks} but task_executor is None. Cannot process task (ID:{msg_id}, tid:{tid}).")
                    with self.lock:
                        self.active_tasks -= 1 # 回滚
                    status = "fail"
                    return_msg = "Internal error: Task executor not configured correctly for multi-threading."
                    with self.lock:
                        self.processing_msg_ids.discard(msg_id)
                        if msg_id in self.msgList: del self.msgList[msg_id]
                    self.send_response("MsgDone", msg_id, {"tid": tid, "status": status, "msg": return_msg})
            else:
                # 服务器拒绝开始，清理消息并从 processing_msg_ids 中移除
                self.log.warning(f"Server denied start for msg_id: {msg_id} (tid:{tid}). Removing task.")
                with self.lock:  # 操作 msgList, processing_msg_ids 需要锁
                    if msg_id in self.msgList:
                        del self.msgList[msg_id]
                    self.processing_msg_ids.discard(msg_id) # 从处理集合中移除
                    # 服务器拒绝，active_tasks 未增加，无需减少
                    self.log.info(f"Task {tid} (ID:{msg_id}) denied by server. Messages pending/processing: {len(self.processing_msg_ids)}")
                # 可以选择回复 MsgDeny 给服务器，虽然 MsgStartResponse(Result=False) 已经表明拒绝
                # self.send_response("MsgDeny", msg_id, {"tid": tid, "reason": "Server denied start"}) # 可选
        except Exception as e:
            self.log.error(f"on_msg_start 处理消息时出错 (msg_id: {msg_id}, tid:{tid}): {e}\n{traceback.format_exc()}")
            # 在异常发生时，也需要考虑清理状态，以防任务挂起
            with self.lock:
                 if msg_id in self.processing_msg_ids:
                     self.processing_msg_ids.discard(msg_id) # 尝试移除
                     if msg_id in self.msgList:
                         del self.msgList[msg_id]
                     # 异常发生时，task可能尚未提交，active_tasks 可能未增加，无需减少
    # 修改回调函数以接收 msg_id 和 tid
    def _task_done_callback_with_id(self, future, msg_id, task_id):
        # 这个回调函数在任务从线程池完成时被调用
        try:
            # 检查任务执行是否有异常
            exc = future.exception()
            if exc:
                # 任务执行内部抛出异常，start_msg 应该已经捕获并发送了 MsgDone
                self.log.error(f"Callback (ID:{msg_id}, tid:{task_id}): Task failed with exception: {exc}\n{traceback.format_exc()}")
            else:
                # 任务成功完成
                self.log.info(f"Callback (ID:{msg_id}, tid:{task_id}): Task execution completed.")
        except Exception as e:
             # 回调函数自身的错误处理
             self.log.error(f"Error in task done callback itself (ID:{msg_id}, tid:{task_id}): {e}\n{traceback.format_exc()}")
        finally:
            # 无论任务成功或失败，都执行到这里进行清理
            # 清理 processing_msg_ids 和 active_tasks
            with self.lock:
                if msg_id in self.processing_msg_ids:
                    self.processing_msg_ids.discard(msg_id) # 任务完成，从处理集合中移除
                    # 减少 active_tasks 计数，因为它代表正在执行的任务
                    self.active_tasks -= 1
                    self.log.info(f"Callback (ID:{msg_id}, tid:{task_id}): Task state cleaned up. Active tasks: {self.active_tasks}/{self.max_concurrent_tasks}. Messages pending/processing: {len(self.processing_msg_ids)}")
                else:
                    # 这不应该发生，除非 MsgID 在任务完成前被其他地方意外移除了
                    self.log.warning(f"Callback (ID:{msg_id}, tid:{task_id}): Task completed, but MsgID not found in processing_msg_ids. State inconsistency?")
            # 移除 msgList 中的消息（如果存在）
            with self.lock:
                 if msg_id in self.msgList:
                      del self.msgList[msg_id]


    def on_msg_time(self, resp):
        # 收到服务器的 MsgTimeResponse
        msg_id = resp.get("ID")
        # 在新的逻辑中，MsgTimeResponse 应该对应一个在 processing_msg_ids 中的消息
        msg_exists_in_processing = False
        with self.lock:
             msg_exists_in_processing = msg_id in self.processing_msg_ids
        ok = resp.get("Data", {}).get("Result", False)
        if msg_exists_in_processing:
            if ok:
                self.log.info(f"设置消息时间成功:[{msg_id}]")
            else:
                self.log.warning(f"设置消息时间失败:[{msg_id}]")
        else:
            self.log.warning(f"收到的 MsgTimeResponse 没有对应正在处理的消息:[{msg_id}]")

    def on_msg_done(self, resp):
        # 收到服务器的 MsgDoneResponse
        # 这个响应是对我们发送的 MsgDone 的确认
        msg_id = resp.get("ID")
        ok = resp.get("Data", {}).get("Result", False)
        tid = resp.get("Data", {}).get("tid", None)
        # MsgDoneResponse 应该对应一个已经完成的任务
        # 任务的清理（active_tasks 和 processing_msg_ids 移除）应该已经在 _task_done_callback 中完成
        # 这里的处理主要是记录服务器的确认状态，并可以作为额外的兜底清理
        # 由于 MsgDoneResponse 可能晚于回调，msg_id 可能已经被移除
        with self.lock:
            msg_removed = False
            if msg_id in self.msgList:
                del self.msgList[msg_id]
                msg_removed = True
        if ok:
            self.log.info(f"收到服务器消息完成确认:[{msg_id}] (tid:{tid}). 状态: {ok}. 消息已从 msgList 中移除: {msg_removed}")
        else:
            self.log.warning(f"收到服务器消息完成拒绝:[{msg_id}] (tid:{tid}). 状态: {ok}. 消息已从 msgList 中移除: {msg_removed}")

    # 添加一个关闭线程池的方法，用于程序退出时调用
    def shutdown(self):
        self.log.info("Predictor shutdown requested. Closing WebSocket and thread pool.")
        # 先设置状态为非连接和非启动，以便线程可以退出
        self.isConnected = False
        self.isStarted = False
        # 关闭WebSocket连接
        if self.ws:
             try:
                 # 可以在一个单独的线程中调用 ws.close()，以防阻塞 shutdown
                 threading.Thread(target=lambda: self.ws.close() if self.ws else None).start()
             except Exception as e:
                 self.log.error(f"Exception during ws.close() in shutdown: {e}")
        else:
             self.log.warning("WebSocket object is None during shutdown.")
        # 关闭线程池，等待任务完成 (wait=True)
        if self.task_executor:
             self.log.info("Shutting down task executor, waiting for pending tasks...")
             try:
                 self.task_executor.shutdown(wait=True, cancel_futures=True) # 等待所有任务完成，或取消未开始的任务
                 self.log.info("Task executor shut down.")
             except Exception as e:
                  self.log.error(f"Exception during task_executor shutdown: {e}")
        else:
             self.log.warning("Task executor is None during shutdown.")

if __name__ == '__main__':
    freeze_support()
    APP_name = "common_polyclone_bg"
    predictor = Predictor(APP_name)
    try:
        predictor.start() # start() 方法是阻塞的
    except KeyboardInterrupt:
        # 优雅地处理中断信号 (Ctrl+C)
        predictor.log.info("Ctrl+C received. Shutting down...")
        # 触发 shutdown 逻辑
    except Exception as e:
        predictor.log.error(f"Unexpected error in main loop: {e}\n{traceback.format_exc()}")
        # 触发 shutdown 逻辑
    finally:
        # 无论发生什么异常，都在 finally 块中调用 shutdown
        predictor.shutdown()
        predictor.log.info("Application exit.")


# pyinstaller -D mq_main_common_polyclone_bg.spec --noconfirm
