"""
General utils
"""
import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2,50))
import tifffile
import time
from pathlib import Path
# Settings
import networkx
import numpy as np
from PIL import Image,ImageFile
ImageFile.LOAD_TRUNCATED_IMAGES = True
Image.MAX_IMAGE_PIXELS = None
from networkx.algorithms.components.connected import connected_components
from scipy.spatial.distance import squareform, pdist
FILE = Path(__file__).resolve()
ROOT = FILE.parents[1]  # YOLOv5 root directory
import math
from sklearn.neighbors import KDTree
import cv2
import imutils
from loguru import logger as log
import traceback

class channel_struct:
    def __init__(self, cell_img=np.zeros((1, 1, 3), np.uint8), cell_gray=np.zeros((1, 1), np.uint8), type=0,
                 local_channel=0, group_type=1, area=0, perimeter=0, major_axis_length=0, minor_axis_length=0,
                 roundness=0, sharpne=0, edge_diff=0, avg_gray: float = 0, sum_gray=0, max_gray=0, min_gray=0,
                 binary_img=np.zeros((1, 1), np.uint8),
                 background_diff=0, circle_rate=0, luminance=0, center_x=0, center_y=0):
        self.tx = None
        self.bx = None
        self.by = None
        self.ty = None
        self.extend_img = None
        self.conuter = None
        self.custom_color_dic={0:"FFFFFF",1:"00FF00",2:"FF0000",3:"0000FF"}
        self.cell_img = cell_img  # 彩色图
        self.cell_gray = cell_gray  # 灰度图
        self.type = type  # 属性值
        self.local_channel = local_channel  # 通道
        self.center_x = int(center_x)  # 中心点X坐标
        self.center_y = int(center_y)  # 中心点Y坐标
        self.group_type = group_type  # 团属性
        self.major_axis_length = major_axis_length  # 主轴长度
        self.minor_axis_length = minor_axis_length  # 次轴长度
        self.area = area  # 面积
        self.perimeter = perimeter  # 周长
        self.roundness = roundness  # 圆度
        self.sharpne = sharpne  # 边缘锐利度
        self.avg_gray = avg_gray  # 平均灰度
        self.sum_gray = sum_gray  # 累计灰度
        self.min_gray = min_gray  # 最小灰度
        self.max_gray = max_gray  # 最大灰度
        self.diff_gray = self.max_gray - self.min_gray  # 灰度差
        self.edge_diff = edge_diff  # 边缘差异率
        self.radius = int((self.major_axis_length + self.minor_axis_length) / 4)
        self.background_diff = background_diff  # 背景差异
        self.circle_rate = circle_rate  # 正圆率
        self.luminance = luminance  # 光度
        self.binary_img = binary_img
        self.point_conuter=""
        self.struct_infos = {"属性值": self.type, "通道": self.local_channel, "中心点X坐标": self.center_x,
                             "中心点Y坐标": self.center_y, "团属性": self.group_type, "主轴长度": self.major_axis_length,
                             "次轴长度": self.minor_axis_length, "面积": self.area, "周长": self.perimeter,
                             "圆度": self.roundness, "边缘锐利度": self.sharpne, "平均灰度": self.avg_gray, "累计灰度": self.sum_gray,
                             "最小灰度": self.min_gray, "最大灰度": self.max_gray, "灰度差": self.diff_gray,
                             "边缘差异率": self.edge_diff, "背景差异": self.background_diff, "正圆率": self.circle_rate,
                             "光度": self.luminance}

    def get_seg_info(self, cell_img=np.zeros((1, 1, 3), np.uint8),tx=0,ty=0,bx=0,by=0,
                     center_x=0, center_y=0,area=0,perimeter=0,axis_major_length=0,axis_minor_length=0):
        self.cell_gray = cv2.cvtColor(cell_img, cv2.COLOR_BGR2GRAY)  # 灰度图
        self.center_x, self.center_y = center_x, center_y  # 中心点
        self.major_axis_length=axis_major_length
        self.minor_axis_length=axis_minor_length
        self.area = area  # 面积
        self.tx=tx
        self.bx=bx
        self.by=by
        self.ty=ty
        self.perimeter =perimeter
        self.roundness = (4 * math.pi * self.area) / self.perimeter ** 2 if self.perimeter != 0 else 0  # 圆度
        self.sharpne = cv2.Laplacian(self.cell_gray, cv2.CV_64F).var()  # 边缘锐利度
        self.avg_gray = np.mean(self.cell_gray)  # 平均灰度
        self.sum_gray = np.sum(self.cell_gray)  # 累计灰度
        self.min_gray = int(np.min(self.cell_gray))  # 最小灰度
        self.max_gray = np.max(self.cell_gray)  # 最大灰度
        self.diff_gray = self.max_gray - self.min_gray  # 灰度差
        self.edge_diff = self.max_gray - self.min_gray
        self.background_diff = abs(int(self.avg_gray) - int(self.min_gray))  # 背景差异
        self.circle_rate = np.sqrt(1 - (self.minor_axis_length / self.major_axis_length) ** 2) if self.major_axis_length != 0 else 0 # 正圆率
        self.luminance = np.mean(cv2.cvtColor(cell_img, cv2.COLOR_BGR2HSV)[:, :, 2])
        self.point_conuter=f"{'|'.join([str(self.tx)+':'+str(self.ty),str(self.bx)+':'+str(self.by)])}"
        self.radius = int((self.major_axis_length + self.minor_axis_length) / 4)
        self.struct_infos = {"属性值": self.type, "通道": self.local_channel, "中心点X坐标": self.center_x,
                             "中心点Y坐标": self.center_y,
                             "团属性": self.group_type, "主轴长度": self.major_axis_length, "次轴长度": self.minor_axis_length,
                             "面积": self.area, "周长": self.perimeter, "圆度": self.roundness, "边缘锐利度": self.sharpne,
                             "平均灰度": self.avg_gray, "累计灰度": self.sum_gray, "最小灰度": self.min_gray, "最大灰度": self.max_gray,
                             "灰度差": self.diff_gray, "边缘差异率": self.edge_diff, "背景差异": self.background_diff,
                             "正圆率": self.circle_rate, "光度": self.luminance}
        del self.cell_gray
        return self

    def get_det_info(self, img, group_type=1, xyxy_0=0, xyxy_1=0, xyxy_2=0, xyxy_3=0, padding=3):
        self.tx = xyxy_0
        self.ty = xyxy_1
        self.bx = xyxy_2
        self.by = xyxy_3
        self.cell_img = img[int(xyxy_1):int(xyxy_3), int(xyxy_0):int(xyxy_2)]  # 彩色图
        self.cell_gray = cv2.cvtColor(self.cell_img, cv2.COLOR_BGR2GRAY)  # 灰度图
        self.extend_img = self.crop_box(img, xyxy_0, xyxy_1, xyxy_2, xyxy_3, padding=padding)  # 扩展灰度图
        self.conuter = self.find_conuter_det(self.extend_img)  # 轮廓
        self.major_axis_length = max(xyxy_2 - xyxy_0, xyxy_3 - xyxy_1)
        self.minor_axis_length = min(xyxy_2 - xyxy_0, xyxy_3 - xyxy_1)  # 长短轴
        self.area = self.major_axis_length * self.minor_axis_length
        self.perimeter = 2 * (self.major_axis_length + self.minor_axis_length)
        self.center_x = int((xyxy_0 + xyxy_2) / 2)
        self.center_y = int((xyxy_1 + xyxy_3) / 2)  # 中心点
        self.group_type = group_type  # 团属性
        if len(self.conuter) > 0:
            if cv2.contourArea(self.conuter) > (2 * self.area) / 3:
                self.major_axis_length, self.minor_axis_length = self.compute_boudingbox(self.conuter)  # 长短轴
                self.area = cv2.contourArea(self.conuter)
                self.perimeter = cv2.arcLength(self.conuter, True)  # 周长
        self.roundness = (4 * math.pi * self.area) / self.perimeter ** 2 if self.perimeter != 0 else 0  # 圆度
        self.sharpne = cv2.Laplacian(self.cell_gray, cv2.CV_64F).var()  # 边缘锐利度
        self.avg_gray = np.mean(self.cell_gray)  # 平均灰度
        self.sum_gray = np.sum(self.cell_gray)  # 累计灰度
        self.min_gray = np.min(self.cell_gray)  # 最小灰度
        self.max_gray = np.max(self.cell_gray)  # 最大灰度
        self.diff_gray = self.max_gray - self.min_gray  # 灰度差
        self.edge_diff = (self.max_gray - self.min_gray) / self.max_gray if self.max_gray != 0 else 0
        self.background_diff = abs(int(self.avg_gray) - int(self.min_gray))  # 背景差异
        self.circle_rate = np.sqrt(1 - (self.minor_axis_length / self.major_axis_length) ** 2) if self.major_axis_length != 0 else 0 # 正圆率
        self.luminance = np.mean(cv2.cvtColor(self.cell_img, cv2.COLOR_BGR2HSV)[:, :, 2])
        self.point_conuter=f"{'|'.join([str(self.tx)+':'+str(self.ty),str(self.bx)+':'+str(self.by)])}"
        self.radius = int((self.major_axis_length + self.minor_axis_length) / 4)
        self.struct_infos = {"属性值": self.type, "通道": self.local_channel, "中心点X坐标": self.center_x,
                             "中心点Y坐标": self.center_y,
                             "团属性": self.group_type, "主轴长度": self.major_axis_length, "次轴长度": self.minor_axis_length,
                             "面积": self.area, "周长": self.perimeter, "圆度": self.roundness, "边缘锐利度": self.sharpne,
                             "平均灰度": self.avg_gray, "累计灰度": self.sum_gray, "最小灰度": self.min_gray, "最大灰度": self.max_gray,
                             "灰度差": self.diff_gray, "边缘差异率": self.edge_diff, "背景差异": self.background_diff,
                             "正圆率": self.circle_rate, "光度": self.luminance}
        del self.cell_img
        del self.cell_gray
        del self.extend_img
        del self.conuter
        return self

    def copy(self):
        import copy
        dup = copy.deepcopy(self)
        return dup

    def crop_box(self, im, x1, y1, x2, y2, padding=2):
        x1 = max(x1 - padding, 0)
        y1 = max(y1 - padding, 0)
        x2 = min(x2 + padding, im.shape[1])
        y2 = min(y2 + padding, im.shape[0])
        return im[y1:y2, x1:x2]

    def compute_boudingbox(self,contour):
        try:
            x, y, w, h = cv2.boundingRect(contour)
            maxj=max(w,h)
            minj=min(w,h)
            axis = [maxj, minj]
        except:
            axis = [0, 0]
        return axis

    def delet_contours(self,contours, delete_list):
        #  自定义函数:用于删除列表指定序号的轮廓
        #  输入 1:contours:原始轮廓
        #  输入 2:delete_list:待删除轮廓序号列表
        #  返回值:contours:筛选后轮廓
        delta = 0
        for i in range(len(delete_list)):
            del contours[delete_list[i] - delta]
            delta = delta + 1
        return contours
    def hierarchy_contours(self,contours, hierarchy):
        # 筛选轮廓
        # 使用层级结构筛选轮廓
        # hierarchy[i]: [Next,Previous,First_Child,Parent]
        # 要求有父级轮廓
        #print("hierarchy",hierarchy)
        delete_list = []  # 新建待删除的轮廓序号列表
        c, row, col = hierarchy.shape
        for i in range(row):
            if hierarchy[0, i, 3]<0:  # 没有父轮廓或子轮廓
                delete_list.append(i)
        # 根据列表序号删除不符合要求的轮廓
        end_contours = self.delet_contours(contours, delete_list)
        return end_contours
    def find_conuter_det(self, crop_img):
        try:
            extend_gray=cv2.cvtColor(crop_img,cv2.COLOR_BGR2GRAY)
            kennel= cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            #kennel= np.ones((3, 3), np.uint8)
            grad_gray = cv2.morphologyEx(extend_gray, cv2.MORPH_GRADIENT, kernel=kennel)
            ret, binary = cv2.threshold(grad_gray, 1, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            cnts,hierarchy = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
            maxc = max(cnts, key=cv2.contourArea)
        except:
            maxc = []
        return maxc

    def find_conuter_seg(self, gray):
        try:
            cnts = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cnts = imutils.grab_contours(cnts)
            maxc = max(cnts, key=cv2.contourArea)
        except:
            maxc = []
        return maxc

    def print_cell_infos(self):
        log.info(self.struct_infos)

    def output(self):
        return [self.type, self.local_channel, self.center_x, self.center_y, self.group_type, self.area, self.perimeter,
                self.major_axis_length, self.minor_axis_length, self.roundness, self.sharpne, self.edge_diff,
                self.avg_gray, self.sum_gray, self.max_gray,
                self.min_gray, self.diff_gray, self.background_diff, self.circle_rate, self.luminance,self.point_conuter]

    def output_group_info(self):
        return [self.center_x, self.center_y, self.group_type, self.area, self.perimeter, self.major_axis_length,
                self.minor_axis_length, self.roundness, self.sharpne, self.edge_diff, self.avg_gray, self.sum_gray,
                self.max_gray,
                self.min_gray, self.diff_gray, self.background_diff, self.circle_rate, self.luminance,self.point_conuter]

    def to_file(self, path):
        cv2.imencode('.jpg', self.cell_img)[1].tofile(path)
        return True

def coutoure_jypue(firest_countour,process_countours):
    # 判断两个轮廓是否相交,如果相交,则剔除第一个轮廓集合中的轮廓
    del_list = []
    for two_co in process_countours:
        for index in range(len(firest_countour)):
            if cv2.pointPolygonTest(firest_countour[index],tuple([int(i) for i in two_co[0][0]]),False)>=0:
                del_list.append(index)
    for index in set(del_list[::-1]):
        firest_countour = firest_countour[:index] + firest_countour[index + 1:]
    process_countours+= firest_countour
    return process_countours

class cell_struct:
    def __init__(self, BF=None, FL1=None, FL2=None,FL3=None,FL4=None):
        self.cell_info = [BF, FL1, FL2,FL3,FL4]

    def get_channel_info(self, channel):
        if channel == 0 or channel == "BF" or channel == "bf":
            return self.cell_info[0]
        elif channel == 1 or channel == "FL1" or channel == "fl1":
            return self.cell_info[1]
        elif channel == 2 or channel == "FL2" or channel == "fl2":
            return self.cell_info[2]
        elif channel == 3 or channel == "FL3" or channel == "fl3":
            return self.cell_info[3]
        elif channel == 4 or channel == "FL4" or channel == "fl4":
            return self.cell_info[4]
        else:
            log.info("channel not support ")
            return None

    def get_all_channel_info(self, id=1):
        result = ""
        for channel in self.cell_info:
            if channel is not None:
                result += str(id) + "," + ",".join([str(i) for i in channel.output()]) + "\n"
        return result

def replace(cellinfo, replacinfo):
    cellinfo.area = replacinfo.area
    cellinfo.perimeter = replacinfo.perimeter
    cellinfo.major_axis_length = replacinfo.major_axis_length
    cellinfo.minor_axis_length = replacinfo.minor_axis_length
    cellinfo.roundness = replacinfo.roundness
    cellinfo.circle_rate = replacinfo.circle_rate
    cellinfo.binary_img = replacinfo.binary_img
    return cellinfo


def replace_zero(cellinfo):
    cellinfo.center_x = 0
    cellinfo.center_y = 0
    cellinfo.group_type = 0
    cellinfo.area = 0
    cellinfo.perimeter = 0
    cellinfo.major_axis_length = 0
    cellinfo.minor_axis_length = 0
    cellinfo.roundness = 0
    cellinfo.circle_rate = 0
    cellinfo.sharpne = 0
    cellinfo.edge_diff = 0
    cellinfo.avg_gray = 0
    cellinfo.sum_gray = 0
    cellinfo.max_gray = 0
    cellinfo.min_gray = 0
    cellinfo.diff_gray = 0
    cellinfo.background_diff = 0
    cellinfo.circle_rate = 0
    cellinfo.luminance = 0
    return cellinfo
class cell_list:
    def __init__(self):
        self.cell_infos = []
        self.group_list = [[], [], [],[], []]
        self.group_flag = False

    def merge_cell_list(self, cell_list):
        for cell in cell_list.cell_infos:
            self.cell_infos.append(cell)
        return self

    def push_cell(self, cell_struct):
        self.cell_infos.append(cell_struct)

    def infos_output(self):
        analysis_result_title = ','.join(['细胞编号', '属性值', '通道', 'X坐标', 'Y坐标', '团属性', '面积',
                                        '周长', '长轴', '短轴', '圆度', '边缘锐利度', '边缘差异率', '平均灰度', '累计灰度',
                                        '最大灰度', '最小灰度', '灰度差', '背景差异', '正圆率', '亮度','轮廓']) + "\n"
        if len(self.cell_infos) > 0:
            result = ""
            for num, cell in enumerate(self.cell_infos,start=1):
                result += cell.get_all_channel_info(num)
            return analysis_result_title + result
        else:
            return analysis_result_title

    def save_tiff_mask(self, mask_save_path, channel="FL1", img_shape=None, compression_level=5, bigtiff=False):
        """保存细胞掩码为TIFF文件，支持批处理和进度显示"""

        try:
            start_time = time.time()
            # 确保目录存在
            os.makedirs(os.path.dirname(mask_save_path), exist_ok=True)

            # 计算有效细胞数量
            valid_cells = []
            for cell in self.cell_infos:
                cell_info = cell.get_channel_info(channel)
                if cell_info is not None and hasattr(cell_info,
                                                     'binary_img') and cell_info.binary_img is not None and cell_info.binary_img.size > 0:
                    valid_cells.append(cell)

            total_cells = len(valid_cells)
            if total_cells == 0:
                log.warning(f"通道 {channel} 中没有找到有效细胞")
                return False
            log.info(f"开始保存 {total_cells} 个细胞掩码到 {mask_save_path}")
            # 创建TiffWriter对象，使用适当的参数
            # 预处理所有掩码数据，减少IO操作
            all_masks = []
            all_metadata = []
            for index, cell in enumerate(valid_cells, start=1):
                cell_info = cell.get_channel_info(channel)
                all_masks.append(cell_info.binary_img)
                all_metadata.append(
                    {'cell_number': index, "boxs": [cell_info.tx, cell_info.ty, cell_info.bx, cell_info.by]})
            if total_cells > 255:
                log.info("启用单页tiff保存")
                if len(img_shape) == 3:
                    img_shape = img_shape[:2]
                mask = np.zeros(img_shape, dtype=np.uint16)
                for cell_mask, cell_metadata in zip(all_masks, all_metadata):
                    try:
                        tx, ty, bx, by = cell_metadata["boxs"]
                        cell_index = cell_metadata["cell_number"]
                        cell_mask = cell_mask.astype(np.uint16)
                        cell_mask *= cell_index
                        non_zero_indices = np.nonzero(cell_mask)
                        new_y_indices = non_zero_indices[0] + ty
                        new_x_indices = non_zero_indices[1] + tx
                        mask[new_y_indices, new_x_indices] = cell_mask[non_zero_indices]
                    except Exception as e:
                        print(e)
                        continue
                with tifffile.TiffWriter(mask_save_path, bigtiff=bigtiff) as obj_mask:
                    obj_mask.write(
                        mask,
                        metadata={'total_cells': total_cells, 'shape': img_shape, 'channels': channel},
                        compression="zlib",  # 使用zlib压缩,平衡速度和压缩率
                        compressionargs={'level': compression_level},  # 使用压缩级别5以提高速度
                        contiguous=True
                    )
            else:
                log.info("启用多页tiff保存")
                # 使用上下文管理器创建TiffWriter对象
                with tifffile.TiffWriter(mask_save_path, bigtiff=bigtiff) as obj_mask:
                    # 批量处理
                    for mask, metadata in zip(all_masks, all_metadata):
                        obj_mask.write(
                            mask,
                            metadata=metadata,
                            compression="zlib",  
                            compressionargs={'level': compression_level},  # 使用压缩级别5以提高速度
                        )
            # 计算并显示性能统计
            elapsed_time = time.time() - start_time
            file_size_mb = os.path.getsize(mask_save_path) / (1024 * 1024)
            log.info(f"TIFF掩码保存完成: {mask_save_path}")
            log.info(
                f"总耗时: {elapsed_time:.2f}秒, 文件大小: {file_size_mb:.2f}MB, 速度: {file_size_mb / elapsed_time:.2f}MB/s")
            return True
        except:
            log.error("保存TIFF掩码失败",exc_info=True)
            return False
    def get_length(self):
        return len(self.cell_infos)

    def get_sum_channel(self):
        temp = []
        for cell in self.cell_infos:
            for channel in cell:
                if channel is not None:
                    temp.append(channel.local_channel)
        return len(set(temp))

    def group_computer(self, channel, distance_tred=0):
        group_starttime = time.time()
        self.group_flag = True
        if len(self.cell_infos) < 2:
            log.info(f"{len(self.cell_infos)} cell not make up a group")
            return False
        else:
            centerlist = []
            radiuslist = []
            for cell_num, cell in enumerate(self.cell_infos):
                if cell.get_channel_info(channel) is not None:
                    centerlist.append(
                        [cell.get_channel_info(channel).center_x, cell.get_channel_info(channel).center_y])
                    radiuslist.append(cell.get_channel_info(channel).radius)
            # print("centerlist",len(centerlist))
            # print("radiuslist",len(radiuslist))
            if len(centerlist) > 0:
                radius = np.array(radiuslist)
                centerlist = np.array(centerlist)
                tree = KDTree(centerlist)
                center_index = []
                topk = min(centerlist.shape[0], 50)
                for ind in range(centerlist.shape[0]):
                    dist, index = tree.query([centerlist[ind]], k=topk)
                    for num in range(1, topk):
                        if dist[0][num] <= radius[ind] + radius[index[0][num]] + distance_tred:
                            center_index.append([index[0][0], index[0][num]])
                sortedlist = merge_lists(center_index)
                len_list = [len(i) for i in sortedlist]
                # print("sortedlist",sortedlist)
                for list in sortedlist:
                    temp = [(int(centerlist[k][0]), int(centerlist[k][1])) for k in list]
                    self.group_list[channel].append(temp)
                    for k in list:
                        #print(k)
                        #print(self.cell_infos[k].get_channel_info(channel))
                        self.cell_infos[k].get_channel_info(channel).group_type = len(temp)
                print("channel ", channel, " 团计算cost_time", time.time() - group_starttime)
                return sum(len_list), self.group_list
            return False

    def draw_group(self, img, channel, line_thickness=1, color=(250, 250, 255)):#250, 250, 255
        if len(self.group_list[channel]) > 0:
            for cell_group in self.group_list[channel]:
                if len(cell_group) == 2:
                    raduis = np.max(squareform(pdist(np.array(cell_group)))) + 1
                    roundceter = (round((cell_group[0][0] + cell_group[1][0]) / 2),
                                  round((cell_group[0][1] + cell_group[1][1]) / 2))
                    # cv2.line(im0, ctentsers[0], ctentsers[1], (255, 255, 0), 1, cv2.LINE_AA)
                    if raduis>0 and roundceter[0]>-1 and roundceter[1]>-1:
                        cv2.circle(img, roundceter, int(raduis), color, line_thickness, cv2.LINE_AA)
                else:
                    raduis = np.max(squareform(pdist(np.array(cell_group))))-(0.75*len(cell_group))
                    px, py = get_centerpoint(cell_group)
                    if raduis>0 and px>-1 and py>-1:
                        cv2.circle(img, (px, py), int(raduis), color, line_thickness, cv2.LINE_AA)
        return img

    def draw_cell_info(self, img, channel, line_thickness=1):
        color = Colors()
        shuxing = [i for i in range(39)]
        # custom_colors = [color(random.randint(0, 255)) for i in range(36)]+[ (0, 0, 255), (0, 255, 0)]
        # shuxing = [1, 6, 7, 16, 2, 3, 10, 35, 34]
        # custom_colors = [(128, 128, 0), (34, 139, 34), (0, 0, 255), (0, 215, 255), (124, 205, 124), (85, 85, 205),
        #                  (0, 238, 238), (0, 0, 255), (0, 255, 0)]
        custom_colors={1:(220,220,220),2:(154,255,154),3:(238,99,99),4:(30,144,255),5:color(5),6:color(6),7:(255,0,0),8:(0,0,238),
                       9:color(9),10:(238,238,0),11:(0,255,255),12:color(12),13:(159,121,238),14:color(14),15:color(15),16:(238,180,34),
                       17:(0,139,139),18:color(18),19:(125,38,205),20:color(20),21:color(21),22:(248,248,255),23:color(23),24:color(24),
                       25:color(25),26:(245,245,245),27:color(27),28:color(28),29:color(29),30:color(30),31:color(31),32:color(32),33:color(33),
                       34:(0,255,0),35:(255,0,0),36:color(36),37:(0,255,0),38:(255,0,0)}

        if len(self.cell_infos) > 0:
            tl = line_thickness or round(0.002 * (img.shape[0] + img.shape[1]) / 2) + 1  # line/font thickness
            #tl=2
            if channel != "all":
                img = self.draw_group(img, channel, line_thickness=line_thickness)
                for cell in self.cell_infos:
                    if cell.get_channel_info(channel) is not None:
                        # cv2.rectangle(im, c1, c2, color, thickness=tl, lineType=cv2.LINE_AA)
                        # cv2.ellipse(img,
                        #            (cell.get_channel_info(channel).center_x, cell.get_channel_info(channel).center_y),
                        #             (int(cell.get_channel_info(channel).major_axis_length/2),
                        #              int(cell.get_channel_info(channel).minor_axis_length/2)),0,0,360,color=custom_colors[shuxing.index(cell.get_channel_info(channel).type)], thickness=tl,lineType=cv2.LINE_AA)
                        cv2.circle(img,
                                   (cell.get_channel_info(channel).center_x, cell.get_channel_info(channel).center_y),
                                   cell.get_channel_info(channel).radius,
                                   custom_colors[shuxing.index(cell.get_channel_info(channel).type)][::-1], tl, cv2.LINE_AA)
            else:
                for cell in self.cell_infos:
                    if cell.get_channel_info(0) is not None:
                        channel = 0
                    elif cell.get_channel_info(1) is not None:
                        channel = 1
                    elif cell.get_channel_info(2) is not None:
                        channel = 2
                    elif cell.get_channel_info(3) is not None:
                        channel = 3
                    else:
                        channel = 4
                    # cv2.ellipse(img,
                    #             (cell.get_channel_info(channel).center_x, cell.get_channel_info(channel).center_y),
                    #             (int(cell.get_channel_info(channel).major_axis_length/2),
                    #              int(cell.get_channel_info(channel).minor_axis_length/2)), 0, 0, 360,
                    #             color=custom_colors[shuxing.index(cell.get_channel_info(channel).type)], thickness=tl,
                    #             lineType=cv2.LINE_AA)
                    cv2.circle(img,
                               (cell.get_channel_info(channel).center_x, cell.get_channel_info(channel).center_y),
                               cell.get_channel_info(channel).radius,
                               custom_colors[shuxing.index(cell.get_channel_info(channel).type)][::-1], tl, cv2.LINE_AA)

        return img

    def to_csv(self, data_save_path, group_computer=False):
        if os.path.exists(data_save_path):
            os.remove(data_save_path)
        if not self.cell_infos:
            log.info("cell_infos is empty")
        if group_computer:
            if not self.group_flag:
                log.info("group_computer is not run")
                self.group_computer(channel=0)
        try:
            with open(data_save_path, 'a+', encoding='utf-8') as f:
                f.write(self.infos_output())
                log.info(f"{data_save_path} cell_infos write to csv success")
        except Exception as e:
            log.error(f"{data_save_path} cell_infos write to csv fail {e}")
            error_message = traceback.format_exc()
            log.error(f"error:{error_message}")
        return True

    def get_RECT(self, infos, im_shape, padding=0):
        left_column_max = max(infos.tx - padding, 0)  # tx
        right_column_min = min(infos.bx + padding, im_shape[1])  # bx
        up_row_max = max(infos.ty - padding, 0)  # ty
        down_row_min = min(infos.by + padding, im_shape[0])  # by
        return list(map(int, [left_column_max, up_row_max, right_column_min, down_row_min]))

    def compute_IOU(self, rec1, rec2):
        """
        计算两个矩形框的交并比.
        :param rec1: (x0,y0,x1,y1)      (x0,y0)代表矩形左上的顶点,（x1,y1）代表矩形右下的顶点.下同.
        :param rec2: (x0,y0,x1,y1)
        :return: 交并比IOU.
        """
        left_column_max = max(rec1[0], rec2[0])
        right_column_min = min(rec1[2], rec2[2])
        up_row_max = max(rec1[1], rec2[1])
        down_row_min = min(rec1[3], rec2[3])
        # 两矩形无相交区域的情况
        if left_column_max >= right_column_min or down_row_min <= up_row_max:
            return 0
        # 两矩形有相交区域的情况
        else:
            S1 = (rec1[2] - rec1[0]) * (rec1[3] - rec1[1])
            S2 = (rec2[2] - rec2[0]) * (rec2[3] - rec2[1])
            S_cross = (down_row_min - up_row_max) * (right_column_min - left_column_max)
            return S_cross / (S1 + S2 - S_cross)
    def get_shuxing5(self, im_shape, same_cell_iou_thred=0.3, bf_img=None, fl1_img=None, fl2_img=None, fl3_img=None,
                     fl4_img=None, fcs_output=False):
        compare_map = np.zeros([im_shape[1], im_shape[0], 5])
        growh_num = 17
        for cell_num, cell in enumerate(self.cell_infos):
            for channel in cell.cell_info:
                if channel is not None:
                    compare_map[channel.center_x, channel.center_y, channel.local_channel] = cell_num + growh_num
        iou_thred = same_cell_iou_thred
        BF_FL1_FL2_FL3_FL4_array = compare_map[np.where((compare_map[:, :, 0] > 0) & (compare_map[:, :, 1] > 0) & (compare_map[:, :, 2] > 0) & (compare_map[:, :, 3] > 0) & (compare_map[:, :, 4] > 0))]
        BF_FL1_FL2_FL3_FL4 = (BF_FL1_FL2_FL3_FL4_array - growh_num).tolist()
        BF, BF_FL1, BF_FL2, BF_FL3, BF_FL4, FL1, FL2, FL3, FL4, FL1_FL2, FL1_FL3, FL1_FL4, FL2_FL3, FL2_FL4, FL3_FL4, BF_FL1_FL2, \
        BF_FL1_FL3, BF_FL1_FL4, BF_FL2_FL3, BF_FL2_FL4, BF_FL3_FL4, FL1_FL2_FL3, FL1_FL2_FL4, FL1_FL3_FL4, FL2_FL3_FL4, BF_FL1_FL2_FL3, \
        BF_FL1_FL2_FL4, BF_FL1_FL3_FL4, BF_FL2_FL3_FL4, FL1_FL2_FL3_FL4 = [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], \
        [], [], [], [], [], [], [], [], [], [], [], [], []
        compare_map[np.where((compare_map[:, :, 0] > 0) & (compare_map[:, :, 1] > 0) & (compare_map[:, :, 2] > 0) & (compare_map[:, :, 3] > 0) & (compare_map[:, :, 4] > 0))] = 0
        compare_map = compare_map.astype(int)
        for bfchannel_index in compare_map[np.nonzero(compare_map[:, :, 0])]:
            bf_cell_info = self.cell_infos[int(bfchannel_index[0] - growh_num)].get_channel_info("BF")
            bf_roi_extend_box = self.get_RECT(bf_cell_info, im_shape, padding=bf_cell_info.radius)
            bf_box = self.get_RECT(bf_cell_info, im_shape, padding=0)
            channelroi = compare_map[bf_roi_extend_box[0]:bf_roi_extend_box[2],bf_roi_extend_box[1]:bf_roi_extend_box[3],:]
            exist_fl1, exist_fl2, exist_fl3, exist_fl4 = False, False, False, False
            fl1_index, fl2_index, fl3_index, fl4_index = -1, -1, -1, -1
            for FL1_channel in channelroi[np.nonzero(channelroi[:, :, 1])]:
                fl1_cell_info = self.cell_infos[FL1_channel[1] - growh_num].get_channel_info("FL1")
                fl1_box = self.get_RECT(fl1_cell_info, im_shape, padding=0)
                if self.compute_IOU(bf_box, fl1_box) > iou_thred:
                    compare_map[int(fl1_cell_info.center_x), int(fl1_cell_info.center_y), 1] = 0
                    exist_fl1 = True
                    fl1_index = FL1_channel[1] - growh_num
                    break
            for FL2_channel in channelroi[np.nonzero(channelroi[:, :, 2])]:
                fl2_cell_info = self.cell_infos[FL2_channel[2] - growh_num].get_channel_info("FL2")
                fl2_box = self.get_RECT(fl2_cell_info, im_shape, padding=0)
                if self.compute_IOU(bf_box, fl2_box) > iou_thred:
                    compare_map[int(fl2_cell_info.center_x), int(fl2_cell_info.center_y), 2] = 0
                    exist_fl2 = True
                    fl2_index = FL2_channel[2] - growh_num
                    break
            for FL3_channel in channelroi[np.nonzero(channelroi[:, :, 3])]:
                fl3_cell_info = self.cell_infos[FL3_channel[3] - growh_num].get_channel_info("FL3")
                fl3_box = self.get_RECT(fl3_cell_info, im_shape, padding=0)
                if self.compute_IOU(bf_box, fl3_box) > iou_thred:
                    compare_map[int(fl3_cell_info.center_x), int(fl3_cell_info.center_y), 3] = 0
                    exist_fl3 = True
                    fl3_index = FL3_channel[3] - growh_num
                    break
            for FL4_channel in channelroi[np.nonzero(channelroi[:, :, 4])]:
                fl4_cell_info = self.cell_infos[FL4_channel[4] - growh_num].get_channel_info("FL4")
                fl4_box = self.get_RECT(fl4_cell_info, im_shape, padding=0)
                if self.compute_IOU(bf_box, fl4_box) > iou_thred:
                    compare_map[int(fl4_cell_info.center_x), int(fl4_cell_info.center_y), 4] = 0
                    exist_fl4 = True
                    fl4_index = FL4_channel[4] - growh_num
                    break
            compare_map[int(bf_cell_info.center_x), int(bf_cell_info.center_y), 0] = 0
            if exist_fl1 == True and exist_fl2 == True and exist_fl3 == True and exist_fl4 == True:
                BF_FL1_FL2_FL3_FL4.append([bfchannel_index[0] - growh_num, fl1_index, fl2_index, fl3_index, fl4_index])
            elif exist_fl1 == True and exist_fl2 == True and exist_fl3 == True:
                BF_FL1_FL2_FL3.append([bfchannel_index[0] - growh_num, fl1_index, fl2_index, fl3_index])
            elif exist_fl1 == True and exist_fl2 == True and exist_fl4 == True:
                BF_FL1_FL2_FL4.append([bfchannel_index[0] - growh_num, fl1_index, fl2_index, fl4_index])
            elif exist_fl1 == True and exist_fl3 == True and exist_fl4 == True:
                BF_FL1_FL3_FL4.append([bfchannel_index[0] - growh_num, fl1_index, fl3_index, fl4_index])
            elif exist_fl2 == True and exist_fl3 == True and exist_fl4 == True:
                BF_FL2_FL3_FL4.append([bfchannel_index[0] - growh_num, fl2_index, fl3_index, fl4_index])
            elif exist_fl1 == True and exist_fl2 == True:
                BF_FL1_FL2.append([bfchannel_index[0] - growh_num, fl1_index, fl2_index])
            elif exist_fl1 == True and exist_fl3 == True:
                BF_FL1_FL3.append([bfchannel_index[0] - growh_num, fl1_index, fl3_index])
            elif exist_fl1 == True and exist_fl4 == True:
                BF_FL1_FL4.append([bfchannel_index[0] - growh_num, fl1_index, fl4_index])
            elif exist_fl2 == True and exist_fl3 == True:
                BF_FL2_FL3.append([bfchannel_index[0] - growh_num, fl2_index, fl3_index])
            elif exist_fl2 == True and exist_fl4 == True:
                BF_FL2_FL4.append([bfchannel_index[0] - growh_num, fl2_index, fl4_index])
            elif exist_fl3 == True and exist_fl4 == True:
                BF_FL3_FL4.append([bfchannel_index[0] - growh_num, fl3_index, fl4_index])
            elif exist_fl1:
                BF_FL1.append([bfchannel_index[0] - growh_num, fl1_index])
            elif exist_fl2:
                BF_FL2.append([bfchannel_index[0] - growh_num, fl2_index])
            elif exist_fl3:
                BF_FL3.append([bfchannel_index[0] - growh_num, fl3_index])
            elif exist_fl4:
                BF_FL4.append([bfchannel_index[0] - growh_num, fl4_index])
            else:
                BF.extend([bfchannel_index[0] - growh_num])
        for fl1channel_index in compare_map[np.nonzero(compare_map[:, :, 1])]:
            fl1_cell_info = self.cell_infos[fl1channel_index[1] - growh_num].get_channel_info("FL1")
            fl1_roi_extend_box = self.get_RECT(fl1_cell_info, im_shape, padding=fl1_cell_info.radius)
            fl1_box = self.get_RECT(fl1_cell_info, im_shape, padding=0)
            channelroi = compare_map[fl1_roi_extend_box[0]:fl1_roi_extend_box[2],
                         fl1_roi_extend_box[1]:fl1_roi_extend_box[3]]
            exist_fl2, exist_fl3, exist_fl4 = False, False, False
            fl2_index, fl3_index, fl4_index = -1, -1, -1
            for FL2_channel in channelroi[np.nonzero(channelroi[:, :, 2])]:
                fl2_cell_info = self.cell_infos[FL2_channel[2] - growh_num].get_channel_info("FL2")
                fl2_box = self.get_RECT(fl2_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl1_box, fl2_box) > iou_thred:
                    compare_map[int(fl2_cell_info.center_x), int(fl2_cell_info.center_y), 2] = 0
                    exist_fl2 = True
                    fl2_index = FL2_channel[2] - growh_num
                    break
            for FL3_channel in channelroi[np.nonzero(channelroi[:, :, 3])]:
                fl3_cell_info = self.cell_infos[FL3_channel[3] - growh_num].get_channel_info("FL3")
                fl3_box = self.get_RECT(fl3_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl1_box, fl3_box) > iou_thred:
                    compare_map[int(fl3_cell_info.center_x), int(fl3_cell_info.center_y), 3] = 0
                    exist_fl3 = True
                    fl3_index = FL3_channel[3] - growh_num
                    break
            for FL4_channel in channelroi[np.nonzero(channelroi[:, :, 4])]:
                fl4_cell_info = self.cell_infos[FL4_channel[4] - growh_num].get_channel_info("FL4")
                fl4_box = self.get_RECT(fl4_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl1_box, fl4_box) > iou_thred:
                    compare_map[int(fl4_cell_info.center_x), int(fl4_cell_info.center_y), 4] = 0
                    exist_fl4 = True
                    fl4_index = FL4_channel[4] - growh_num
                    break
            compare_map[int(fl1_cell_info.center_x), int(fl1_cell_info.center_y), 1] = 0
            if exist_fl2 == True and exist_fl3 == True and exist_fl4 == True:
                FL1_FL2_FL3_FL4.append([fl1channel_index[1] - growh_num, fl2_index, fl3_index, fl4_index])
            elif exist_fl2 == True and exist_fl3 == True:
                FL1_FL2_FL3.append([fl1channel_index[1] - growh_num, fl2_index, fl3_index])
            elif exist_fl2 == True and exist_fl4 == True:
                FL1_FL2_FL4.append([fl1channel_index[1] - growh_num, fl2_index, fl4_index])
            elif exist_fl3 == True and exist_fl4 == True:
                FL1_FL3_FL4.append([fl1channel_index[1] - growh_num, fl3_index, fl4_index])
            elif exist_fl2:
                FL1_FL2.append([fl1channel_index[1] - growh_num, fl2_index])
            elif exist_fl3:
                FL1_FL3.append([fl1channel_index[1] - growh_num, fl3_index])
            elif exist_fl4:
                FL1_FL4.append([fl1channel_index[1] - growh_num, fl4_index])
            else:
                FL1.extend([fl1channel_index[1] - growh_num])
        for fl2channel_index in compare_map[np.nonzero(compare_map[:, :, 2])]:
            fl2_cell_info = self.cell_infos[fl2channel_index[2] - growh_num].get_channel_info("FL2")
            fl2_roi_extend_box = self.get_RECT(fl2_cell_info, im_shape, padding=fl2_cell_info.radius)
            fl2_box = self.get_RECT(fl2_cell_info, im_shape, padding=0)
            channelroi = compare_map[fl2_roi_extend_box[0]:fl2_roi_extend_box[2],
                         fl2_roi_extend_box[1]:fl2_roi_extend_box[3]]
            exist_fl3, exist_fl4 = False, False
            fl3_index, fl4_index = -1, -1
            for FL3_channel in channelroi[np.nonzero(channelroi[:, :, 3])]:
                fl3_cell_info = self.cell_infos[FL3_channel[3] - growh_num].get_channel_info("FL3")
                fl3_box = self.get_RECT(fl3_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl2_box, fl3_box) > iou_thred:
                    compare_map[int(fl3_cell_info.center_x), int(fl3_cell_info.center_y), 3] = 0
                    exist_fl3 = True
                    fl3_index = FL3_channel[3] - growh_num
                    break
            for FL4_channel in channelroi[np.nonzero(channelroi[:, :, 4])]:
                fl4_cell_info = self.cell_infos[FL4_channel[4] - growh_num].get_channel_info("FL4")
                fl4_box = self.get_RECT(fl4_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl2_box, fl4_box) > iou_thred:
                    compare_map[int(fl4_cell_info.center_x), int(fl4_cell_info.center_y), 4] = 0
                    exist_fl4 = True
                    fl4_index = FL4_channel[4] - growh_num
                    break
            compare_map[int(fl2_cell_info.center_x), int(fl2_cell_info.center_y), 2] = 0
            if exist_fl3 == True and exist_fl4 == True:
                FL2_FL3_FL4.append([fl2channel_index[2] - growh_num, fl3_index, fl4_index])
            elif exist_fl3:
                FL2_FL3.append([fl2channel_index[2] - growh_num, fl3_index])
            elif exist_fl4:
                FL2_FL4.append([fl2channel_index[2] - growh_num, fl4_index])
            else:
                FL2.extend([fl2channel_index[2] - growh_num])
        for fl3channel_index in compare_map[np.nonzero(compare_map[:, :, 3])]:
            fl3_cell_info = self.cell_infos[fl3channel_index[3] - growh_num].get_channel_info("FL3")
            fl3_roi_extend_box = self.get_RECT(fl3_cell_info, im_shape, padding=fl3_cell_info.radius)
            fl3_box = self.get_RECT(fl3_cell_info, im_shape, padding=0)
            channelroi = compare_map[fl3_roi_extend_box[0]:fl3_roi_extend_box[2],
                         fl3_roi_extend_box[1]:fl3_roi_extend_box[3]]
            exist_fl4 = False
            fl4_index = -1
            for FL4_channel in channelroi[np.nonzero(channelroi[:, :, 4])]:
                fl4_cell_info = self.cell_infos[FL4_channel[4] - growh_num].get_channel_info("FL4")
                fl4_box = self.get_RECT(fl4_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl3_box, fl4_box) > iou_thred:
                    compare_map[int(fl4_cell_info.center_x), int(fl4_cell_info.center_y), 4] = 0
                    exist_fl4 = True
                    fl4_index = FL4_channel[4] - growh_num
                    break
            compare_map[int(fl3_cell_info.center_x), int(fl3_cell_info.center_y), 3] = 0
            if exist_fl4:
                FL3_FL4.append([fl3channel_index[3] - growh_num, fl4_index])
            else:
                FL3.extend([fl3channel_index[3] - growh_num])
        BF.extend((compare_map[:, :, 0][np.nonzero(compare_map[:, :, 0])] - growh_num).tolist())
        FL1.extend((compare_map[:, :, 1][np.nonzero(compare_map[:, :, 1])] - growh_num).tolist())
        FL2.extend((compare_map[:, :, 2][np.nonzero(compare_map[:, :, 2])] - growh_num).tolist())
        FL3.extend((compare_map[:, :, 3][np.nonzero(compare_map[:, :, 3])] - growh_num).tolist())
        FL4.extend((compare_map[:, :, 4][np.nonzero(compare_map[:, :, 4])] - growh_num).tolist())
        print(fcs_output,[len(i) for i in [BF, BF_FL1, BF_FL2, BF_FL3, BF_FL4, FL1, FL2, FL3, FL4, FL1_FL2, FL1_FL3, FL1_FL4, FL2_FL3, FL2_FL4, FL3_FL4, BF_FL1_FL2,
                                           BF_FL1_FL3, BF_FL1_FL4, BF_FL2_FL3, BF_FL2_FL4, BF_FL3_FL4, FL1_FL2_FL3, FL1_FL2_FL4, FL1_FL3_FL4, FL2_FL3_FL4, BF_FL1_FL2_FL3,
                                           BF_FL1_FL2_FL4, BF_FL1_FL3_FL4, BF_FL2_FL3_FL4, FL1_FL2_FL3_FL4]])
        if fcs_output:
            new_cell_infos = []
            bfinfo=fl1info=fl2info=fl3info=fl4info=None
            for cell_number in BF:
                cell_shuxing = 1
                bfinfo = self.cell_infos[int(cell_number)].get_channel_info("BF")
                bfinfo.type = cell_shuxing
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                           xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                           xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                           xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (bfinfo.copy()).get_det_info(img=fl4_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                           xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, bfinfo)
                new_cell_infos.append(cell_struct(BF=bfinfo, FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_number in FL1:
                cell_shuxing = 2
                fl1info = self.cell_infos[int(cell_number)].get_channel_info("FL1")
                fl1info.type = cell_shuxing
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl1info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl1info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl1info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl1info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl1info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo, FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_number in FL2:
                cell_shuxing = 3
                fl2info = self.cell_infos[int(cell_number)].get_channel_info("FL2")
                fl2info.type = cell_shuxing
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl2info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                          xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl2info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl2info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl2info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl2info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl2info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl2info)
                new_cell_infos.append(cell_struct(BF=bfinfo, FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_number in FL3:
                cell_shuxing = 4
                fl3info = self.cell_infos[int(cell_number)].get_channel_info("FL3")
                fl3info.type = cell_shuxing
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl3info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                          xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl3info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                           xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl3info)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl3info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                           xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl3info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl3info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                           xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl3info)
                new_cell_infos.append(cell_struct(BF=bfinfo, FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_number in FL4:
                cell_shuxing = 5
                fl4info = self.cell_infos[int(cell_number)].get_channel_info("FL4")
                fl4info.type = cell_shuxing
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl4info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl4info.tx), xyxy_1=int(fl4info.ty),
                                                          xyxy_2=int(fl4info.bx), xyxy_3=int(fl4info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl4info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl4info.tx), xyxy_1=int(fl4info.ty),
                                                           xyxy_2=int(fl4info.bx), xyxy_3=int(fl4info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl4info)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl4info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl4info.tx), xyxy_1=int(fl4info.ty),
                                                           xyxy_2=int(fl4info.bx), xyxy_3=int(fl4info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl4info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl4info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl4info.tx), xyxy_1=int(fl4info.ty),
                                                           xyxy_2=int(fl4info.bx), xyxy_3=int(fl4info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl4info)
                new_cell_infos.append(cell_struct(BF=bfinfo, FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_list in BF_FL1:
                cell_shuxing = 6
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                fl1info = self.cell_infos[int(cell_list[1])].get_channel_info("FL1")
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl1info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl1info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl1info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl1info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl1info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl1info)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1")
                                                  , FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_list in BF_FL2:
                cell_shuxing = 7
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                fl2info = self.cell_infos[int(cell_list[1])].get_channel_info("FL2")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl2info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                            xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl2info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl2info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                            xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl2info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl2info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                            xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl2info)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=fl1info, FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2")
                                                  , FL3=fl3info, FL4=fl4info))
            for cell_list in BF_FL3:
                cell_shuxing = 8
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                fl3info = self.cell_infos[int(cell_list[1])].get_channel_info("FL3")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl3info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                            xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl3info)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl3info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                            xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl3info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl3info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                            xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl3info)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=fl1info, FL2=fl2info, FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3")
                                                  , FL4=fl4info))
            for cell_list in BF_FL4:
                cell_shuxing = 9
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                bfinfo = self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                            xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                            xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                            xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL1_FL2:
                cell_shuxing = 10
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                fl1info = self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl1info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl1info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info= (fl1info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=fl3info, FL4=fl4info))
            for cell_list in FL1_FL3:
                cell_shuxing = 11
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                fl1info = self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl1info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl1info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl1info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=fl2info, FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=fl4info))
            for cell_list in FL1_FL4:
                cell_shuxing = 12
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                fl1info = self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl1info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl1info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl1info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=fl2info, FL3=fl3info, FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL2_FL3:
                cell_shuxing = 13
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                fl2info = self.cell_infos[int(cell_list[0])].get_channel_info("FL2")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl2info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl2info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                        xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl2info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl2info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                        xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl2info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=fl1info,FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                                FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=fl4info))
            for cell_list in FL2_FL4:
                cell_shuxing = 14
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                fl2info = self.cell_infos[int(cell_list[0])].get_channel_info("FL2")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl2info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl2info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                        xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl2info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl2info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                        xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl2info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=fl1info,FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                                  FL3=fl3info, FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL3_FL4:
                cell_shuxing = 15
                self.cell_infos[int(cell_list[0])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                fl3info = self.cell_infos[int(cell_list[0])].get_channel_info("FL3")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl3info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                           xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info=(fl3info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                        xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl3info)
                if isinstance(fl2_img, np.ndarray):
                    fl2info=(fl3info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                        xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl3info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=fl1info,FL3=self.cell_infos[int(cell_list[0])].get_channel_info("FL3"),
                                                FL2=fl2info, FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2:
                cell_shuxing = 16
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (bfinfo.copy()).get_det_info(img=fl4_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                              FL3=fl3info, FL4=fl4info))

            for cell_list in BF_FL1_FL3:
                cell_shuxing = 17
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (bfinfo.copy()).get_det_info(img=fl4_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=fl2info,
                                              FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                              FL4=fl4info))

            for cell_list in BF_FL1_FL4:
                cell_shuxing = 18
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=fl2info,
                                              FL3=fl3info,
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))

            for cell_list in BF_FL2_FL3:
                cell_shuxing = 19
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (bfinfo.copy()).get_det_info(img=fl4_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=fl1info,
                                              FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                              FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                              FL4=fl4info))

            for cell_list in BF_FL2_FL4:
                cell_shuxing = 20
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=fl1info,
                                              FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                              FL3=fl3info,
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))

            for cell_list in BF_FL3_FL4:
                cell_shuxing = 21
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=fl1info,
                                              FL2=fl2info,
                                              FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))

            for cell_list in FL1_FL2_FL3:
                cell_shuxing = 22
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                fl1info=self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl1info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=fl4info))

            for cell_list in FL1_FL2_FL4:
                cell_shuxing = 23
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                fl1info=self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl3_img, np.ndarray):
                    fl3info=(fl1info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,
                                              FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                              FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                              FL3=fl3info,
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in FL1_FL3_FL4:
                cell_shuxing = 24
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                fl1info=self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl2_img, np.ndarray):
                    fl2info=(fl1info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,
                                              FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                              FL2=fl2info,
                                              FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))

            for cell_list in FL2_FL3_FL4:
                cell_shuxing = 25
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                fl2info=self.cell_infos[int(cell_list[0])].get_channel_info("FL2")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl2info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    bfinfo.local_channel = 0
                    bfinfo = replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info=(fl2info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                        xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl2info)
                new_cell_infos.append(cell_struct(BF=bfinfo,
                                              FL1=fl1info,
                                              FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                              FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2_FL3:
                cell_shuxing = 26
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL3").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl4_img, np.ndarray):
                    fl4info=(bfinfo.copy()).get_det_info(img=fl4_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                              FL3=self.cell_infos[int(cell_list[3])].get_channel_info("FL3"),
                                              FL4=fl4info))

            for cell_list in BF_FL1_FL2_FL4:
                cell_shuxing = 27
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl3_img, np.ndarray):
                    fl3info=(bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                              FL3=fl3info,
                                              FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))

            for cell_list in BF_FL1_FL3_FL4:
                cell_shuxing = 28
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl2_img, np.ndarray):
                    fl2info=(bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=fl2info,
                                              FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))

            for cell_list in BF_FL2_FL3_FL4:
                cell_shuxing = 29
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl1_img, np.ndarray):
                    fl1info=(bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=fl1info,
                                              FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                              FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))
            for cell_list in FL1_FL2_FL3_FL4:
                cell_shuxing = 30
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                fl1info=self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo=(fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                    xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                new_cell_infos.append(cell_struct(BF=bfinfo,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2_FL3_FL4:
                cell_shuxing = 31
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[4])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                              FL3=self.cell_infos[int(cell_list[3])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[4])].get_channel_info("FL4")))
        else:
            new_cell_infos = []
            for cell_number in BF:
                cell_shuxing = 1
                self.cell_infos[int(cell_number)].get_channel_info("BF").type = cell_shuxing
                new_cell_infos.append(self.cell_infos[int(cell_number)])
            for cell_number in FL1:
                cell_shuxing = 2
                self.cell_infos[int(cell_number)].get_channel_info("FL1").type = cell_shuxing
                new_cell_infos.append(self.cell_infos[int(cell_number)])
            for cell_number in FL2:
                cell_shuxing = 3
                self.cell_infos[int(cell_number)].get_channel_info("FL2").type = cell_shuxing
                new_cell_infos.append(self.cell_infos[int(cell_number)])
            for cell_number in FL3:
                cell_shuxing = 4
                self.cell_infos[int(cell_number)].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(self.cell_infos[int(cell_number)])
            for cell_number in FL4:
                cell_shuxing = 5
                self.cell_infos[int(cell_number)].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(self.cell_infos[int(cell_number)])
            for cell_list in BF_FL1:
                cell_shuxing = 6
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1")
                                                  , FL2=None,FL3=None,FL4=None))
            for cell_list in BF_FL2:
                cell_shuxing = 7
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=None))
            for cell_list in BF_FL3:
                cell_shuxing = 8
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in BF_FL4:
                cell_shuxing = 9
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=None,
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL1_FL2:
                cell_shuxing = 10
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=None))
            for cell_list in FL1_FL3:
                cell_shuxing = 11
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in FL1_FL4:
                cell_shuxing = 12
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL2_FL3:
                cell_shuxing = 13
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in FL2_FL4:
                cell_shuxing = 14
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL3_FL4:
                cell_shuxing = 15
                self.cell_infos[int(cell_list[0])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=None,
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[0])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2:
                cell_shuxing = 16
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=None))
            for cell_list in BF_FL1_FL3:
                cell_shuxing = 17
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in BF_FL1_FL4:
                cell_shuxing = 18
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in BF_FL2_FL3:
                cell_shuxing = 19
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in BF_FL2_FL4:
                cell_shuxing = 20
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in BF_FL3_FL4:
                cell_shuxing = 21
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in FL1_FL2_FL3:
                cell_shuxing = 22
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in FL1_FL2_FL4:
                cell_shuxing = 23
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))

            for cell_list in FL1_FL3_FL4:
                cell_shuxing = 24
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in FL2_FL3_FL4:
                cell_shuxing = 25
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2_FL3:
                cell_shuxing = 26
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[3])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in BF_FL1_FL2_FL4:
                cell_shuxing = 27
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL3_FL4:
                cell_shuxing = 28
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))
            for cell_list in BF_FL2_FL3_FL4:
                cell_shuxing = 29
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))

            for cell_list in FL1_FL2_FL3_FL4:
                cell_shuxing = 30
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2_FL3_FL4:
                cell_shuxing = 31
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[4])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[3])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[4])].get_channel_info("FL4")))
        self.cell_infos = new_cell_infos
        return self
    def get_shuxing5_mode(self, im_shape, same_cell_iou_thred=0.3, bf_img=None, fl1_img=None, fl2_img=None, fl3_img=None,
                     fl4_img=None, fcs_output=False):
        compare_map = np.zeros([im_shape[1], im_shape[0], 5])
        growh_num = 17
        for cell_num, cell in enumerate(self.cell_infos):
            for channel in cell.cell_info:
                if channel is not None:
                    compare_map[channel.center_x, channel.center_y, channel.local_channel] = cell_num + growh_num
        iou_thred = same_cell_iou_thred
        BF_FL1_FL2_FL3_FL4_array = compare_map[np.where((compare_map[:, :, 0] > 0) & (compare_map[:, :, 1] > 0) & (compare_map[:, :, 2] > 0) & (compare_map[:, :, 3] > 0) & (compare_map[:, :, 4] > 0))]
        BF_FL1_FL2_FL3_FL4 = (BF_FL1_FL2_FL3_FL4_array - growh_num).tolist()
        BF, BF_FL1, BF_FL2, BF_FL3, BF_FL4, FL1, FL2, FL3, FL4, FL1_FL2, FL1_FL3, FL1_FL4, FL2_FL3, FL2_FL4, FL3_FL4, BF_FL1_FL2, \
        BF_FL1_FL3, BF_FL1_FL4, BF_FL2_FL3, BF_FL2_FL4, BF_FL3_FL4, FL1_FL2_FL3, FL1_FL2_FL4, FL1_FL3_FL4, FL2_FL3_FL4, BF_FL1_FL2_FL3, \
        BF_FL1_FL2_FL4, BF_FL1_FL3_FL4, BF_FL2_FL3_FL4, FL1_FL2_FL3_FL4 = [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], \
        [], [], [], [], [], [], [], [], [], [], [], [], []
        compare_map[np.where((compare_map[:, :, 0] > 0) & (compare_map[:, :, 1] > 0) & (compare_map[:, :, 2] > 0) & (compare_map[:, :, 3] > 0) & (compare_map[:, :, 4] > 0))] = 0
        compare_map = compare_map.astype(int)
        for fl1channel_index in compare_map[np.nonzero(compare_map[:, :, 1])]:
            fl1_cell_info = self.cell_infos[fl1channel_index[1] - growh_num].get_channel_info("FL1")
            fl1_roi_extend_box = self.get_RECT(fl1_cell_info, im_shape, padding=fl1_cell_info.radius)
            fl1_box = self.get_RECT(fl1_cell_info, im_shape, padding=0)
            channelroi = compare_map[fl1_roi_extend_box[0]:fl1_roi_extend_box[2],
                         fl1_roi_extend_box[1]:fl1_roi_extend_box[3]]
            exist_fl2, exist_fl3, exist_fl4 = False, False, False
            fl2_index, fl3_index, fl4_index = -1, -1, -1
            for FL2_channel in channelroi[np.nonzero(channelroi[:, :, 2])]:
                fl2_cell_info = self.cell_infos[FL2_channel[2] - growh_num].get_channel_info("FL2")
                fl2_box = self.get_RECT(fl2_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl1_box, fl2_box) > iou_thred:
                    compare_map[int(fl2_cell_info.center_x), int(fl2_cell_info.center_y), 2] = 0
                    exist_fl2 = True
                    fl2_index = FL2_channel[2] - growh_num
                    break
            for FL3_channel in channelroi[np.nonzero(channelroi[:, :, 3])]:
                fl3_cell_info = self.cell_infos[FL3_channel[3] - growh_num].get_channel_info("FL3")
                fl3_box = self.get_RECT(fl3_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl1_box, fl3_box) > iou_thred:
                    compare_map[int(fl3_cell_info.center_x), int(fl3_cell_info.center_y), 3] = 0
                    exist_fl3 = True
                    fl3_index = FL3_channel[3] - growh_num
                    break
            for FL4_channel in channelroi[np.nonzero(channelroi[:, :, 4])]:
                fl4_cell_info = self.cell_infos[FL4_channel[4] - growh_num].get_channel_info("FL4")
                fl4_box = self.get_RECT(fl4_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl1_box, fl4_box) > iou_thred:
                    compare_map[int(fl4_cell_info.center_x), int(fl4_cell_info.center_y), 4] = 0
                    exist_fl4 = True
                    fl4_index = FL4_channel[4] - growh_num
                    break
            compare_map[int(fl1_cell_info.center_x), int(fl1_cell_info.center_y), 1] = 0
            if exist_fl2 == True and exist_fl3 == True and exist_fl4 == True:
                FL1_FL2_FL3_FL4.append([fl1channel_index[1] - growh_num, fl2_index, fl3_index, fl4_index])
            elif exist_fl2 == True and exist_fl3 == True:
                FL1_FL2_FL3.append([fl1channel_index[1] - growh_num, fl2_index, fl3_index])
            elif exist_fl2 == True and exist_fl4 == True:
                FL1_FL2_FL4.append([fl1channel_index[1] - growh_num, fl2_index, fl4_index])
            elif exist_fl3 == True and exist_fl4 == True:
                FL1_FL3_FL4.append([fl1channel_index[1] - growh_num, fl3_index, fl4_index])
            elif exist_fl2:
                FL1_FL2.append([fl1channel_index[1] - growh_num, fl2_index])
            elif exist_fl3:
                FL1_FL3.append([fl1channel_index[1] - growh_num, fl3_index])
            elif exist_fl4:
                FL1_FL4.append([fl1channel_index[1] - growh_num, fl4_index])
            else:
                FL1.extend([fl1channel_index[1] - growh_num])
        for fl2channel_index in compare_map[np.nonzero(compare_map[:, :, 2])]:
            fl2_cell_info = self.cell_infos[fl2channel_index[2] - growh_num].get_channel_info("FL2")
            fl2_roi_extend_box = self.get_RECT(fl2_cell_info, im_shape, padding=fl2_cell_info.radius)
            fl2_box = self.get_RECT(fl2_cell_info, im_shape, padding=0)
            channelroi = compare_map[fl2_roi_extend_box[0]:fl2_roi_extend_box[2],
                         fl2_roi_extend_box[1]:fl2_roi_extend_box[3]]
            exist_fl3, exist_fl4 = False, False
            fl3_index, fl4_index = -1, -1
            for FL3_channel in channelroi[np.nonzero(channelroi[:, :, 3])]:
                fl3_cell_info = self.cell_infos[FL3_channel[3] - growh_num].get_channel_info("FL3")
                fl3_box = self.get_RECT(fl3_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl2_box, fl3_box) > iou_thred:
                    compare_map[int(fl3_cell_info.center_x), int(fl3_cell_info.center_y), 3] = 0
                    exist_fl3 = True
                    fl3_index = FL3_channel[3] - growh_num
                    break
            for FL4_channel in channelroi[np.nonzero(channelroi[:, :, 4])]:
                fl4_cell_info = self.cell_infos[FL4_channel[4] - growh_num].get_channel_info("FL4")
                fl4_box = self.get_RECT(fl4_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl2_box, fl4_box) > iou_thred:
                    compare_map[int(fl4_cell_info.center_x), int(fl4_cell_info.center_y), 4] = 0
                    exist_fl4 = True
                    fl4_index = FL4_channel[4] - growh_num
                    break
            compare_map[int(fl2_cell_info.center_x), int(fl2_cell_info.center_y), 2] = 0
            if exist_fl3 == True and exist_fl4 == True:
                FL2_FL3_FL4.append([fl2channel_index[2] - growh_num, fl3_index, fl4_index])
            elif exist_fl3:
                FL2_FL3.append([fl2channel_index[2] - growh_num, fl3_index])
            elif exist_fl4:
                FL2_FL4.append([fl2channel_index[2] - growh_num, fl4_index])
            else:
                FL2.extend([fl2channel_index[2] - growh_num])
        for fl3channel_index in compare_map[np.nonzero(compare_map[:, :, 3])]:
            fl3_cell_info = self.cell_infos[fl3channel_index[3] - growh_num].get_channel_info("FL3")
            fl3_roi_extend_box = self.get_RECT(fl3_cell_info, im_shape, padding=fl3_cell_info.radius)
            fl3_box = self.get_RECT(fl3_cell_info, im_shape, padding=0)
            channelroi = compare_map[fl3_roi_extend_box[0]:fl3_roi_extend_box[2],
                         fl3_roi_extend_box[1]:fl3_roi_extend_box[3]]
            exist_fl4 = False
            fl4_index = -1
            for FL4_channel in channelroi[np.nonzero(channelroi[:, :, 4])]:
                fl4_cell_info = self.cell_infos[FL4_channel[4] - growh_num].get_channel_info("FL4")
                fl4_box = self.get_RECT(fl4_cell_info, im_shape, padding=0)
                if self.compute_IOU(fl3_box, fl4_box) > iou_thred:
                    compare_map[int(fl4_cell_info.center_x), int(fl4_cell_info.center_y), 4] = 0
                    exist_fl4 = True
                    fl4_index = FL4_channel[4] - growh_num
                    break
            compare_map[int(fl3_cell_info.center_x), int(fl3_cell_info.center_y), 3] = 0
            if exist_fl4:
                FL3_FL4.append([fl3channel_index[3] - growh_num, fl4_index])
            else:
                FL3.extend([fl3channel_index[3] - growh_num])
        BF.extend((compare_map[:, :, 0][np.nonzero(compare_map[:, :, 0])] - growh_num).tolist())
        FL1.extend((compare_map[:, :, 1][np.nonzero(compare_map[:, :, 1])] - growh_num).tolist())
        FL2.extend((compare_map[:, :, 2][np.nonzero(compare_map[:, :, 2])] - growh_num).tolist())
        FL3.extend((compare_map[:, :, 3][np.nonzero(compare_map[:, :, 3])] - growh_num).tolist())
        FL4.extend((compare_map[:, :, 4][np.nonzero(compare_map[:, :, 4])] - growh_num).tolist())
        print(fcs_output,[len(i) for i in [BF, BF_FL1, BF_FL2, BF_FL3, BF_FL4, FL1, FL2, FL3, FL4, FL1_FL2, FL1_FL3, FL1_FL4, FL2_FL3, FL2_FL4, FL3_FL4, BF_FL1_FL2,
                                           BF_FL1_FL3, BF_FL1_FL4, BF_FL2_FL3, BF_FL2_FL4, BF_FL3_FL4, FL1_FL2_FL3, FL1_FL2_FL4, FL1_FL3_FL4, FL2_FL3_FL4, BF_FL1_FL2_FL3,
                                           BF_FL1_FL2_FL4, BF_FL1_FL3_FL4, BF_FL2_FL3_FL4, FL1_FL2_FL3_FL4]])
        if fcs_output:
            new_cell_infos = []
            bfinfo=fl1info=fl2info=fl3info=fl4info=None
            for cell_number in BF:
                cell_shuxing = 1
                bfinfo = self.cell_infos[int(cell_number)].get_channel_info("BF")
                bfinfo.type = cell_shuxing
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                           xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                           xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                           xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (bfinfo.copy()).get_det_info(img=fl4_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                           xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, bfinfo)
                new_cell_infos.append(cell_struct(BF=bfinfo, FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_number in FL1:
                cell_shuxing = 2
                fl1info = self.cell_infos[int(cell_number)].get_channel_info("FL1")
                fl1info.type = cell_shuxing
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl1info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl1info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl1info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl1info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl1info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo, FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_number in FL2:
                cell_shuxing = 3
                fl2info = self.cell_infos[int(cell_number)].get_channel_info("FL2")
                fl2info.type = cell_shuxing
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl2info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                          xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl2info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl2info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl2info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl2info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl2info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl2info)
                new_cell_infos.append(cell_struct(BF=bfinfo, FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_number in FL3:
                cell_shuxing = 4
                fl3info = self.cell_infos[int(cell_number)].get_channel_info("FL3")
                fl3info.type = cell_shuxing
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl3info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                          xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl3info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                           xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl3info)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl3info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                           xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl3info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl3info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                           xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl3info)
                new_cell_infos.append(cell_struct(BF=bfinfo, FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_number in FL4:
                cell_shuxing = 5
                fl4info = self.cell_infos[int(cell_number)].get_channel_info("FL4")
                fl4info.type = cell_shuxing
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl4info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl4info.tx), xyxy_1=int(fl4info.ty),
                                                          xyxy_2=int(fl4info.bx), xyxy_3=int(fl4info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl4info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl4info.tx), xyxy_1=int(fl4info.ty),
                                                           xyxy_2=int(fl4info.bx), xyxy_3=int(fl4info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl4info)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl4info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl4info.tx), xyxy_1=int(fl4info.ty),
                                                           xyxy_2=int(fl4info.bx), xyxy_3=int(fl4info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl4info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl4info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl4info.tx), xyxy_1=int(fl4info.ty),
                                                           xyxy_2=int(fl4info.bx), xyxy_3=int(fl4info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl4info)
                new_cell_infos.append(cell_struct(BF=bfinfo, FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_list in BF_FL1:
                cell_shuxing = 6
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                fl1info = self.cell_infos[int(cell_list[1])].get_channel_info("FL1")
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl1info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl1info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl1info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl1info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl1info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                            xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl1info)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1")
                                                  , FL2=fl2info, FL3=fl3info, FL4=fl4info))
            for cell_list in BF_FL2:
                cell_shuxing = 7
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                fl2info = self.cell_infos[int(cell_list[1])].get_channel_info("FL2")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl2info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                            xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl2info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl2info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                            xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl2info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl2info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                            xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl2info)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=fl1info, FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2")
                                                  , FL3=fl3info, FL4=fl4info))
            for cell_list in BF_FL3:
                cell_shuxing = 8
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                fl3info = self.cell_infos[int(cell_list[1])].get_channel_info("FL3")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl3info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                            xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl3info)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl3info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                            xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl3info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl3info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                            xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl3info)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=fl1info, FL2=fl2info, FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3")
                                                  , FL4=fl4info))
            for cell_list in BF_FL4:
                cell_shuxing = 9
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                bfinfo = self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                            xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                            xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                            xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=fl1info, FL2=fl2info, FL3=fl3info, FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL1_FL2:
                cell_shuxing = 10
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                fl1info = self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl1info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl1info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info= (fl1info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=fl3info, FL4=fl4info))
            for cell_list in FL1_FL3:
                cell_shuxing = 11
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                fl1info = self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl1info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl1info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl1info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=fl2info, FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=fl4info))
            for cell_list in FL1_FL4:
                cell_shuxing = 12
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                fl1info = self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (fl1info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl1info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl1info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=fl2info, FL3=fl3info, FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL2_FL3:
                cell_shuxing = 13
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                fl2info = self.cell_infos[int(cell_list[0])].get_channel_info("FL2")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl2info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl2info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                        xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl2info)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl2info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                        xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl2info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=fl1info,FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                                FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=fl4info))
            for cell_list in FL2_FL4:
                cell_shuxing = 14
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                fl2info = self.cell_infos[int(cell_list[0])].get_channel_info("FL2")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl2info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (fl2info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                        xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl2info)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (fl2info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                        xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl2info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=fl1info,FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                                  FL3=fl3info, FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL3_FL4:
                cell_shuxing = 15
                self.cell_infos[int(cell_list[0])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                fl3info = self.cell_infos[int(cell_list[0])].get_channel_info("FL3")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl3info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                           xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info=(fl3info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                        xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl3info)
                if isinstance(fl2_img, np.ndarray):
                    fl2info=(fl3info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl3info.tx), xyxy_1=int(fl3info.ty),
                                                        xyxy_2=int(fl3info.bx), xyxy_3=int(fl3info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl3info)
                new_cell_infos.append(cell_struct(BF=bfinfo,FL1=fl1info,FL3=self.cell_infos[int(cell_list[0])].get_channel_info("FL3"),
                                                FL2=fl2info, FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2:
                cell_shuxing = 16
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (bfinfo.copy()).get_det_info(img=fl4_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                              FL3=fl3info, FL4=fl4info))

            for cell_list in BF_FL1_FL3:
                cell_shuxing = 17
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (bfinfo.copy()).get_det_info(img=fl4_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=fl2info,
                                              FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                              FL4=fl4info))

            for cell_list in BF_FL1_FL4:
                cell_shuxing = 18
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=fl2info,
                                              FL3=fl3info,
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))

            for cell_list in BF_FL2_FL3:
                cell_shuxing = 19
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (bfinfo.copy()).get_det_info(img=fl4_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=fl1info,
                                              FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                              FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                              FL4=fl4info))

            for cell_list in BF_FL2_FL4:
                cell_shuxing = 20
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                if isinstance(fl3_img, np.ndarray):
                    fl3info = (bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=fl1info,
                                              FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                              FL3=fl3info,
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))

            for cell_list in BF_FL3_FL4:
                cell_shuxing = 21
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl1_img, np.ndarray):
                    fl1info = (bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                if isinstance(fl2_img, np.ndarray):
                    fl2info = (bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=fl1info,
                                              FL2=fl2info,
                                              FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))

            for cell_list in FL1_FL2_FL3:
                cell_shuxing = 22
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                fl1info=self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl4_img, np.ndarray):
                    fl4info = (fl1info.copy()).get_det_info(img=fl4_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=fl4info))

            for cell_list in FL1_FL2_FL4:
                cell_shuxing = 23
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                fl1info=self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl3_img, np.ndarray):
                    fl3info=(fl1info.copy()).get_det_info(img=fl3_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,
                                              FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                              FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                              FL3=fl3info,
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in FL1_FL3_FL4:
                cell_shuxing = 24
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                fl1info=self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                           xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl2_img, np.ndarray):
                    fl2info=(fl1info.copy()).get_det_info(img=fl2_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                        xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, fl1info)
                new_cell_infos.append(cell_struct(BF=bfinfo,
                                              FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                              FL2=fl2info,
                                              FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))

            for cell_list in FL2_FL3_FL4:
                cell_shuxing = 25
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                fl2info=self.cell_infos[int(cell_list[0])].get_channel_info("FL2")
                if isinstance(bf_img, np.ndarray):
                    bfinfo = (fl2info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                           xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                if isinstance(fl1_img, np.ndarray):
                    fl1info=(fl2info.copy()).get_det_info(img=fl1_img, xyxy_0=int(fl2info.tx), xyxy_1=int(fl2info.ty),
                                                        xyxy_2=int(fl2info.bx), xyxy_3=int(fl2info.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, fl2info)
                new_cell_infos.append(cell_struct(BF=bfinfo,
                                              FL1=fl1info,
                                              FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                              FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2_FL3:
                cell_shuxing = 26
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL3").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl4_img, np.ndarray):
                    fl4info=(bfinfo.copy()).get_det_info(img=fl4_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl4info.local_channel = 4
                    fl4info = replace(fl4info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                              FL3=self.cell_infos[int(cell_list[3])].get_channel_info("FL3"),
                                              FL4=fl4info))

            for cell_list in BF_FL1_FL2_FL4:
                cell_shuxing = 27
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl3_img, np.ndarray):
                    fl3info=(bfinfo.copy()).get_det_info(img=fl3_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl3info.local_channel = 3
                    fl3info = replace(fl3info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                              FL3=fl3info,
                                              FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))

            for cell_list in BF_FL1_FL3_FL4:
                cell_shuxing = 28
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl2_img, np.ndarray):
                    fl2info=(bfinfo.copy()).get_det_info(img=fl2_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl2info.local_channel = 2
                    fl2info = replace(fl2info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=fl2info,
                                              FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))

            for cell_list in BF_FL2_FL3_FL4:
                cell_shuxing = 29
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                bfinfo=self.cell_infos[int(cell_list[0])].get_channel_info("BF")
                if isinstance(fl1_img, np.ndarray):
                    fl1info=(bfinfo.copy()).get_det_info(img=fl1_img, xyxy_0=int(bfinfo.tx), xyxy_1=int(bfinfo.ty),
                                                        xyxy_2=int(bfinfo.bx), xyxy_3=int(bfinfo.by))
                    fl1info.local_channel = 1
                    fl1info = replace(fl1info, bfinfo)
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=fl1info,
                                              FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                              FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))
            for cell_list in FL1_FL2_FL3_FL4:
                cell_shuxing = 30
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                fl1info=self.cell_infos[int(cell_list[0])].get_channel_info("FL1")
                if isinstance(bf_img, np.ndarray):
                    bfinfo=(fl1info.copy()).get_det_info(img=bf_img, xyxy_0=int(fl1info.tx), xyxy_1=int(fl1info.ty),
                                                    xyxy_2=int(fl1info.bx), xyxy_3=int(fl1info.by))
                    bfinfo.local_channel = 0
                    bfinfo =  replace_zero(bfinfo)
                else:
                    bfinfo = None
                new_cell_infos.append(cell_struct(BF=bfinfo,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2_FL3_FL4:
                cell_shuxing = 31
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[4])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                              FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                              FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                              FL3=self.cell_infos[int(cell_list[3])].get_channel_info("FL3"),
                                              FL4=self.cell_infos[int(cell_list[4])].get_channel_info("FL4")))
        else:
            new_cell_infos = []
            for cell_number in BF:
                cell_shuxing = 1
                self.cell_infos[int(cell_number)].get_channel_info("BF").type = cell_shuxing
                new_cell_infos.append(self.cell_infos[int(cell_number)])
            for cell_number in FL1:
                cell_shuxing = 2
                self.cell_infos[int(cell_number)].get_channel_info("FL1").type = cell_shuxing
                new_cell_infos.append(self.cell_infos[int(cell_number)])
            for cell_number in FL2:
                cell_shuxing = 3
                self.cell_infos[int(cell_number)].get_channel_info("FL2").type = cell_shuxing
                new_cell_infos.append(self.cell_infos[int(cell_number)])
            for cell_number in FL3:
                cell_shuxing = 4
                self.cell_infos[int(cell_number)].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(self.cell_infos[int(cell_number)])
            for cell_number in FL4:
                cell_shuxing = 5
                self.cell_infos[int(cell_number)].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(self.cell_infos[int(cell_number)])
            for cell_list in BF_FL1:
                cell_shuxing = 6
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1")
                                                  , FL2=None,FL3=None,FL4=None))
            for cell_list in BF_FL2:
                cell_shuxing = 7
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=None))
            for cell_list in BF_FL3:
                cell_shuxing = 8
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in BF_FL4:
                cell_shuxing = 9
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=None,
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL1_FL2:
                cell_shuxing = 10
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=None))
            for cell_list in FL1_FL3:
                cell_shuxing = 11
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in FL1_FL4:
                cell_shuxing = 12
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL2_FL3:
                cell_shuxing = 13
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in FL2_FL4:
                cell_shuxing = 14
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in FL3_FL4:
                cell_shuxing = 15
                self.cell_infos[int(cell_list[0])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=None,
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[0])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[1])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2:
                cell_shuxing = 16
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=None))
            for cell_list in BF_FL1_FL3:
                cell_shuxing = 17
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in BF_FL1_FL4:
                cell_shuxing = 18
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in BF_FL2_FL3:
                cell_shuxing = 19
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in BF_FL2_FL4:
                cell_shuxing = 20
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in BF_FL3_FL4:
                cell_shuxing = 21
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in FL1_FL2_FL3:
                cell_shuxing = 22
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in FL1_FL2_FL4:
                cell_shuxing = 23
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))

            for cell_list in FL1_FL3_FL4:
                cell_shuxing = 24
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in FL2_FL3_FL4:
                cell_shuxing = 25
                self.cell_infos[int(cell_list[0])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[0])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[1])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[2])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2_FL3:
                cell_shuxing = 26
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL3").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[3])].get_channel_info("FL3"),
                                                  FL4=None))
            for cell_list in BF_FL1_FL2_FL4:
                cell_shuxing = 27
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                                  FL3=None,
                                                  FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL3_FL4:
                cell_shuxing = 28
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=None,
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))
            for cell_list in BF_FL2_FL3_FL4:
                cell_shuxing = 29
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=None,
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))

            for cell_list in FL1_FL2_FL3_FL4:
                cell_shuxing = 30
                self.cell_infos[int(cell_list[0])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=None,
                                                  FL1=self.cell_infos[int(cell_list[0])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[1])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[2])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[3])].get_channel_info("FL4")))
            for cell_list in BF_FL1_FL2_FL3_FL4:
                cell_shuxing = 31
                self.cell_infos[int(cell_list[0])].get_channel_info("BF").type = cell_shuxing
                self.cell_infos[int(cell_list[1])].get_channel_info("FL1").type = cell_shuxing
                self.cell_infos[int(cell_list[2])].get_channel_info("FL2").type = cell_shuxing
                self.cell_infos[int(cell_list[3])].get_channel_info("FL3").type = cell_shuxing
                self.cell_infos[int(cell_list[4])].get_channel_info("FL4").type = cell_shuxing
                new_cell_infos.append(cell_struct(BF=self.cell_infos[int(cell_list[0])].get_channel_info("BF"),
                                                  FL1=self.cell_infos[int(cell_list[1])].get_channel_info("FL1"),
                                                  FL2=self.cell_infos[int(cell_list[2])].get_channel_info("FL2"),
                                                  FL3=self.cell_infos[int(cell_list[3])].get_channel_info("FL3"),
                                                  FL4=self.cell_infos[int(cell_list[4])].get_channel_info("FL4")))
        self.cell_infos = new_cell_infos
        return self

def merge_lists(l):
    def to_graph(l):
        G = networkx.Graph()
        for part in l:
            # each sublist is a bunch of nodes
            G.add_nodes_from(part)
            # it also imlies a number of edges:
            G.add_edges_from(to_edges(part))
        return G

    def to_edges(l):
        """
            treat `l` as a Graph and returns it's edges
            to_edges(['a','b','c','d']) -> [(a,b), (b,c),(c,d)]
        """
        it = iter(l)
        last = next(it)
        for current in it:
            yield last, current
            last = current

    G = to_graph(l)
    return [list(i) for i in connected_components(G)]


def groupcomputer(center_lists, distance_tred=0):
    new_celllist = center_lists.copy()
    if center_lists.shape[1] < 2:
        return 0, [], new_celllist
    # print(center_lists[:, 0])
    centerlist = center_lists[:, :2].tolist()
    radius = center_lists[:, 2]
    centerlist = np.array(centerlist)
    tree = KDTree(centerlist)
    center_index = []
    center_aix = []
    topk = min(centerlist.shape[0], 50)
    for ind in range(centerlist.shape[0]):
        dist, index = tree.query([centerlist[ind]], k=topk)
        for num in range(1, topk):
            if dist[0][num] <= radius[ind] + radius[index[0][num]] + distance_tred:
                center_index.append([index[0][0], index[0][num]])
    sortedlist = merge_lists(center_index)
    len_list = [len(i) for i in sortedlist]
    for list in sortedlist:
        temp = [(int(centerlist[k][0]), int(centerlist[k][1])) for k in list]
        center_aix.append(temp)
        for k in list:
            new_celllist[k, 3] = len(temp)
    return sum(len_list), center_aix, new_celllist





class Colors:
    # Ultralytics color palette https://ultralytics.com/
    def __init__(self):
        # hex = matplotlib.colors.TABLEAU_COLORS.values()
        hexs = ('FF3838', 'FF9D97', 'FF701F', 'FFB21D', 'CFD231', '48F90A', '92CC17', '3DDB86', '1A9334', '00D4BB',
                '2C99A8', '00C2FF', '344593', '6473FF', '0018EC', '8438FF', '520085', 'CB38FF', 'FF95C8', 'FF37C7')
        self.palette = [self.hex2rgb(f'#{c}') for c in hexs]
        self.n = len(self.palette)

    def __call__(self, i, bgr=False):
        c = self.palette[int(i) % self.n]
        return (c[2], c[1], c[0]) if bgr else c

    @staticmethod
    def hex2rgb(h):  # rgb order (PIL)
        return tuple(int(h[1 + i:1 + i + 2], 16) for i in (0, 2, 4))

def get_centerpoint(lis):
    _x_list = [vertex[0] for vertex in lis]
    _y_list = [vertex[1] for vertex in lis]
    _len = len(lis)
    _x = sum(_x_list) / _len
    _y = sum(_y_list) / _len
    return int(_x), int(_y)





def convert_from_cv2_to_image(img: np.ndarray) -> Image:
    return Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))


def convert_from_image_to_cv2(img: Image) -> np.ndarray:
    return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

