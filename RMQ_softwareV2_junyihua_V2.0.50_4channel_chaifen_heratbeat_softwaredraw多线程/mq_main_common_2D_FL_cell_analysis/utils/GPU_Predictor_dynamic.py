from copy import deepcopy
import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
import numpy as np
import pycuda.driver as cuda #cuda和tensorrt放在cupy之前,否则运行会出错
import tensorrt as trt
import cupyx.scipy.ndimage._interpolation as cusi
import cupy as cp
import os
import cv2
from loguru import logger as log

import gc



class GPUPredictorUtil(object):
    def __init__(self):
        self.max_concurrent_tasks=1
        self._shared_context=None
        self._shared_stream=None
        self._shared_execution_contexts= {}
        self.gpu_id = 0
        self.allocations = None
        self.engine = None
        self.context = None
        self.inputs = None
        self.outputs = None
        self.device = None
        self.ctx = None
        self.model_file_path = None
        self.SAMmodel=None
        self.ws = None
        self.root_url = 'ws://localhost:12318/ws'  # 这里输入websocket的url
        self.send_response=None
        os.environ['CUDA_VISIBLE_DEVICES'] = f"{self.gpu_id}"

    def release(self):
        self.context.__del__()
        self.engine.__del__()
        if self.allocations:
            for allocation in self.allocations:
                allocation.free()
        
        self.allocations = None
        self.inputs = None
        self.outputs = None
        self.engine = None
        self.context = None
        self.SAMmodel=None
        self.gpu_id = 0
        self.ctx = None
        cuda.init()
        self.device = cuda.Device(self.gpu_id)
        self.ctx = self.device.retain_primary_context()
        gc.collect()
        log.info("release model{}".format(self.model_file_path))
        return None

