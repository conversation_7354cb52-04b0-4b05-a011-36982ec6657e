# -*- mode: python ; coding: utf-8 -*-


block_cipher = None


a = Analysis(['mq_main_organoid_co_culture_piao.py'],
             pathex=[],
             binaries=[],
             datas=[],
             hiddenimports=["sklearn","sklearn.utils._cython_blas","sklearn.utils._typedefs","sklearn.neighbors.typedefs","sklearn.neighbors.quad_tree"
			 ,"sklearn.tree._utils","sklearn.tree","sklearn.neighbors._partition_nodes","skimage.restoration._unwrap_1d","skimage.filters.edges",
			 "fastrlock","fastrlock.rlock","cupy_backends.cuda.api._driver_enum","pyodbc",
             "cupy._core._cub_reduction","cupy._core._ufuncs","cupy_backends.cuda.api._runtime_enum",
             "cupy._core._routines_sorting",'cupy._core.flags','cupy._core.new_fusion','cupy._core._fusion_trace',
             "cupy._core._fusion_variable",'cupy._core._fusion_op','cupy._core._fusion_optimization','cupy._core._fusion',
             "cupy._core._fusion_kernel","cupy_backends.cuda.stream","cupy_backends.cuda._softlink",'cupy._core._carray'],
             hookspath=[],
             hooksconfig={},
             runtime_hooks=[],
             excludes=[],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher,
             noarchive=False)
pyz = PYZ(a.pure, a.zipped_data,
             cipher=block_cipher)

exe = EXE(pyz,
          a.scripts, 
          [],
          exclude_binaries=True,
          name='mq_main_organoid_co_culture_piao',
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,
          console=False,
          disable_windowed_traceback=False,
          target_arch=None,
          icon='AAAI.ico',
          version="app_version_info.txt",
          codesign_identity=None,
          entitlements_file=None )
coll = COLLECT(exe,
               a.binaries,
               a.zipfiles,
               a.datas, 
               strip=False,
               upx=True,
               upx_exclude=[],
               name='mq_main_organoid_co_culture_piao')
