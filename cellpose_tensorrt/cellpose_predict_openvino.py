import cv2
from matplotlib import pyplot as plt
from tqdm import trange, tqdm
import numpy as np
from openvino.runtime import Core
import transforms
import dynamics
from loguru import logger as io_logger
io_logger.add("run.txt")
def run_tiled(
    imgi,
    exec_net,
    batch_size=1,
    nclasses=3,
    augment=False,
    bsize=224,
    tile_overlap=0.1,
    return_conv=False,
):
    IMG, ysub, xsub, Ly, Lx = transforms.make_tiles(imgi, bsize=bsize,
                                                    augment=augment, tile_overlap=tile_overlap)
    ny, nx, nchan, ly, lx = IMG.shape
    IMG = np.reshape(IMG, (ny * nx, nchan, ly, lx))
    batch_size = batch_size
    niter = int(np.ceil(IMG.shape[0] / batch_size))
    nout = nclasses + 32 * return_conv
    y = np.zeros((IMG.shape[0], nout, ly, lx))
    for k in tqdm(range(niter), desc="tiled process"):
        irange = slice(batch_size * k, min(IMG.shape[0], batch_size * k + batch_size))
        outs = exec_net.infer({"input":  IMG[irange]})
        outs = {out.get_any_name(): value for out, value in outs.items()}
        y0,style=outs["output"],outs["style"]
        y[irange] = y0.reshape(irange.stop - irange.start, y0.shape[-3], y0.shape[-2], y0.shape[-1])
        if k == 0:
            styles = style[0]
        styles += style.sum(axis=0)
    styles /= IMG.shape[0]
    if augment:
        y = np.reshape(y, (ny, nx, nout, bsize, bsize))
        y = transforms.unaugment_tiles(y, False)
        y = np.reshape(y, (-1, nout, bsize, bsize))

    yf = transforms.average_tiles(y, ysub, xsub, Ly, Lx)
    yf = yf[:, :imgi.shape[1], :imgi.shape[2]]
    styles /= (styles ** 2).sum() ** 0.5
    return yf, styles
def run_net(
        imgs,exec_net, nclasses=3,augment=False, tile=True, tile_overlap=0.1, bsize=224,
        return_conv=False,net_avg=False
):
    if imgs.ndim == 4:
        # make image Lz x nchan x Ly x Lx for net
        imgs = np.transpose(imgs, (0, 3, 1, 2))
        detranspose = (0, 2, 3, 1)
        return_conv = False
    else:
        # make image nchan x Ly x Lx for net
        imgs = np.transpose(imgs, (2, 0, 1))
        detranspose = (1, 2, 0)

    # pad image for net so Ly and Lx are divisible by 4
    imgs, ysub, xsub = transforms.pad_image_ND(imgs)
    # slices from padding
    #         slc = [slice(0, self.nclasses) for n in range(imgs.ndim)] # changed from imgs.shape[n]+1 for first slice size
    slc = [slice(0, imgs.shape[n] + 1) for n in range(imgs.ndim)]
    slc[-3] = slice(0, nclasses + 32 * return_conv + 1)
    slc[-2] = slice(ysub[0], ysub[-1] + 1)
    slc[-1] = slice(xsub[0], xsub[-1] + 1)
    slc = tuple(slc)
    # run network
    if tile or augment or imgs.ndim == 4:
        y, style = run_tiled(imgs,exec_net,augment=augment, bsize=bsize,
                                   tile_overlap=tile_overlap,
                                   return_conv=return_conv)
    else:
        imgs = np.expand_dims(imgs, axis=0)
        outs = exec_net.infer({"input": imgs})
        outs = {out.get_any_name(): value for out, value in outs.items()}
        y, style = outs["output"], outs["style"]
        y, style = y[0], style[0]
    style /= (style ** 2).sum() ** 0.5

    # slice out padding
    y = y[slc]
    # transpose so channels axis is last again
    y = np.transpose(y, detranspose)

    return y, style





def predict(x,exec_net, normalize=True, invert=False,
        rescale=1.0, resample=True,diameter=None,
        cellprob_threshold=0.0,
        flow_threshold=0.4, min_size=15,tile_overlap=0.1,
        interp=True, do_3D=False, stitch_threshold=0.0,channels=[0,0], channel_axis=None,
        z_axis=None,nclasses=3,nchan=2,net_avg=False,augment=False,tile=True,model_type=None,
):
    nbase = [nchan, 32, 64, 128, 256]
    if model_type == 'nuclei':
        diam_mean = 17.
    else:
        diam_mean = 30.

    x = transforms.convert_image(x, channels, channel_axis=channel_axis, z_axis=z_axis,
                                 do_3D=(do_3D or stitch_threshold > 0),
                                 normalize=False, invert=False, nchan=nchan)
    if x.ndim < 4:
        x = x[np.newaxis, ...]
    if diameter is not None and diameter > 0:
        rescale = diam_mean / diameter
    elif rescale is None:
        diameter = diam_mean
        rescale = diam_mean / diameter

    shape = x.shape
    nimg = shape[0]
    iterator = trange(nimg) if nimg > 1 else range(nimg)
    styles = np.zeros((nimg, nbase[-1]), np.float32)
    if resample:
        dP = np.zeros((2, nimg, shape[1], shape[2]), np.float32)
        cellprob = np.zeros((nimg, shape[1], shape[2]), np.float32)

    else:
        dP = np.zeros((2, nimg, int(shape[1] * rescale), int(shape[2] * rescale)), np.float32)
        cellprob = np.zeros((nimg, int(shape[1] * rescale), int(shape[2] * rescale)), np.float32)

    for i in iterator:
        img = np.asarray(x[i])
        if normalize or invert:
            img = transforms.normalize_img(img, invert=invert)
        if rescale != 1.0:
            img = transforms.resize_image(img, rsz=rescale)
        yf, style = run_net(img, exec_net,net_avg=net_avg,
                                   augment=augment, tile=tile,
                                   tile_overlap=tile_overlap)
        if resample:
            yf = transforms.resize_image(yf, shape[1], shape[2])

        cellprob[i] = yf[:, :, 2]
        dP[:, i] = yf[:, :, :2].transpose((2, 0, 1))
        if nclasses == 4:
            if i == 0:
                bd = np.zeros_like(cellprob)
            bd[i] = yf[:, :, 3]
        styles[i] = style
    del yf, style
    styles = styles.squeeze()
    niter = 200 if (do_3D and not resample) else (1 / rescale * 200)
    masks, p = [], []
    resize = [shape[1], shape[2]] if not resample else None
    for i in tqdm(iterator, desc="compute_masks"):
        outputs = dynamics.compute_masks(dP[:, i], cellprob[i], niter=niter, cellprob_threshold=cellprob_threshold,
                                         flow_threshold=flow_threshold, interp=interp, resize=resize)
        masks.append(outputs[0])
        p.append(outputs[1])

    masks = np.array(masks)
    p = np.array(p)

    if stitch_threshold > 0 and nimg > 1:
        masks = transforms.stitch3D(masks, stitch_threshold=stitch_threshold)
        masks = transforms.fill_holes_and_remove_small_masks(masks, min_size=min_size)
    masks, dP, cellprob, p = masks.squeeze(), dP.squeeze(), cellprob.squeeze(), p.squeeze()
    flows = [transforms.dx_to_circ(dP), dP, cellprob, p]
    return masks, flows[0], styles

def create_model(model_path, device="CPU"):
    ie = Core()
    net = ie.read_model(model=model_path)
    exec_net = ie.compile_model(net, device).create_infer_request()
    return exec_net


if __name__ == "__main__":
    model_path = "best.onnx"
    img= transforms.io_imread("G2_01_02_01.jpg")
    print(img.shape)
    exec_net=create_model(model_path)
    masks, flows, styles = predict(img, exec_net)
    # fig = plt.figure(figsize=(12,5))
    # transforms.show_segmentation(fig, img, masks, flows)
    # plt.tight_layout()
    # plt.show()
    cv2.imwrite("masks.tiff",masks)