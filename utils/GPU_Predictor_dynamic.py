import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
from copy import deepcopy
import numpy as np
import pycuda.driver as cuda #cuda和tensorrt放在cupy之前,否则运行会出错
from pycuda.tools import clear_context_caches
import tensorrt as trt
import cupyx.scipy.ndimage._interpolation as cusi
import cupy as cp
import os
import cv2
from loguru import logger as log
import gc

class HostDeviceMem(object):
    def __init__(self, host_mem, device_mem):
        self.host = host_mem
        self.device = device_mem

    def __str__(self):
        return "Host:\n" + str(self.host) + "\nDevice:\n" + str(self.device)

    def __repr__(self):
        return self.__str__()
class TrtModel():
    def __init__(self, engine_path, gpu_id=0, max_batch_size=1):
        self.inputs = None
        self.outputs = None
        self.bindings = None
        self.stream = None
        print("TensorRT version: %s" % trt.__version__)
        cuda.init()
        self.cfx = cuda.Device(gpu_id).retain_primary_context()
        self.engine_path = engine_path
        self.trt_logger = trt.Logger(trt.Logger.ERROR)
        self.runtime = trt.Runtime(self.trt_logger)
        self.engine = self.load_engine(self.runtime, self.engine_path)
        self.context = self.engine.create_execution_context()
        self.max_batch_size = max_batch_size


    @staticmethod
    def load_engine(trt_runtime, engine_path):
        trt.init_libnvinfer_plugins(None, "")
        try:
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            engine = trt_runtime.deserialize_cuda_engine(engine_data)
            assert engine
        except Exception as e:
            log.error("load SAM model error")
            from .export_trt import EngineBuilder
            if os.path.exists(engine_path.replace(".engine", ".onnx")):
                log.info("find onnx model,try to load onnx model convert to engine")
                builder = EngineBuilder(verbose=False)
                builder.get_engine(engine_path.replace(".engine", ".onnx"),engine_path)
            else:
                log.error(f"{engine_path.replace('.engine', '.onnx')} file is not exists,not convert model to engine,pls check")
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            engine = trt_runtime.deserialize_cuda_engine(engine_data)
        return engine

    def allocate_buffers(self):
        self.cfx.push()
        inputs = []
        outputs = []
        bindings = []
        stream = cuda.Stream()
        for binding in self.engine:
            if binding in ["point_coords", "point_labels"]:
                size = abs(trt.volume(self.engine.get_tensor_shape(binding))) * self.max_batch_size
            else:
                size = abs(trt.volume(self.engine.get_tensor_shape(binding)))
            # print(f"binding: {binding}, size: {size}")
            dtype = trt.nptype(self.engine.get_tensor_dtype(binding))
            host_mem = cuda.pagelocked_empty(size, dtype)
            device_mem = cuda.mem_alloc(host_mem.nbytes)
            bindings.append(int(device_mem))
            if self.engine.binding_is_input(binding):
                inputs.append(HostDeviceMem(host_mem, device_mem))
            else:
                outputs.append(HostDeviceMem(host_mem, device_mem))
        return inputs, outputs, bindings, stream
    def sam_infer(self, inf_in_list, binding_shape_map=None):
        self.inputs, self.outputs, self.bindings, self.stream = self.allocate_buffers()
        if binding_shape_map:
            self.context.set_optimization_profile_async(0,self.stream.handle)
            for binding_name, shape in binding_shape_map.items():
                binding_idx = self.engine[binding_name]
                # print(f"binding_name: {binding_name}, binding_idx: {binding_idx}, shape: {shape}")
                self.context.set_binding_shape(binding_idx, shape)

        for i in range(len(self.inputs)):
            self.inputs[i].host = inf_in_list[i]
            cuda.memcpy_htod_async(self.inputs[i].device, self.inputs[i].host, self.stream)
        self.context.execute_async_v2(bindings=self.bindings, stream_handle=self.stream.handle)
        for i in range(len(self.outputs)):
            cuda.memcpy_dtoh_async(self.outputs[i].host, self.outputs[i].device, self.stream)
        self.stream.synchronize()
        self.cfx.pop()
        return [out.host.copy() for out in self.outputs]
class SegmentAnythingTRT:
    """Segmentation model using SegmentAnything"""
    def __init__(self, encoder_model_path, decoder_model_path) -> None:
        self.target_size = 1024
        self.input_size = (1024, 1024)
        self.batch_size=1
        # embedding init
        self.encoder_session = TrtModel(engine_path=encoder_model_path)
        # sam init
        self.decoder_session = TrtModel(engine_path=decoder_model_path,max_batch_size=20)
        log.info(f"{encoder_model_path} {self.encoder_session}")
        log.info(f"{decoder_model_path} {self.decoder_session}")
    def clip_segments(self, boxes, shape):
        # Clip segments (xy1,xy2,...) to image shape (height, width)
        boxes[:, 0] = boxes[:, 0].clip(0, shape[1])  # x
        boxes[:, 1] = boxes[:, 1].clip(0, shape[0])  # y
    def scale_segments(self, img1_shape, segments, img0_shape, shift_amount=None, ratio_pad=None):
        # Rescale coords (xyxy) from img1_shape to img0_shape
        if ratio_pad == 0:
            gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
            segments /= gain
            self.clip_segments(segments, img0_shape)
            if shift_amount is not None:
                segments[:, 0] += shift_amount[0]  # x padding
                segments[:, 1] += shift_amount[1]  # y padding
            return segments
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]
        segments[:, 0] -= pad[0]  # x padding
        segments[:, 1] -= pad[1]  # y padding
        segments /= gain
        self.clip_segments(segments, img0_shape)
        if shift_amount is not None:
            segments[:, 0] += shift_amount[0]  # x padding
            segments[:, 1] += shift_amount[1]  # y padding
        return segments

    def expand_box(self, box, image_shape, expand_pixels=10, box_expand=1):
        x1, y1, x2, y2 = box.astype(int)
        img_h, img_w, _ = image_shape
        # 计算扩大后的新box
        x1_new = max(0, x1 - expand_pixels)
        y1_new = max(0, y1 - expand_pixels)
        x2_new = min(img_w - 1, x2 + expand_pixels)
        y2_new = min(img_h - 1, y2 + expand_pixels)
        box_extended = [x1_new, y1_new, x2_new, y2_new]
        # 计算box_map,检查边界
        new_box_w = x2_new - x1_new
        new_box_h = y2_new - y1_new
        x1_map = max(x1 - x1_new - box_expand, 0)
        y1_map = max(y1 - y1_new - box_expand, 0)
        x2_map = min(x2 - x1_new + box_expand, new_box_w)
        y2_map = min(y2 - y1_new + box_expand, new_box_h)
        new_box_map = [x1_map, y1_map, x2_map, y2_map]
        return box_extended,new_box_map
    def transform_segments(self, masks,box,original_size,shift_amount=None):
        """Transform masks
        Transform the masks back to the original image size.
        """
        if len(masks.shape) == 4:
            origin_masks = np.transpose(masks[0],(1,2,0))
        else:
            masks = cusi.zoom(cp.array(masks), (4, 4), order=1, mode="nearest")
            masks = cp.asnumpy(masks)
            masks = masks[None]
            #print(masks.shape)
            origin_masks = np.transpose(masks,(1,2,0))
        # print(origin_masks.shape)
        origin_masks[origin_masks > 0.0] = 255
        origin_masks[origin_masks <= 0.0] = 0
        origin_masks=origin_masks.astype(np.uint8)
        cnt = cv2.findContours(origin_masks, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
        x1, y1, x2, y2 = box.astype(int)
        center_x = int((x1 + x2) / 2)
        center_y = int((y1 + y2) / 2)
        major_box_length = max((x2 - x1), (y2 - y1))
        minor_box_length = min((x2 - x1), (y2 - y1))
        end_a = 0
        if major_box_length == (y2 - y1):
            end_a = 90
        ell_con = cv2.ellipse2Poly((center_x, center_y),
                                   (int(major_box_length / 2), int(minor_box_length / 2)), end_a, 0, 360, 1)
        c = [ell_con]
        segment = np.array(c[np.array([len(x) for x in c]).argmax()]).reshape(-1, 2)
        if len(cnt):
            # 计算每个轮廓的面积
            areas = [cv2.contourArea(c) for c in cnt]
            # 找到面积最大的轮廓
            largest_contour = cnt[np.argmax(areas)]
            # 计算最大轮廓的面积
            largest_area = areas[np.argmax(areas)]
            # 检查最大轮廓的面积是否大于总面积的一半
            #print("len(cnt)",len(cnt),np.unique(origin_masks),largest_area)
            if largest_area < 10:
                return segment
            segment = [self.scale_segments(origin_masks.shape, np.array(x, dtype='float32'), original_size,shift_amount=shift_amount, ratio_pad=0) for num, x in enumerate(largest_contour)]
            segment = np.array(segment, dtype='int32')
            epsilon = (1e-100) * cv2.arcLength(segment, True)
            segment = cv2.approxPolyDP(segment, epsilon, True)
        return segment
    def get_input_points(self, prompt):
        """Get input points"""
        points = []
        labels = []
        # print("prompt",prompt)
        if len(prompt) == 2:
            # points.append([int(prompt[0]/self.input_size[0]),int(prompt[1]/self.input_size[1])])
            points.append([prompt[0], prompt[1]])
            labels.append(1)
        elif len(prompt) == 4:
            # points.append([int(prompt[0]/self.input_size[0]),int(prompt[1]/self.input_size[1])])  # top left
            # points.append([int(prompt[2]/self.input_size[0]),int(prompt[3]/self.input_size[1])])  # bottom right
            points.append([prompt[0], prompt[1]])  # top left
            points.append([prompt[2], prompt[3]])  # bottom right
            labels.append(2)
            labels.append(3)
            # points.append([int((prompt[0]+prompt[2])/2),int((prompt[1]+prompt[3])/2)])
            # labels.append(2)
        else:
            pass
            # print("prompt fail", prompt)
        points, labels = np.array(points), np.array(labels)
        # print(points,labels)
        return points, labels

    def run_encoder(self, encoder_inputs):
        """Run encoder"""
        encoder_inputs = encoder_inputs.transpose(2, 0, 1)
        encoder_inputs = np.ascontiguousarray(encoder_inputs)[None, :, :, :]
        pixel_mean = [123.675, 116.28, 103.53],
        pixel_std = [58.395, 57.12, 57.375]
        pixel_mean = np.array(pixel_mean).reshape(-1, 1, 1)
        pixel_std = np.array(pixel_std).reshape(-1, 1, 1)
        encoder_inputs = ((encoder_inputs - pixel_mean) / pixel_std).astype(np.float32)
        output = self.encoder_session.sam_infer(encoder_inputs)
        image_embedding = output[0]
        return image_embedding

    @staticmethod
    def get_preprocess_shape(oldh: int, oldw: int, long_side_length: int):
        """
        Compute the output size given input size and target long side length.
        """
        scale = long_side_length * 1.0 / max(oldh, oldw)
        newh, neww = oldh * scale, oldw * scale
        neww = int(neww + 0.5)
        newh = int(newh + 0.5)
        return (newh, neww)

    def apply_coords(self, coords: np.ndarray, original_size, target_length):
        """
        Expects a numpy array of length 2 in the final dimension. Requires the
        original image size in (H, W) format.
        """
        old_h, old_w = original_size
        new_h, new_w = self.get_preprocess_shape(
            original_size[0], original_size[1], target_length
        )
        coords = deepcopy(coords).astype(float)
        coords[..., 0] = coords[..., 0] * (new_w / old_w)
        coords[..., 1] = coords[..., 1] * (new_h / old_h)
        return coords

    def run_decoder(
            self, image_embedding, original_size, transform_matrix, prompt
    ):
        """Run decoder"""
        input_points, input_labels = self.get_input_points(prompt)

        # Add a batch index, concatenate a padding point, and transform.
        onnx_coord = np.concatenate(
            [input_points, np.array([[0.0, 0.0]])], axis=0
        )[None, :, :]
        onnx_label = np.concatenate([input_labels, np.array([-1])], axis=0)[
                     None, :
                     ].astype(np.float32)
        onnx_coord = self.apply_coords(
            onnx_coord, self.input_size, self.target_size
        ).astype(np.float32)

        # Apply the transformation matrix to the coordinates.
        onnx_coord = np.concatenate(
            [
                onnx_coord,
                np.ones((1, onnx_coord.shape[1], 1), dtype=np.float32),
            ],
            axis=2,
        )
        onnx_coord = np.matmul(onnx_coord, transform_matrix.T)
        onnx_coord = onnx_coord[:, :, :2].astype(np.float32)

        # Create an empty mask input and an indicator for no mask.
        onnx_mask_input = np.zeros((1, 1, 256, 256), dtype=np.float32)
        onnx_has_mask_input = np.zeros(1, dtype=np.float32)

        decoder_inputs = {
            "image_embeddings": image_embedding,
            "point_coords": onnx_coord,
            "point_labels": onnx_label,
            "mask_input": onnx_mask_input,
            "has_mask_input": onnx_has_mask_input,
            "orig_im_size": np.array(self.input_size, dtype=np.float32),
        }
        masks, scores, _= self.decoder_session.sam_infer(list(decoder_inputs.values()), binding_shape_map={key:value.shape for key,value in decoder_inputs.items()})
        masks=masks.reshape(self.batch_size, -1).reshape(4, 256, 256)
        scores =scores.reshape(self.batch_size, -1).squeeze(0)
        output = scores.argmax()
        masks = masks[output]
        #print(masks.shape)
        return masks

    def encode(self, cv_image):
        """
        Calculate embedding and metadata for a single image.
        """
        original_size = cv_image.shape[:2]

        # Calculate a transformation matrix to convert to self.input_size
        scale_x = self.input_size[1] / cv_image.shape[1]
        scale_y = self.input_size[0] / cv_image.shape[0]
        scale = min(scale_x, scale_y)
        transform_matrix = np.array(
            [
                [scale, 0, 0],
                [0, scale, 0],
                [0, 0, 1],
            ]
        )
        cv_image = cv2.warpAffine(
            cv_image,
            transform_matrix[:2],
            (self.input_size[1], self.input_size[0]),
            flags=cv2.INTER_LINEAR,
        )

        encoder_inputs = cv_image.astype(np.float32)
        image_embedding = self.run_encoder(encoder_inputs).reshape(1, 256, 64, 64)
        return {
            "image_embedding": image_embedding,
            "original_size": original_size,
            "transform_matrix": transform_matrix,
        }

    def predict_masks(self, embedding, prompt):
        """
        Predict masks for a single image.
        """
        box_masks = self.run_decoder(
            embedding["image_embedding"],
            embedding["original_size"],
            embedding["transform_matrix"],
            prompt,
        )
        x1_map, y1_map, x2_map, y2_map=prompt
        point_prompt=[(x1_map+x2_map)/2,(y1_map+y2_map)/2]
        points_mask=self.run_decoder(
            embedding["image_embedding"],
            embedding["original_size"],
            embedding["transform_matrix"],
            point_prompt,
        )
        # 同为正的情况
        condition_both_positive = (box_masks > 0) & (points_mask > 0)
        # 同为负的情况
        condition_both_negative = (box_masks < 0) & (points_mask < 0)
        # 一正一负的情况
        condition_one_pos_one_neg = (box_masks * points_mask < 0)

        # 构建最终结果
        mask = np.where(condition_both_positive, np.maximum(box_masks, points_mask),
                              np.where(condition_both_negative, np.minimum(box_masks, points_mask),
                                       np.where(condition_one_pos_one_neg,
                                                np.where(np.abs(box_masks*1.3) > np.abs(points_mask*0.7), box_masks,
                                                         points_mask),
                                                box_masks)))
        return mask

class GPUPredictorUtil(object):
    def __init__(self):
        self.gpu_id = 0
        self.allocations = None
        self.engine = None
        self.context = None
        self.inputs = None
        self.outputs = None
        self.device = None
        self.ctx = None
        self.model_file_path = None
        self.SAMmodel=None
        self.task_id = ""
        self.cut_info = None
        self.msg_id = None
        self.ws = None
        self.root_url = 'ws://localhost:12318/ws'  # 这里输入websocket的url
        self.send_response=None
        os.environ['CUDA_VISIBLE_DEVICES'] = f"{self.gpu_id}"



    def release(self):
        self.context.__del__()
        self.engine.__del__()
        if self.allocations:
            for allocation in self.allocations:
                allocation.free()
        clear_context_caches()
        self.allocations = None
        self.inputs = None
        self.outputs = None
        self.engine = None
        self.context = None
        self.SAMmodel=None
        self.gpu_id = 0
        self.ctx = None
        cuda.init()
        self.device = cuda.Device(self.gpu_id)
        self.ctx = self.device.retain_primary_context()
        gc.collect()
        log.info("release model{}".format(self.model_file_path))
        return None

