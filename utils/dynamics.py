"""
Co<PERSON>right © 2023 Howard Hughes Medical Institute, Authored by <PERSON><PERSON> and <PERSON>.
"""
import time
import numpy as np
from numba import njit
import cupy as cp
import cv2
import fastremap
from . import transforms
from scipy.ndimage import mean
from scipy.ndimage import find_objects
from loguru import logger
from cupyx.scipy.ndimage import maximum_filter1d
from cupyx.scipy.ndimage._interpolation import map_coordinates as cusi_map_coordinates
mempool = cp.get_default_memory_pool()

def _extend_centers(T, y, x, ymed, xmed, Lx, niter):
    """ run diffusion from center of mask (ymed, xmed) on mask pixels (y, x)
    Parameters
    --------------
    T: float64, array
        _ x Lx array that diffusion is run in
    y: int32, array
        pixels in y inside mask
    x: int32, array
        pixels in x inside mask
    ymed: int32
        center of mask in y
    xmed: int32
        center of mask in x
    Lx: int32
        size of x-dimension of masks
    niter: int32
        number of iterations to run diffusion
    Returns
    ---------------
    T: float64, array
        amount of diffused particles at each pixel
    """

    T_gpu = cp.asarray(T)
    y_gpu = cp.asarray(y)
    x_gpu = cp.asarray(x)
    ymed_gpu = cp.asarray(ymed)
    xmed_gpu = cp.asarray(xmed)

    for t in range(niter):
        T_gpu[ymed_gpu * Lx + xmed_gpu] += 1
        T_gpu[y_gpu * Lx + x_gpu] = 1 / 9. * (T_gpu[y_gpu * Lx + x_gpu] +
                                              T_gpu[(y_gpu - 1) * Lx + x_gpu] +
                                              T_gpu[(y_gpu + 1) * Lx + x_gpu] +
                                              T_gpu[y_gpu * Lx + x_gpu - 1] +
                                              T_gpu[y_gpu * Lx + x_gpu + 1] +
                                              T_gpu[(y_gpu - 1) * Lx + x_gpu - 1] +
                                              T_gpu[(y_gpu - 1) * Lx + x_gpu + 1] +
                                              T_gpu[(y_gpu + 1) * Lx + x_gpu - 1] +
                                              T_gpu[(y_gpu + 1) * Lx + x_gpu + 1])
    return cp.asnumpy(T_gpu)



def diameters_cpu(masks):
    _, counts = np.unique(np.int32(masks), return_counts=True)
    counts = counts[1:]
    md = np.median(counts**0.5)
    if np.isnan(md):
        md = 0
    md /= (np.pi**0.5)/2
    return md, counts**0.5
def get_centers(masks, slices):
    """
    Get the centers of the masks and their extents.

    Args:
        masks (ndarray): The labeled masks.
        slices (ndarray): The slices of the masks.

    Returns:
        tuple containing
            - centers (ndarray): The centers of the masks.
            - ext (ndarray): The extents of the masks.
    """
    masks_gpu = cp.asarray(masks)
    slices_gpu = cp.asarray(slices)

    centers = cp.zeros((len(slices_gpu), 2), "int32")
    ext = cp.zeros((len(slices_gpu),), "int32")

    for p in range(len(slices_gpu)):
        si = slices_gpu[p]
        i = si[0]
        sr, sc = si[1:3], si[3:5]
        # find center in slice around mask
        yi, xi = cp.nonzero(masks_gpu[sr[0]:sr[-1], sc[0]:sc[-1]] == (i + 1))
        ymed = yi.mean()
        xmed = xi.mean()
        # center is closest point to (ymed, xmed) within mask
        imin = ((xi - xmed)**2 + (yi - ymed)**2).argmin()
        ymed = yi[imin] + sr[0]
        xmed = xi[imin] + sc[0]
        centers[p] = cp.array([ymed, xmed])
        ext[p] = (sr[-1] - sr[0]) + (sc[-1] - sc[0]) + 2

    return cp.asnumpy(centers), cp.asnumpy(ext)
def _extend_centers_cupy(neighbors, meds, isneighbor, shape, n_iter):
    try:
        mempool.free_all_blocks()
        T = cp.zeros(shape, dtype=cp.float64)
        for i in range(n_iter):
            T[tuple(meds.T)] += 1
            Tneigh = T[tuple(neighbors)]
            Tneigh *= isneighbor
            T[tuple(neighbors[:, 0])] = Tneigh.mean(axis=0)
        del meds, isneighbor, Tneigh

        if T.ndim == 2:
            grads = T[neighbors[0, [2, 1, 4, 3]], neighbors[1, [2, 1, 4, 3]]]
            del neighbors
            dy = grads[0] - grads[1]
            dx = grads[2] - grads[3]
            del grads
            mu_cupy = cp.stack((dy, dx), axis=-2)
        else:
            grads = T[tuple(neighbors[:, 1:])]
            del neighbors
            dz = grads[0] - grads[1]
            dy = grads[2] - grads[3]
            dx = grads[4] - grads[5]
            del grads
            mu_cupy = cp.stack((dz, dy, dx), axis=-2)
    except Exception as e:
        logger.error(f"Error in cupy,use numpy {e}")
        if isinstance(neighbors, cp.ndarray):
            neighbors = neighbors.get()
        if isinstance(meds, cp.ndarray):
            meds = meds.get()
        if isinstance(isneighbor, cp.ndarray):
            isneighbor = isneighbor.get()
        T = np.zeros(shape, dtype=np.float64)
        for i in range(n_iter):
            T[tuple(meds.T)] += 1
            Tneigh = T[tuple(neighbors)]
            Tneigh *= isneighbor
            T[tuple(neighbors[:, 0])] = Tneigh.mean(axis=0)
        del meds, isneighbor, Tneigh
        if T.ndim == 2:
            grads = T[neighbors[0, [2, 1, 4, 3]], neighbors[1, [2, 1, 4, 3]]]
            del neighbors
            dy = grads[0] - grads[1]
            dx = grads[2] - grads[3]
            del grads
            mu = np.stack((dy, dx), axis=-2)
        else:
            grads = T[tuple(neighbors[:, 1:])]
            del neighbors
            dz = grads[0] - grads[1]
            dy = grads[2] - grads[3]
            dx = grads[4] - grads[5]
            del grads
            mu = np.stack((dz, dy, dx), axis=-2)
        mu_cupy = cp.asarray(mu)
    return mu_cupy
def masks_to_flows_gpu(masks, device):
    """ convert masks to flows using diffusion from center pixel
    Center of masks where diffusion starts is defined using COM
    Parameters
    -------------
    masks: int, 2D or 3D array
        labelled masks 0=NO masks; 1,2,...=mask labels
    Returns
    -------------
    mu: float, 3D or 4D array
        flows in Y = mu[-2], flows in X = mu[-1].
        if masks are 3D, flows in Z = mu[0].
    mu_c: float, 2D or 3D array
        for each pixel, the distance to the center of the mask
        in which it resides
        :param masks:
        :param device:
    """
    masks_padded = cp.array(masks.astype("int64"))
    masks_padded = cp.pad(masks_padded, (1, 1), 'constant')

    ### get mask pixel neighbors
    y, x = cp.nonzero(masks_padded)
    neighborsY = cp.stack((y, y - 1, y + 1, y, y, y - 1, y - 1, y + 1, y + 1), axis=0)
    neighborsX = cp.stack((x, x, x, x - 1, x + 1, x - 1, x + 1, x - 1, x + 1), axis=0)
    neighbors = cp.stack((neighborsY, neighborsX), axis=0)
    neighbor_masks = masks_padded[tuple(neighbors)]
    isneighbor = neighbor_masks == neighbor_masks[0]

    ### get center-of-mass within cell
    slices = find_objects(masks)
    # turn slices into array
    slices = np.array([
        np.array([i, si[0].start, si[0].stop, si[1].start, si[1].stop])
        for i, si in enumerate(slices)
        if si is not None
    ])
    centers, ext = get_centers(masks, slices)
    meds_p = cp.array(centers)
    meds_p += 1  # for padding

    ### run diffusion
    n_iter = 2 * ext.max()
    shape = masks_padded.shape
    mu = _extend_centers_cupy(neighbors, meds_p, isneighbor, shape, n_iter=n_iter)

    # new normalization
    mu /= (1e-60 + (mu ** 2).sum(axis=0) ** 0.5)

    # put into original image
    mu0 = np.zeros((2, masks.shape[0], masks.shape[1]))
    mu0[:, y.get() - 1, x.get() - 1] = mu.get()

    return mu0, meds_p.get() - 1

def masks_to_flows_cpu(masks, device=None):
    """ convert masks to flows using diffusion from center pixel
    Center of masks where diffusion starts is defined to be the
    closest pixel to the median of all pixels that is inside the
    mask. Result of diffusion is converted into flows by computing
    the gradients of the diffusion density map.
    Parameters
    -------------
    masks: int, 2D array
        labelled masks 0=NO masks; 1,2,...=mask labels
    Returns
    -------------
    mu: float, 3D array
        flows in Y = mu[-2], flows in X = mu[-1].
        if masks are 3D, flows in Z = mu[0].
    mu_c: float, 2D array
        for each pixel, the distance to the center of the mask
        in which it resides
        :param masks:
        :param device:
    """

    Ly, Lx = masks.shape
    mu = np.zeros((2, Ly, Lx), np.float64)
    mu_c = np.zeros((Ly, Lx), np.float64)

    nmask = masks.max()
    slices = find_objects(masks)
    dia = diameters_cpu(masks)[0]
    s2 = (.15 * dia) ** 2
    for i, si in enumerate(slices):
        if si is not None:
            sr, sc = si
            ly, lx = sr.stop - sr.start + 1, sc.stop - sc.start + 1
            y, x = np.nonzero(masks[sr, sc] == (i + 1))
            y = y.astype(np.int32) + 1
            x = x.astype(np.int32) + 1
            ymed = np.median(y)
            xmed = np.median(x)
            imin = np.argmin((x - xmed) ** 2 + (y - ymed) ** 2)
            xmed = x[imin]
            ymed = y[imin]

            d2 = (x - xmed) ** 2 + (y - ymed) ** 2
            mu_c[sr.start + y - 1, sc.start + x - 1] = np.exp(-d2 / s2)

            niter = 2 * np.int32(np.ptp(x) + np.ptp(y))
            T = np.zeros((ly + 2) * (lx + 2), np.float64)
            T = _extend_centers(T, y, x, ymed, xmed, np.int32(lx), np.int32(niter))
            T[(y + 1) * lx + x + 1] = np.log(1. + T[(y + 1) * lx + x + 1])

            dy = T[(y + 1) * lx + x] - T[(y - 1) * lx + x]
            dx = T[y * lx + x + 1] - T[y * lx + x - 1]
            mu[:, sr.start + y - 1, sc.start + x - 1] = np.stack((dy, dx))

    mu /= (1e-20 + (mu ** 2).sum(axis=0) ** 0.5)

    return mu, mu_c


def masks_to_flows(masks, use_gpu=False, device=None):
    """ convert masks to flows using diffusion from center pixel

    Center of masks where diffusion starts is defined to be the
    closest pixel to the median of all pixels that is inside the
    mask. Result of diffusion is converted into flows by computing
    the gradients of the diffusion density map.

    Parameters
    -------------

    masks: int, 2D or 3D array
        labelled masks 0=NO masks; 1,2,...=mask labels

    Returns
    -------------

    mu: float, 3D or 4D array
        flows in Y = mu[-2], flows in X = mu[-1].
        if masks are 3D, flows in Z = mu[0].

    mu_c: float, 2D or 3D array
        for each pixel, the distance to the center of the mask
        in which it resides
        :param masks:
        :param use_gpu:
        :param device:

    """
    if masks.max() == 0:
        logger.warning('empty masks!')
        return np.zeros((2, *masks.shape), 'float32')
    if use_gpu:
        logger.info(f"masks_to_flows use_gpu {device}")
        masks_to_flows_device = masks_to_flows_gpu
    else:
        logger.info(f"masks_to_flows use_cpu {device}")
        masks_to_flows_device = masks_to_flows_cpu
    if masks.ndim == 3:
        Lz, Ly, Lx = masks.shape
        mu = np.zeros((3, Lz, Ly, Lx), np.float32)
        for z in range(Lz):
            mu0 = masks_to_flows_device(masks[z], device=device)[0]
            mu[[1, 2], z] += mu0
        for y in range(Ly):
            mu0 = masks_to_flows_device(masks[:, y], device=device)[0]
            mu[[0, 2], :, y] += mu0
        for x in range(Lx):
            mu0 = masks_to_flows_device(masks[:, :, x], device=device)[0]
            mu[[0, 1], :, :, x] += mu0
        return mu
    elif masks.ndim == 2:
        mu, mu_c = masks_to_flows_device(masks, device=device)
        return mu
    else:
        raise ValueError('masks_to_flows only takes 2D or 3D arrays')




@njit(['(int16[:,:,:], float32[:], float32[:], float32[:,:])',
       '(float32[:,:,:], float32[:], float32[:], float32[:,:])'], cache=True)
def map_coordinates_cpu(I, yc, xc, Y):
    """
    bilinear interpolation of image 'I' in-place with ycoordinates yc and xcoordinates xc to Y

    Parameters
    -------------
    I : C x Ly x Lx
    yc : ni
        new y coordinates
    xc : ni
        new x coordinates
    Y : C x ni
        I sampled at (yc,xc)
    """
    C, Ly, Lx = I.shape
    yc_floor = yc.astype(np.int32)
    xc_floor = xc.astype(np.int32)
    yc = yc - yc_floor
    xc = xc - xc_floor
    for i in range(yc_floor.shape[0]):
        yf = min(Ly - 1, max(0, yc_floor[i]))
        xf = min(Lx - 1, max(0, xc_floor[i]))
        yf1 = min(Ly - 1, yf + 1)
        xf1 = min(Lx - 1, xf + 1)
        y = yc[i]
        x = xc[i]
        for c in range(C):
            Y[c, i] = (np.float32(I[c, yf, xf]) * (1 - y) * (1 - x) +
                       np.float32(I[c, yf, xf1]) * (1 - y) * x +
                       np.float32(I[c, yf1, xf]) * y * (1 - x) +
                       np.float32(I[c, yf1, xf1]) * y * x)



def map_coordinates_cupy(I, yc, xc, Y):
    """
    Bilinear interpolation of image 'I' using CuPy with y-coordinates yc and x-coordinates xc to Y.
    """
    C, Ly, Lx = I.shape
    yc_floor = cp.floor(yc).astype(cp.int32)
    xc_floor = cp.floor(xc).astype(cp.int32)
    yc = yc - yc_floor
    xc = xc - xc_floor
    yf = cp.clip(yc_floor, 0, Ly - 1)
    xf = cp.clip(xc_floor, 0, Lx - 1)
    yf1 = cp.clip(yf + 1, 0, Ly - 1)
    xf1 = cp.clip(xf + 1, 0, Lx - 1)
    y = yc
    x = xc
    for c in range(C):
        Y[c] = (I[c, yf, xf] * (1 - y) * (1 - x) +
                I[c, yf, xf1] * (1 - y) * x +
                I[c, yf1, xf] * y * (1 - x) +
                I[c, yf1, xf1] * y * x)
def steps2D_interp(p, dP, niter, use_gpu=False,device=None):
    if use_gpu:
        print("steps2D_interp use_gpu")
        p_gpu = cp.asarray(p.copy(), dtype=cp.float32)
        dP_gpu = cp.asarray(dP, dtype=cp.float32)
        shape = dP_gpu.shape[1:]
        dPt_gpu = cp.zeros_like(p_gpu, dtype=cp.float32)
        for t in range(niter):
            #map_coordinates_cupy(dP_gpu, p_gpu[0], p_gpu[1], dPt_gpu)
            #print(dP_gpu.shape,p_gpu[0].shape,p_gpu[1].shape,p_gpu.shape,dPt_gpu.shape) #(2, 2160, 3840) (2635837,) (2635837,) (2, 2635837) (2, 2635837)
            dPt_gpu[0]  = cusi_map_coordinates(dP_gpu[0], coordinates=p_gpu, order=1, mode='constant', cval=0.0,
                                           prefilter=False).reshape(1,-1)
            dPt_gpu[1]  = cusi_map_coordinates(dP_gpu[1], coordinates=p_gpu, order=1, mode='constant', cval=0.0,
                                           prefilter=False).reshape(1,-1)
            #print(dP_gpu.shape,p_gpu[0].shape,p_gpu[1].shape,p_gpu.shape,dPt_gpu.shape) #(2, 2160, 3840) (2635837,) (2635837,) (2, 2635837) (2635837,)
            for k in range(len(p_gpu)):
                p_gpu[k] = cp.minimum(shape[k] - 1, cp.maximum(0, p_gpu[k] + dPt_gpu[k]))
        return cp.asnumpy(p_gpu)
    else:
        print("steps2D_interp use_cpu")
        shape = dP.shape[1:]
        dPt = np.zeros(p.shape, np.float32)
        for t in range(niter):
            map_coordinates_cpu(dP.astype(np.float32), p[0], p[1], dPt)
            #print(dP.shape,p[0].shape,p[1].shape) #(2, 2160, 3840) (2635837,) (2635837,)
            for k in range(len(p)):
                p[k] = np.minimum(shape[k] - 1, np.maximum(0, p[k] + dPt[k]))
        return p

@njit('(float32[:,:,:,:],float32[:,:,:,:], int32[:,:], int32)', nogil=True)
def steps3D(p, dP, inds, niter):
    """ run dynamics of pixels to recover masks in 3D

    Euler integration of dynamics dP for niter steps

    Parameters
    ----------------

    p: float32, 4D array
        pixel locations [axis x Lz x Ly x Lx] (start at initial meshgrid)

    dP: float32, 4D array
        flows [axis x Lz x Ly x Lx]

    inds: int32, 2D array
        non-zero pixels to run dynamics on [npixels x 3]

    niter: int32
        number of iterations of dynamics to run

    Returns
    ---------------

    p: float32, 4D array
        final locations of each pixel after dynamics

    """
    shape = p.shape[1:]
    for t in range(niter):
        # pi = p.astype(np.int32)
        for j in range(inds.shape[0]):
            z = inds[j, 0]
            y = inds[j, 1]
            x = inds[j, 2]
            p0, p1, p2 = int(p[0, z, y, x]), int(p[1, z, y, x]), int(p[2, z, y, x])
            p[0, z, y, x] = min(shape[0] - 1, max(0, p[0, z, y, x] + dP[0, p0, p1, p2]))
            p[1, z, y, x] = min(shape[1] - 1, max(0, p[1, z, y, x] + dP[1, p0, p1, p2]))
            p[2, z, y, x] = min(shape[2] - 1, max(0, p[2, z, y, x] + dP[2, p0, p1, p2]))
    return p


def steps2D(p, dP, inds, niter):
    """ run dynamics of pixels to recover masks in 2D

    Euler integration of dynamics dP for niter steps

    Parameters
    ----------------

    p: float32, 3D array
        pixel locations [axis x Ly x Lx] (start at initial meshgrid)

    dP: float32, 3D array
        flows [axis x Ly x Lx]

    inds: int32, 2D array
        non-zero pixels to run dynamics on [npixels x 2]

    niter: int32
        number of iterations of dynamics to run

    Returns
    ---------------

    p: float32, 3D array
        final locations of each pixel after dynamics

    """
    p_gpu = cp.asarray(p)
    dP_gpu = cp.asarray(dP)
    inds_gpu = cp.asarray(inds)
    shape = p_gpu.shape[1:]

    for t in range(niter):
        for j in range(inds_gpu.shape[0]):
            # starting coordinates
            y = inds_gpu[j, 0]
            x = inds_gpu[j, 1]
            p0, p1 = int(p_gpu[0, y, x]), int(p_gpu[1, y, x])
            step = dP_gpu[:, p0, p1]
            for k in range(p_gpu.shape[0]):
                p_gpu[k, y, x] = cp.minimum(shape[k] - 1, cp.maximum(0, p_gpu[k, y, x] + step[k]))
    return cp.asnumpy(p_gpu)

def follow_flows(dP, mask=None, niter=200, interp=True, use_gpu=True, device=None):
    """ define pixels and run dynamics to recover masks in 2D

    Pixels are meshgrid. Only pixels with non-zero cell-probability
    are used (as defined by inds)

    Parameters
    ----------------

    dP: float32, 3D or 4D array
        flows [axis x Ly x Lx] or [axis x Lz x Ly x Lx]

    mask: (optional, default None)
        pixel mask to seed masks. Useful when flows have low magnitudes.

    niter: int (optional, default 200)
        number of iterations of dynamics to run

    interp: bool (optional, default True)
        interpolate during 2D dynamics (not available in 3D)
        (in previous versions + paper it was False)

    use_gpu: bool (optional, default False)
        use GPU to run interpolated dynamics (faster than CPU)


    Returns
    ---------------

    p: float32, 3D or 4D array
        final locations of each pixel after dynamics; [axis x Ly x Lx] or [axis x Lz x Ly x Lx]

    inds: int32, 3D or 4D array
        indices of pixels used for dynamics; [axis x Ly x Lx] or [axis x Lz x Ly x Lx]
        :param dP:
        :param mask:
        :param niter:
        :param interp:
        :param use_gpu:
        :param device:

    """
    shape = np.array(dP.shape[1:]).astype(np.int32)
    niter = np.uint32(niter)
    if len(shape) > 2:
        p = np.meshgrid(np.arange(shape[0]), np.arange(shape[1]),
                        np.arange(shape[2]), indexing='ij')
        p = np.array(p).astype(np.float32)
        # run dynamics on subset of pixels
        inds = np.array(np.nonzero(np.abs(dP[0]) > 1e-3)).astype(np.int32).T
        p = steps3D(p, dP, inds, niter)
    else:
        p = np.meshgrid(np.arange(shape[0]), np.arange(shape[1]), indexing='ij')
        p = np.array(p).astype(np.float32)

        inds = np.array(np.nonzero(np.abs(dP[0]) > 1e-3)).astype(np.int32).T

        if inds.ndim < 2 or inds.shape[0] < 5:
            logger.warning('WARNING: no mask pixels found')
            return p, None

        if not interp:
            p = steps2D(p, dP.astype(np.float32), inds, niter)
        else:
            p_interp = steps2D_interp(p[:, inds[:, 0], inds[:, 1]], dP, niter, use_gpu=use_gpu, device=device)
            p[:, inds[:, 0], inds[:, 1]] = p_interp
    return p, inds


def flow_error(maski, dP_net, use_gpu=False, device=None):
    """ error in flows from predicted masks vs flows predicted by network run on image

    This function serves to benchmark the quality of masks, it works as follows
    1. The predicted masks are used to create a flow diagram
    2. The mask-flows are compared to the flows that the network predicted

    If there is a discrepancy between the flows, it suggests that the mask is incorrect.
    Masks with flow_errors greater than 0.4 are discarded by default. Setting can be
    changed in Cellpose.eval or CellposeModel.eval.

    Parameters
    ------------

    maski: ND-array (int)
        masks produced from running dynamics on dP_net,
        where 0=NO masks; 1,2... are mask labels
    dP_net: ND-array (float)
        ND flows where dP_net.shape[1:] = maski.shape

    Returns
    ------------

    flow_errors: float array with length maski.max()
        mean squared error between predicted flows and flows from masks
    dP_masks: ND-array (float)
        ND flows produced from the predicted masks
        :param maski:
        :param dP_net:
        :param use_gpu:
        :param device:

    """
    if dP_net.shape[1:] != maski.shape:
        print('ERROR: net flow is not same size as predicted masks')
        return
    print(f"flow_error using gpu {use_gpu}")
    start_time = time.time()
    # flows predicted from estimated masks
    dP_masks = masks_to_flows(maski, use_gpu=use_gpu, device=device)
    logger.info(f'masks_to_flows took {time.time() - start_time:.2f} seconds')
    # difference between predicted flows vs mask flows
    flow_errors = np.zeros(maski.max())
    for i in range(dP_masks.shape[0]):
        flow_errors += mean((dP_masks[i] - dP_net[i] / 5.) ** 2, maski,
                            index=np.arange(1, maski.max() + 1))
    return flow_errors, dP_masks
def remove_bad_flow_masks(masks, flows, threshold=0.4, use_gpu=False, device=None):
    """ remove masks which have inconsistent flows

    Uses metrics.flow_error to compute flows from predicted masks
    and compare flows to predicted flows from network. Discards
    masks with flow errors greater than the threshold.

    Parameters
    ----------------

    masks: int, 2D or 3D array
        labelled masks, 0=NO masks; 1,2,...=mask labels,
        size [Ly x Lx] or [Lz x Ly x Lx]

    flows: float, 3D or 4D array
        flows [axis x Ly x Lx] or [axis x Lz x Ly x Lx]

    threshold: float (optional, default 0.4)
        masks with flow error greater than threshold are discarded.

    Returns
    ---------------

    masks: int, 2D or 3D array
        masks with inconsistent flow masks removed,
        0=NO masks; 1,2,...=mask labels,
        size [Ly x Lx] or [Lz x Ly x Lx]
        :param masks:
        :param flows:
        :param threshold:
        :param use_gpu:
        :param device:

    """
    merrors, _ = flow_error(masks, flows, use_gpu, device)
    badi = 1 + (merrors > threshold).nonzero()[0]
    masks[np.isin(masks, badi)] = 0
    return masks


def get_masks(p, iscell=None, rpad=20):


    pflows = []
    edges = []
    shape0 = p.shape[1:]
    dims = len(p)
    if iscell is not None:
        if dims == 3:
            inds = np.meshgrid(np.arange(shape0[0]), np.arange(shape0[1]),
                               np.arange(shape0[2]), indexing='ij')
        elif dims == 2:
            inds = np.meshgrid(np.arange(shape0[0]), np.arange(shape0[1]),
                               indexing='ij')
        for i in range(dims):
            p[i, ~iscell] = inds[i][~iscell]

    for i in range(dims):
        pflows.append(p[i].flatten().astype('int32'))
        edges.append(np.arange(-.5 - rpad, shape0[i] + .5 + rpad, 1))

    h, _ = np.histogramdd(tuple(pflows), bins=edges)
    hmax = h.copy()
    for i in range(dims):
        hmax = maximum_filter1d(hmax, 5, axis=i)

    seeds = np.nonzero(np.logical_and(h - hmax > -1e-6, h > 10))
    Nmax = h[seeds]
    isort = np.argsort(Nmax)[::-1]
    for s in seeds:
        s = s[isort]

    pix = list(np.array(seeds).T)

    shape = h.shape
    if dims == 3:
        expand = np.nonzero(np.ones((3, 3, 3)))
    else:
        expand = np.nonzero(np.ones((3, 3)))
    for e in expand:
        e = np.expand_dims(e, 1)

    for iter in range(5):
        for k in range(len(pix)):
            if iter == 0:
                pix[k] = list(pix[k])
            newpix = []
            iin = []
            for i, e in enumerate(expand):
                epix = e[:, np.newaxis] + np.expand_dims(pix[k][i], 0) - 1
                epix = epix.flatten()
                iin.append(np.logical_and(epix >= 0, epix < shape[i]))
                newpix.append(epix)
            iin = np.all(tuple(iin), axis=0)
            for p in newpix:
                p = p[iin]
            newpix = tuple(newpix)
            igood = h[newpix] > 2
            for i in range(dims):
                pix[k][i] = newpix[i][igood]
            if iter == 4:
                pix[k] = tuple(pix[k])

    M = np.zeros(h.shape, np.uint32)
    for k in range(len(pix)):
        M[pix[k]] = 1 + k

    for i in range(dims):
        pflows[i] = pflows[i] + rpad
    M0 = M[tuple(pflows)]

    # remove big masks
    uniq, counts = fastremap.unique(M0, return_counts=True)
    big = np.prod(shape0) * 0.4
    bigc = uniq[counts > big]
    if len(bigc) > 0 and (len(bigc) > 1 or bigc[0] != 0):
        M0 = fastremap.mask(M0, bigc)
    fastremap.renumber(M0, in_place=True)  # convenient to guarantee non-skipped labels
    M0 = np.reshape(M0, shape0)
    return M0

def resize_and_compute_masks(dP, cellprob, p=None, niter=200,
                             cellprob_threshold=0.0,
                             flow_threshold=0.4, interp=True, do_3D=False,
                             min_size=15, resize=None,
                             use_gpu=False, device=None):
    """ compute masks using dynamics from dP, cellprob, and boundary """
    mask, p = compute_masks(dP, cellprob, p=p, niter=niter,
                            cellprob_threshold=cellprob_threshold,
                            flow_threshold=flow_threshold, interp=interp,
                            do_3D=do_3D, min_size=min_size,
                            use_gpu=use_gpu, device=device)

    if resize is not None:
        mask = transforms.resize_image(mask, resize[0], resize[1], interpolation=cv2.INTER_NEAREST)
        p = np.array([transforms.resize_image(pi, resize[0], resize[1], interpolation=cv2.INTER_NEAREST) for pi in p])

    return mask, p


def compute_masks(dP, cellprob, p=None, niter=200,
                  cellprob_threshold=0.0,
                  flow_threshold=0.4, interp=True, do_3D=False,
                  min_size=15, resize=None,
                  use_gpu=False, device=None):
    """ compute masks using dynamics from dP, cellprob, and boundary """

    cp_mask = cellprob > cellprob_threshold

    if np.any(cp_mask):  # mask at this point is a cell cluster binary map, not labels
        # follow flows
        start_time=time.time()
        if p is None:
            p, inds = follow_flows(dP * cp_mask / 5., niter=niter, interp=interp,
                                   use_gpu=use_gpu, device=device)
            if inds is None:
                logger.info('No cell pixels found.')
                shape = resize if resize is not None else cellprob.shape
                mask = np.zeros(shape, np.uint16)
                p = np.zeros((len(shape), *shape), np.uint16)
                return mask, p
        print(f'follow_flows took {time.time()-start_time:.2f} seconds')
        # calculate masks
        start_time=time.time()
        mask = get_masks(p, iscell=cp_mask)
        print(f'get_masks took {time.time()-start_time:.2f} seconds')
        # flow thresholding factored out of get_masks
        start_time2 = time.time()
        if not do_3D:
            shape0 = p.shape[1:]
            start_time = time.time()
            if mask.max() > 0 and flow_threshold is not None and flow_threshold > 0:
                # make sure labels are unique at output of get_masks
                try:
                    mask = remove_bad_flow_masks(mask, dP, threshold=flow_threshold, use_gpu=use_gpu, device=device)
                    print(f'remove_bad_flow_masks took {time.time() - start_time:.2f} seconds')
                except Exception as e:
                    logger.error(f'remove_bad_flow_masks failed with {e}')
        if resize is not None:
            # if verbose:
            #    logger.info(f'resizing output with resize = {resize}')
            if mask.max() > 2 ** 16 - 1:
                recast = True
                mask = mask.astype(np.float32)
            else:
                recast = False
                mask = mask.astype(np.uint16)
            mask = transforms.resize_image(mask, resize[0], resize[1], interpolation=cv2.INTER_NEAREST)
            if recast:
                mask = mask.astype(np.uint32)
            Ly, Lx = mask.shape
        elif mask.max() < 2 ** 16:
            mask = mask.astype(np.uint16)
    else:  # nothing to compute, just make it compatible
        logger.info('No cell pixels found.')
        shape = resize if resize is not None else cellprob.shape
        mask = np.zeros(shape, np.uint16)
        p = np.zeros((len(shape), *shape), np.uint16)
        return mask, p

    # moving the cleanup to the end helps avoid some bugs arising from scaling...
    # maybe better would be to rescale the min_size and hole_size parameters to do the
    # cleanup at the prediction scale, or switch depending on which one is bigger...
    mask = transforms.fill_holes_and_remove_small_masks(mask, min_size=min_size)
    if mask.dtype == np.uint32:
        logger.warning('more than 65535 masks in image, masks returned as np.uint32')
    return mask, p
