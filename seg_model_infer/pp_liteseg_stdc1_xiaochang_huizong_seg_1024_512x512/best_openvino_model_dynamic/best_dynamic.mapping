<?xml version="1.0"?>
<mapping>
	<map>
		<framework name="x" output_port_id="x" />
		<IR name="x" output_port_id="0" />
	</map>
	<map>
		<framework name="BatchNormalization_0" output_port_id="batch_norm_0.tmp_2" />
		<IR name="BatchNormalization_0" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_0" output_port_id="relu_0.tmp_0" />
		<IR name="Relu_0" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_1" output_port_id="batch_norm_1.tmp_2" />
		<IR name="BatchNormalization_1" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_1" output_port_id="relu_1.tmp_0" />
		<IR name="Relu_1" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_2" output_port_id="batch_norm_2.tmp_2" />
		<IR name="BatchNormalization_2" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_2" output_port_id="relu_2.tmp_0" />
		<IR name="Relu_2" output_port_id="1" />
	</map>
	<map>
		<framework name="AveragePool_0" output_port_id="pool2d_0.tmp_0" />
		<IR name="AveragePool_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Multiply_13361" output_port_id="depthwise_conv2d_0.tmp_0" />
		<IR name="Multiply_13361" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_3" output_port_id="batch_norm_3.tmp_2" />
		<IR name="BatchNormalization_3" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_4" output_port_id="batch_norm_4.tmp_2" />
		<IR name="BatchNormalization_4" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_3" output_port_id="relu_3.tmp_0" />
		<IR name="Relu_3" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_5" output_port_id="batch_norm_5.tmp_2" />
		<IR name="BatchNormalization_5" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_4" output_port_id="relu_4.tmp_0" />
		<IR name="Relu_4" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_6" output_port_id="batch_norm_6.tmp_2" />
		<IR name="BatchNormalization_6" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_5" output_port_id="relu_5.tmp_0" />
		<IR name="Relu_5" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_0" output_port_id="concat_0.tmp_0" />
		<IR name="Concat_0" output_port_id="4" />
	</map>
	<map>
		<framework name="BatchNormalization_7" output_port_id="batch_norm_7.tmp_2" />
		<IR name="BatchNormalization_7" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_6" output_port_id="relu_6.tmp_0" />
		<IR name="Relu_6" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_8" output_port_id="batch_norm_8.tmp_2" />
		<IR name="BatchNormalization_8" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_7" output_port_id="relu_7.tmp_0" />
		<IR name="Relu_7" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_9" output_port_id="batch_norm_9.tmp_2" />
		<IR name="BatchNormalization_9" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_8" output_port_id="relu_8.tmp_0" />
		<IR name="Relu_8" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_10" output_port_id="batch_norm_10.tmp_2" />
		<IR name="BatchNormalization_10" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_9" output_port_id="relu_9.tmp_0" />
		<IR name="Relu_9" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_1" output_port_id="concat_1.tmp_0" />
		<IR name="Concat_1" output_port_id="4" />
	</map>
	<map>
		<framework name="BatchNormalization_39" output_port_id="batch_norm_39.tmp_2" />
		<IR name="BatchNormalization_39" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_34" output_port_id="relu_34.tmp_0" />
		<IR name="Relu_34" output_port_id="1" />
	</map>
	<map>
		<framework name="ReduceMean_4" output_port_id="mean_4.tmp_0" />
		<IR name="ReduceMean_4" output_port_id="2" />
	</map>
	<map>
		<framework name="ReduceMax_4" output_port_id="max_4.tmp_0" />
		<IR name="ReduceMax_4" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_11" output_port_id="batch_norm_11.tmp_2" />
		<IR name="BatchNormalization_11" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_10" output_port_id="relu_10.tmp_0" />
		<IR name="Relu_10" output_port_id="1" />
	</map>
	<map>
		<framework name="AveragePool_1" output_port_id="pool2d_1.tmp_0" />
		<IR name="AveragePool_1" output_port_id="1" />
	</map>
	<map>
		<framework name="Multiply_13431" output_port_id="depthwise_conv2d_1.tmp_0" />
		<IR name="Multiply_13431" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_12" output_port_id="batch_norm_12.tmp_2" />
		<IR name="BatchNormalization_12" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_13" output_port_id="batch_norm_13.tmp_2" />
		<IR name="BatchNormalization_13" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_11" output_port_id="relu_11.tmp_0" />
		<IR name="Relu_11" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_14" output_port_id="batch_norm_14.tmp_2" />
		<IR name="BatchNormalization_14" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_12" output_port_id="relu_12.tmp_0" />
		<IR name="Relu_12" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_15" output_port_id="batch_norm_15.tmp_2" />
		<IR name="BatchNormalization_15" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_13" output_port_id="relu_13.tmp_0" />
		<IR name="Relu_13" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_2" output_port_id="concat_2.tmp_0" />
		<IR name="Concat_2" output_port_id="4" />
	</map>
	<map>
		<framework name="BatchNormalization_16" output_port_id="batch_norm_16.tmp_2" />
		<IR name="BatchNormalization_16" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_14" output_port_id="relu_14.tmp_0" />
		<IR name="Relu_14" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_17" output_port_id="batch_norm_17.tmp_2" />
		<IR name="BatchNormalization_17" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_15" output_port_id="relu_15.tmp_0" />
		<IR name="Relu_15" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_18" output_port_id="batch_norm_18.tmp_2" />
		<IR name="BatchNormalization_18" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_16" output_port_id="relu_16.tmp_0" />
		<IR name="Relu_16" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_19" output_port_id="batch_norm_19.tmp_2" />
		<IR name="BatchNormalization_19" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_17" output_port_id="relu_17.tmp_0" />
		<IR name="Relu_17" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_3" output_port_id="concat_3.tmp_0" />
		<IR name="Concat_3" output_port_id="4" />
	</map>
	<map>
		<framework name="BatchNormalization_35" output_port_id="batch_norm_35.tmp_2" />
		<IR name="BatchNormalization_35" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_31" output_port_id="relu_31.tmp_0" />
		<IR name="Relu_31" output_port_id="1" />
	</map>
	<map>
		<framework name="ReduceMean_2" output_port_id="mean_2.tmp_0" />
		<IR name="ReduceMean_2" output_port_id="2" />
	</map>
	<map>
		<framework name="ReduceMax_2" output_port_id="max_2.tmp_0" />
		<IR name="ReduceMax_2" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_20" output_port_id="batch_norm_20.tmp_2" />
		<IR name="BatchNormalization_20" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_18" output_port_id="relu_18.tmp_0" />
		<IR name="Relu_18" output_port_id="1" />
	</map>
	<map>
		<framework name="AveragePool_2" output_port_id="pool2d_2.tmp_0" />
		<IR name="AveragePool_2" output_port_id="1" />
	</map>
	<map>
		<framework name="Multiply_13501" output_port_id="depthwise_conv2d_2.tmp_0" />
		<IR name="Multiply_13501" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_21" output_port_id="batch_norm_21.tmp_2" />
		<IR name="BatchNormalization_21" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_22" output_port_id="batch_norm_22.tmp_2" />
		<IR name="BatchNormalization_22" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_19" output_port_id="relu_19.tmp_0" />
		<IR name="Relu_19" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_23" output_port_id="batch_norm_23.tmp_2" />
		<IR name="BatchNormalization_23" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_20" output_port_id="relu_20.tmp_0" />
		<IR name="Relu_20" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_24" output_port_id="batch_norm_24.tmp_2" />
		<IR name="BatchNormalization_24" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_21" output_port_id="relu_21.tmp_0" />
		<IR name="Relu_21" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_4" output_port_id="concat_4.tmp_0" />
		<IR name="Concat_4" output_port_id="4" />
	</map>
	<map>
		<framework name="BatchNormalization_25" output_port_id="batch_norm_25.tmp_2" />
		<IR name="BatchNormalization_25" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_22" output_port_id="relu_22.tmp_0" />
		<IR name="Relu_22" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_26" output_port_id="batch_norm_26.tmp_2" />
		<IR name="BatchNormalization_26" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_23" output_port_id="relu_23.tmp_0" />
		<IR name="Relu_23" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_27" output_port_id="batch_norm_27.tmp_2" />
		<IR name="BatchNormalization_27" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_24" output_port_id="relu_24.tmp_0" />
		<IR name="Relu_24" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_28" output_port_id="batch_norm_28.tmp_2" />
		<IR name="BatchNormalization_28" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_25" output_port_id="relu_25.tmp_0" />
		<IR name="Relu_25" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_5" output_port_id="concat_5.tmp_0" />
		<IR name="Concat_5" output_port_id="4" />
	</map>
	<map>
		<framework name="BatchNormalization_31" output_port_id="batch_norm_31.tmp_2" />
		<IR name="BatchNormalization_31" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_28" output_port_id="relu_28.tmp_0" />
		<IR name="Relu_28" output_port_id="1" />
	</map>
	<map>
		<framework name="ReduceMean_0" output_port_id="mean_0.tmp_0" />
		<IR name="ReduceMean_0" output_port_id="2" />
	</map>
	<map>
		<framework name="ReduceMax_0" output_port_id="max_0.tmp_0" />
		<IR name="ReduceMax_0" output_port_id="2" />
	</map>
	<map>
		<framework name="GlobalAveragePool_0" output_port_id="pool2d_3.tmp_0" />
		<IR name="GlobalAveragePool_0" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_29" output_port_id="batch_norm_29.tmp_2" />
		<IR name="BatchNormalization_29" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_26" output_port_id="relu_26.tmp_0" />
		<IR name="Relu_26" output_port_id="1" />
	</map>
	<map>
		<framework name="Shape_2" output_port_id="Shape_2" />
		<IR name="Shape_2" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_10" output_port_id="Constant_10" />
		<IR name="Constant_10" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_11" output_port_id="Constant_11" />
		<IR name="Constant_11" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_2" output_port_id="Slice_2" />
		<IR name="Slice_2" output_port_id="4" />
	</map>
	<map>
		<framework name="Shape_1" output_port_id="Shape_1" />
		<IR name="Shape_1" output_port_id="1" />
	</map>
	<map>
		<framework name="Cast_1" output_port_id="shape_1.tmp_0" />
		<IR name="Cast_1" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_4" output_port_id="Constant_4" />
		<IR name="Constant_4" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_5" output_port_id="Constant_5" />
		<IR name="Constant_5" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_6" output_port_id="Constant_6" />
		<IR name="Constant_6" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_1" output_port_id="shape_1.tmp_0_slice_0" />
		<IR name="Slice_1" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_2" output_port_id="Cast_2" />
		<IR name="Cast_2" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_6" output_port_id="Concat_6" />
		<IR name="Concat_6" output_port_id="2" />
	</map>
	<map>
		<framework name="Resize_0" output_port_id="bilinear_interp_v2_0.tmp_0" />
		<IR name="Resize_0" output_port_id="3" />
	</map>
	<map>
		<framework name="BatchNormalization_30" output_port_id="batch_norm_30.tmp_2" />
		<IR name="BatchNormalization_30" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_27" output_port_id="relu_27.tmp_0" />
		<IR name="Relu_27" output_port_id="1" />
	</map>
	<map>
		<framework name="Shape_4" output_port_id="Shape_4" />
		<IR name="Shape_4" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_19" output_port_id="Constant_19" />
		<IR name="Constant_19" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_20" output_port_id="Constant_20" />
		<IR name="Constant_20" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_4" output_port_id="Slice_4" />
		<IR name="Slice_4" output_port_id="4" />
	</map>
	<map>
		<framework name="Shape_3" output_port_id="Shape_3" />
		<IR name="Shape_3" output_port_id="1" />
	</map>
	<map>
		<framework name="Cast_3" output_port_id="shape_2.tmp_0" />
		<IR name="Cast_3" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_14" output_port_id="Constant_14" />
		<IR name="Constant_14" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_15" output_port_id="Constant_15" />
		<IR name="Constant_15" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_16" output_port_id="Constant_16" />
		<IR name="Constant_16" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_3" output_port_id="shape_2.tmp_0_slice_0" />
		<IR name="Slice_3" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_4" output_port_id="Cast_4" />
		<IR name="Cast_4" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_7" output_port_id="Concat_7" />
		<IR name="Concat_7" output_port_id="2" />
	</map>
	<map>
		<framework name="Resize_1" output_port_id="bilinear_interp_v2_1.tmp_0" />
		<IR name="Resize_1" output_port_id="3" />
	</map>
	<map>
		<framework name="ReduceMean_1" output_port_id="mean_1.tmp_0" />
		<IR name="ReduceMean_1" output_port_id="2" />
	</map>
	<map>
		<framework name="ReduceMax_1" output_port_id="max_1.tmp_0" />
		<IR name="ReduceMax_1" output_port_id="2" />
	</map>
	<map>
		<framework name="Concat_8" output_port_id="concat_6.tmp_0" />
		<IR name="Concat_8" output_port_id="4" />
	</map>
	<map>
		<framework name="BatchNormalization_32" output_port_id="batch_norm_32.tmp_2" />
		<IR name="BatchNormalization_32" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_29" output_port_id="relu_29.tmp_0" />
		<IR name="Relu_29" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_33" output_port_id="batch_norm_33.tmp_2" />
		<IR name="BatchNormalization_33" output_port_id="2" />
	</map>
	<map>
		<framework name="Sigmoid_0" output_port_id="sigmoid_0.tmp_0" />
		<IR name="Sigmoid_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Mul_0" output_port_id="tmp_0" />
		<IR name="Mul_0" output_port_id="2" />
	</map>
	<map>
		<framework name="Add_2" output_port_id="tmp_1" />
		<IR name="Add_2" output_port_id="2" />
	</map>
	<map>
		<framework name="Mul_2" output_port_id="tmp_2" />
		<IR name="Mul_2" output_port_id="2" />
	</map>
	<map>
		<framework name="Add_3" output_port_id="tmp_3" />
		<IR name="Add_3" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_34" output_port_id="batch_norm_34.tmp_2" />
		<IR name="BatchNormalization_34" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_30" output_port_id="relu_30.tmp_0" />
		<IR name="Relu_30" output_port_id="1" />
	</map>
	<map>
		<framework name="Shape_6" output_port_id="Shape_6" />
		<IR name="Shape_6" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_29" output_port_id="Constant_29" />
		<IR name="Constant_29" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_30" output_port_id="Constant_30" />
		<IR name="Constant_30" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_6" output_port_id="Slice_6" />
		<IR name="Slice_6" output_port_id="4" />
	</map>
	<map>
		<framework name="Shape_5" output_port_id="Shape_5" />
		<IR name="Shape_5" output_port_id="1" />
	</map>
	<map>
		<framework name="Cast_5" output_port_id="shape_3.tmp_0" />
		<IR name="Cast_5" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_24" output_port_id="Constant_24" />
		<IR name="Constant_24" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_25" output_port_id="Constant_25" />
		<IR name="Constant_25" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_26" output_port_id="Constant_26" />
		<IR name="Constant_26" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_5" output_port_id="shape_3.tmp_0_slice_0" />
		<IR name="Slice_5" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_6" output_port_id="Cast_6" />
		<IR name="Cast_6" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_9" output_port_id="Concat_9" />
		<IR name="Concat_9" output_port_id="2" />
	</map>
	<map>
		<framework name="Resize_2" output_port_id="bilinear_interp_v2_2.tmp_0" />
		<IR name="Resize_2" output_port_id="3" />
	</map>
	<map>
		<framework name="ReduceMean_3" output_port_id="mean_3.tmp_0" />
		<IR name="ReduceMean_3" output_port_id="2" />
	</map>
	<map>
		<framework name="ReduceMax_3" output_port_id="max_3.tmp_0" />
		<IR name="ReduceMax_3" output_port_id="2" />
	</map>
	<map>
		<framework name="Concat_10" output_port_id="concat_7.tmp_0" />
		<IR name="Concat_10" output_port_id="4" />
	</map>
	<map>
		<framework name="BatchNormalization_36" output_port_id="batch_norm_36.tmp_2" />
		<IR name="BatchNormalization_36" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_32" output_port_id="relu_32.tmp_0" />
		<IR name="Relu_32" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_37" output_port_id="batch_norm_37.tmp_2" />
		<IR name="BatchNormalization_37" output_port_id="2" />
	</map>
	<map>
		<framework name="Sigmoid_1" output_port_id="sigmoid_1.tmp_0" />
		<IR name="Sigmoid_1" output_port_id="1" />
	</map>
	<map>
		<framework name="Mul_3" output_port_id="tmp_4" />
		<IR name="Mul_3" output_port_id="2" />
	</map>
	<map>
		<framework name="Add_4" output_port_id="tmp_5" />
		<IR name="Add_4" output_port_id="2" />
	</map>
	<map>
		<framework name="Mul_5" output_port_id="tmp_6" />
		<IR name="Mul_5" output_port_id="2" />
	</map>
	<map>
		<framework name="Add_5" output_port_id="tmp_7" />
		<IR name="Add_5" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_38" output_port_id="batch_norm_38.tmp_2" />
		<IR name="BatchNormalization_38" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_33" output_port_id="relu_33.tmp_0" />
		<IR name="Relu_33" output_port_id="1" />
	</map>
	<map>
		<framework name="Shape_8" output_port_id="Shape_8" />
		<IR name="Shape_8" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_39" output_port_id="Constant_39" />
		<IR name="Constant_39" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_40" output_port_id="Constant_40" />
		<IR name="Constant_40" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_8" output_port_id="Slice_8" />
		<IR name="Slice_8" output_port_id="4" />
	</map>
	<map>
		<framework name="Shape_7" output_port_id="Shape_7" />
		<IR name="Shape_7" output_port_id="1" />
	</map>
	<map>
		<framework name="Cast_7" output_port_id="shape_4.tmp_0" />
		<IR name="Cast_7" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_34" output_port_id="Constant_34" />
		<IR name="Constant_34" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_35" output_port_id="Constant_35" />
		<IR name="Constant_35" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_36" output_port_id="Constant_36" />
		<IR name="Constant_36" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_7" output_port_id="shape_4.tmp_0_slice_0" />
		<IR name="Slice_7" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_8" output_port_id="Cast_8" />
		<IR name="Cast_8" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_11" output_port_id="Concat_11" />
		<IR name="Concat_11" output_port_id="2" />
	</map>
	<map>
		<framework name="Resize_3" output_port_id="bilinear_interp_v2_3.tmp_0" />
		<IR name="Resize_3" output_port_id="3" />
	</map>
	<map>
		<framework name="ReduceMean_5" output_port_id="mean_5.tmp_0" />
		<IR name="ReduceMean_5" output_port_id="2" />
	</map>
	<map>
		<framework name="ReduceMax_5" output_port_id="max_5.tmp_0" />
		<IR name="ReduceMax_5" output_port_id="2" />
	</map>
	<map>
		<framework name="Concat_12" output_port_id="concat_8.tmp_0" />
		<IR name="Concat_12" output_port_id="4" />
	</map>
	<map>
		<framework name="BatchNormalization_40" output_port_id="batch_norm_40.tmp_2" />
		<IR name="BatchNormalization_40" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_35" output_port_id="relu_35.tmp_0" />
		<IR name="Relu_35" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_41" output_port_id="batch_norm_41.tmp_2" />
		<IR name="BatchNormalization_41" output_port_id="2" />
	</map>
	<map>
		<framework name="Sigmoid_2" output_port_id="sigmoid_2.tmp_0" />
		<IR name="Sigmoid_2" output_port_id="1" />
	</map>
	<map>
		<framework name="Mul_6" output_port_id="tmp_8" />
		<IR name="Mul_6" output_port_id="2" />
	</map>
	<map>
		<framework name="Add_6" output_port_id="tmp_9" />
		<IR name="Add_6" output_port_id="2" />
	</map>
	<map>
		<framework name="Mul_8" output_port_id="tmp_10" />
		<IR name="Mul_8" output_port_id="2" />
	</map>
	<map>
		<framework name="Add_7" output_port_id="tmp_11" />
		<IR name="Add_7" output_port_id="2" />
	</map>
	<map>
		<framework name="BatchNormalization_42" output_port_id="batch_norm_42.tmp_2" />
		<IR name="BatchNormalization_42" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_36" output_port_id="relu_36.tmp_0" />
		<IR name="Relu_36" output_port_id="1" />
	</map>
	<map>
		<framework name="BatchNormalization_43" output_port_id="batch_norm_43.tmp_2" />
		<IR name="BatchNormalization_43" output_port_id="2" />
	</map>
	<map>
		<framework name="Relu_37" output_port_id="relu_37.tmp_0" />
		<IR name="Relu_37" output_port_id="1" />
	</map>
	<map>
		<framework name="conv2d_44.w_0" output_port_id="conv2d_44.w_0" />
		<IR name="conv2d_44.w_0" output_port_id="0" />
	</map>
	<map>
		<framework name="Conv_44" output_port_id="conv2d_90.tmp_0" />
		<IR name="Conv_44" output_port_id="2" />
	</map>
	<map>
		<framework name="Shape_9" output_port_id="Shape_9" />
		<IR name="Shape_9" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_45" output_port_id="Constant_45" />
		<IR name="Constant_45" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_46" output_port_id="Constant_46" />
		<IR name="Constant_46" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_9" output_port_id="Slice_9" />
		<IR name="Slice_9" output_port_id="4" />
	</map>
	<map>
		<framework name="Shape_0" output_port_id="Shape_0" />
		<IR name="Shape_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Cast_0" output_port_id="shape_0.tmp_0" />
		<IR name="Cast_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_0" output_port_id="Constant_0" />
		<IR name="Constant_0" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_1" output_port_id="Constant_1" />
		<IR name="Constant_1" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_2" output_port_id="Constant_2" />
		<IR name="Constant_2" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_0" output_port_id="shape_0.tmp_0_slice_0" />
		<IR name="Slice_0" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_9" output_port_id="Cast_9" />
		<IR name="Cast_9" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_13" output_port_id="Concat_13" />
		<IR name="Concat_13" output_port_id="2" />
	</map>
	<map>
		<framework name="Resize_4" output_port_id="bilinear_interp_v2_4.tmp_0" />
		<IR name="Resize_4" output_port_id="3" />
	</map>
	<map>
		<framework name="argmax_0.tmp_0" output_port_id="argmax_0.tmp_0" />
		<IR name="argmax_0.tmp_0" output_port_id="2" />
	</map>
</mapping>
