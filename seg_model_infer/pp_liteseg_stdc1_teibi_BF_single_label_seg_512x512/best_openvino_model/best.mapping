<?xml version="1.0"?>
<mapping>
	<map>
		<framework name="x" output_port_id="x" />
		<IR name="x" output_port_id="0" />
	</map>
	<map>
		<framework name="batch_norm_0.tmp_2" output_port_id="batch_norm_0.tmp_2" />
		<IR name="batch_norm_0.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_0.tmp_0" output_port_id="relu_0.tmp_0" />
		<IR name="relu_0.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_1.tmp_2" output_port_id="batch_norm_1.tmp_2" />
		<IR name="batch_norm_1.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_1.tmp_0" output_port_id="relu_1.tmp_0" />
		<IR name="relu_1.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_2.tmp_2" output_port_id="batch_norm_2.tmp_2" />
		<IR name="batch_norm_2.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_2.tmp_0" output_port_id="relu_2.tmp_0" />
		<IR name="relu_2.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="pool2d_0.tmp_0" output_port_id="pool2d_0.tmp_0" />
		<IR name="pool2d_0.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Multiply_12331" output_port_id="depthwise_conv2d_0.tmp_0" />
		<IR name="Multiply_12331" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_3.tmp_2" output_port_id="batch_norm_3.tmp_2" />
		<IR name="batch_norm_3.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_4.tmp_2" output_port_id="batch_norm_4.tmp_2" />
		<IR name="batch_norm_4.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_3.tmp_0" output_port_id="relu_3.tmp_0" />
		<IR name="relu_3.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_5.tmp_2" output_port_id="batch_norm_5.tmp_2" />
		<IR name="batch_norm_5.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_4.tmp_0" output_port_id="relu_4.tmp_0" />
		<IR name="relu_4.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_6.tmp_2" output_port_id="batch_norm_6.tmp_2" />
		<IR name="batch_norm_6.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_5.tmp_0" output_port_id="relu_5.tmp_0" />
		<IR name="relu_5.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="concat_0.tmp_0" output_port_id="concat_0.tmp_0" />
		<IR name="concat_0.tmp_0" output_port_id="4" />
	</map>
	<map>
		<framework name="batch_norm_7.tmp_2" output_port_id="batch_norm_7.tmp_2" />
		<IR name="batch_norm_7.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_6.tmp_0" output_port_id="relu_6.tmp_0" />
		<IR name="relu_6.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_8.tmp_2" output_port_id="batch_norm_8.tmp_2" />
		<IR name="batch_norm_8.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_7.tmp_0" output_port_id="relu_7.tmp_0" />
		<IR name="relu_7.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_9.tmp_2" output_port_id="batch_norm_9.tmp_2" />
		<IR name="batch_norm_9.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_8.tmp_0" output_port_id="relu_8.tmp_0" />
		<IR name="relu_8.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_10.tmp_2" output_port_id="batch_norm_10.tmp_2" />
		<IR name="batch_norm_10.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_9.tmp_0" output_port_id="relu_9.tmp_0" />
		<IR name="relu_9.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="concat_1.tmp_0" output_port_id="concat_1.tmp_0" />
		<IR name="concat_1.tmp_0" output_port_id="4" />
	</map>
	<map>
		<framework name="batch_norm_41.tmp_2" output_port_id="batch_norm_41.tmp_2" />
		<IR name="batch_norm_41.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_36.tmp_0" output_port_id="relu_36.tmp_0" />
		<IR name="relu_36.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="mean_4.tmp_0" output_port_id="mean_4.tmp_0" />
		<IR name="mean_4.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="max_4.tmp_0" output_port_id="max_4.tmp_0" />
		<IR name="max_4.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_11.tmp_2" output_port_id="batch_norm_11.tmp_2" />
		<IR name="batch_norm_11.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_10.tmp_0" output_port_id="relu_10.tmp_0" />
		<IR name="relu_10.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="pool2d_1.tmp_0" output_port_id="pool2d_1.tmp_0" />
		<IR name="pool2d_1.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Multiply_12401" output_port_id="depthwise_conv2d_1.tmp_0" />
		<IR name="Multiply_12401" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_12.tmp_2" output_port_id="batch_norm_12.tmp_2" />
		<IR name="batch_norm_12.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_13.tmp_2" output_port_id="batch_norm_13.tmp_2" />
		<IR name="batch_norm_13.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_11.tmp_0" output_port_id="relu_11.tmp_0" />
		<IR name="relu_11.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_14.tmp_2" output_port_id="batch_norm_14.tmp_2" />
		<IR name="batch_norm_14.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_12.tmp_0" output_port_id="relu_12.tmp_0" />
		<IR name="relu_12.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_15.tmp_2" output_port_id="batch_norm_15.tmp_2" />
		<IR name="batch_norm_15.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_13.tmp_0" output_port_id="relu_13.tmp_0" />
		<IR name="relu_13.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="concat_2.tmp_0" output_port_id="concat_2.tmp_0" />
		<IR name="concat_2.tmp_0" output_port_id="4" />
	</map>
	<map>
		<framework name="batch_norm_16.tmp_2" output_port_id="batch_norm_16.tmp_2" />
		<IR name="batch_norm_16.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_14.tmp_0" output_port_id="relu_14.tmp_0" />
		<IR name="relu_14.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_17.tmp_2" output_port_id="batch_norm_17.tmp_2" />
		<IR name="batch_norm_17.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_15.tmp_0" output_port_id="relu_15.tmp_0" />
		<IR name="relu_15.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_18.tmp_2" output_port_id="batch_norm_18.tmp_2" />
		<IR name="batch_norm_18.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_16.tmp_0" output_port_id="relu_16.tmp_0" />
		<IR name="relu_16.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_19.tmp_2" output_port_id="batch_norm_19.tmp_2" />
		<IR name="batch_norm_19.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_17.tmp_0" output_port_id="relu_17.tmp_0" />
		<IR name="relu_17.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="concat_3.tmp_0" output_port_id="concat_3.tmp_0" />
		<IR name="concat_3.tmp_0" output_port_id="4" />
	</map>
	<map>
		<framework name="batch_norm_37.tmp_2" output_port_id="batch_norm_37.tmp_2" />
		<IR name="batch_norm_37.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_33.tmp_0" output_port_id="relu_33.tmp_0" />
		<IR name="relu_33.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="mean_2.tmp_0" output_port_id="mean_2.tmp_0" />
		<IR name="mean_2.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="max_2.tmp_0" output_port_id="max_2.tmp_0" />
		<IR name="max_2.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_20.tmp_2" output_port_id="batch_norm_20.tmp_2" />
		<IR name="batch_norm_20.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_18.tmp_0" output_port_id="relu_18.tmp_0" />
		<IR name="relu_18.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="pool2d_2.tmp_0" output_port_id="pool2d_2.tmp_0" />
		<IR name="pool2d_2.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Multiply_12471" output_port_id="depthwise_conv2d_2.tmp_0" />
		<IR name="Multiply_12471" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_21.tmp_2" output_port_id="batch_norm_21.tmp_2" />
		<IR name="batch_norm_21.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_22.tmp_2" output_port_id="batch_norm_22.tmp_2" />
		<IR name="batch_norm_22.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_19.tmp_0" output_port_id="relu_19.tmp_0" />
		<IR name="relu_19.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_23.tmp_2" output_port_id="batch_norm_23.tmp_2" />
		<IR name="batch_norm_23.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_20.tmp_0" output_port_id="relu_20.tmp_0" />
		<IR name="relu_20.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_24.tmp_2" output_port_id="batch_norm_24.tmp_2" />
		<IR name="batch_norm_24.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_21.tmp_0" output_port_id="relu_21.tmp_0" />
		<IR name="relu_21.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="concat_4.tmp_0" output_port_id="concat_4.tmp_0" />
		<IR name="concat_4.tmp_0" output_port_id="4" />
	</map>
	<map>
		<framework name="batch_norm_25.tmp_2" output_port_id="batch_norm_25.tmp_2" />
		<IR name="batch_norm_25.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_22.tmp_0" output_port_id="relu_22.tmp_0" />
		<IR name="relu_22.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_26.tmp_2" output_port_id="batch_norm_26.tmp_2" />
		<IR name="batch_norm_26.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_23.tmp_0" output_port_id="relu_23.tmp_0" />
		<IR name="relu_23.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_27.tmp_2" output_port_id="batch_norm_27.tmp_2" />
		<IR name="batch_norm_27.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_24.tmp_0" output_port_id="relu_24.tmp_0" />
		<IR name="relu_24.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_28.tmp_2" output_port_id="batch_norm_28.tmp_2" />
		<IR name="batch_norm_28.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_25.tmp_0" output_port_id="relu_25.tmp_0" />
		<IR name="relu_25.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="concat_5.tmp_0" output_port_id="concat_5.tmp_0" />
		<IR name="concat_5.tmp_0" output_port_id="4" />
	</map>
	<map>
		<framework name="batch_norm_33.tmp_2" output_port_id="batch_norm_33.tmp_2" />
		<IR name="batch_norm_33.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_30.tmp_0" output_port_id="relu_30.tmp_0" />
		<IR name="relu_30.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="mean_0.tmp_0" output_port_id="mean_0.tmp_0" />
		<IR name="mean_0.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="max_0.tmp_0" output_port_id="max_0.tmp_0" />
		<IR name="max_0.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="pool2d_3.tmp_0" output_port_id="pool2d_3.tmp_0" />
		<IR name="pool2d_3.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_29.tmp_2" output_port_id="batch_norm_29.tmp_2" />
		<IR name="batch_norm_29.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_26.tmp_0" output_port_id="relu_26.tmp_0" />
		<IR name="relu_26.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Shape_2" output_port_id="Shape_2" />
		<IR name="Shape_2" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_10" output_port_id="Constant_10" />
		<IR name="Constant_10" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_11" output_port_id="Constant_11" />
		<IR name="Constant_11" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_2" output_port_id="Slice_2" />
		<IR name="Slice_2" output_port_id="4" />
	</map>
	<map>
		<framework name="Shape_1" output_port_id="Shape_1" />
		<IR name="Shape_1" output_port_id="1" />
	</map>
	<map>
		<framework name="shape_1.tmp_0" output_port_id="shape_1.tmp_0" />
		<IR name="shape_1.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_4" output_port_id="Constant_4" />
		<IR name="Constant_4" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_5" output_port_id="Constant_5" />
		<IR name="Constant_5" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_6" output_port_id="Constant_6" />
		<IR name="Constant_6" output_port_id="0" />
	</map>
	<map>
		<framework name="shape_1.tmp_0_slice_0" output_port_id="shape_1.tmp_0_slice_0" />
		<IR name="shape_1.tmp_0_slice_0" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_2" output_port_id="Cast_2" />
		<IR name="Cast_2" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_6" output_port_id="Concat_6" />
		<IR name="Concat_6" output_port_id="2" />
	</map>
	<map>
		<framework name="bilinear_interp_v2_0.tmp_0" output_port_id="bilinear_interp_v2_0.tmp_0" />
		<IR name="bilinear_interp_v2_0.tmp_0" output_port_id="3" />
	</map>
	<map>
		<framework name="pool2d_4.tmp_0" output_port_id="pool2d_4.tmp_0" />
		<IR name="pool2d_4.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_30.tmp_2" output_port_id="batch_norm_30.tmp_2" />
		<IR name="batch_norm_30.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_27.tmp_0" output_port_id="relu_27.tmp_0" />
		<IR name="relu_27.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Shape_3" output_port_id="Shape_3" />
		<IR name="Shape_3" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_15" output_port_id="Constant_15" />
		<IR name="Constant_15" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_16" output_port_id="Constant_16" />
		<IR name="Constant_16" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_3" output_port_id="Slice_3" />
		<IR name="Slice_3" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_3" output_port_id="Cast_3" />
		<IR name="Cast_3" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_7" output_port_id="Concat_7" />
		<IR name="Concat_7" output_port_id="2" />
	</map>
	<map>
		<framework name="bilinear_interp_v2_1.tmp_0" output_port_id="bilinear_interp_v2_1.tmp_0" />
		<IR name="bilinear_interp_v2_1.tmp_0" output_port_id="3" />
	</map>
	<map>
		<framework name="tmp_0" output_port_id="tmp_0" />
		<IR name="tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="pool2d_5.tmp_0" output_port_id="pool2d_5.tmp_0" />
		<IR name="pool2d_5.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_31.tmp_2" output_port_id="batch_norm_31.tmp_2" />
		<IR name="batch_norm_31.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_28.tmp_0" output_port_id="relu_28.tmp_0" />
		<IR name="relu_28.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Shape_4" output_port_id="Shape_4" />
		<IR name="Shape_4" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_20" output_port_id="Constant_20" />
		<IR name="Constant_20" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_21" output_port_id="Constant_21" />
		<IR name="Constant_21" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_4" output_port_id="Slice_4" />
		<IR name="Slice_4" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_4" output_port_id="Cast_4" />
		<IR name="Cast_4" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_8" output_port_id="Concat_8" />
		<IR name="Concat_8" output_port_id="2" />
	</map>
	<map>
		<framework name="bilinear_interp_v2_2.tmp_0" output_port_id="bilinear_interp_v2_2.tmp_0" />
		<IR name="bilinear_interp_v2_2.tmp_0" output_port_id="3" />
	</map>
	<map>
		<framework name="tmp_1" output_port_id="tmp_1" />
		<IR name="tmp_1" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_32.tmp_2" output_port_id="batch_norm_32.tmp_2" />
		<IR name="batch_norm_32.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_29.tmp_0" output_port_id="relu_29.tmp_0" />
		<IR name="relu_29.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Shape_6" output_port_id="Shape_6" />
		<IR name="Shape_6" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_29" output_port_id="Constant_29" />
		<IR name="Constant_29" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_30" output_port_id="Constant_30" />
		<IR name="Constant_30" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_6" output_port_id="Slice_6" />
		<IR name="Slice_6" output_port_id="4" />
	</map>
	<map>
		<framework name="Shape_5" output_port_id="Shape_5" />
		<IR name="Shape_5" output_port_id="1" />
	</map>
	<map>
		<framework name="shape_2.tmp_0" output_port_id="shape_2.tmp_0" />
		<IR name="shape_2.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_24" output_port_id="Constant_24" />
		<IR name="Constant_24" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_25" output_port_id="Constant_25" />
		<IR name="Constant_25" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_26" output_port_id="Constant_26" />
		<IR name="Constant_26" output_port_id="0" />
	</map>
	<map>
		<framework name="shape_2.tmp_0_slice_0" output_port_id="shape_2.tmp_0_slice_0" />
		<IR name="shape_2.tmp_0_slice_0" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_6" output_port_id="Cast_6" />
		<IR name="Cast_6" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_9" output_port_id="Concat_9" />
		<IR name="Concat_9" output_port_id="2" />
	</map>
	<map>
		<framework name="bilinear_interp_v2_3.tmp_0" output_port_id="bilinear_interp_v2_3.tmp_0" />
		<IR name="bilinear_interp_v2_3.tmp_0" output_port_id="3" />
	</map>
	<map>
		<framework name="mean_1.tmp_0" output_port_id="mean_1.tmp_0" />
		<IR name="mean_1.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="max_1.tmp_0" output_port_id="max_1.tmp_0" />
		<IR name="max_1.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="concat_6.tmp_0" output_port_id="concat_6.tmp_0" />
		<IR name="concat_6.tmp_0" output_port_id="4" />
	</map>
	<map>
		<framework name="batch_norm_34.tmp_2" output_port_id="batch_norm_34.tmp_2" />
		<IR name="batch_norm_34.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_31.tmp_0" output_port_id="relu_31.tmp_0" />
		<IR name="relu_31.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_35.tmp_2" output_port_id="batch_norm_35.tmp_2" />
		<IR name="batch_norm_35.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="sigmoid_0.tmp_0" output_port_id="sigmoid_0.tmp_0" />
		<IR name="sigmoid_0.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="tmp_2" output_port_id="tmp_2" />
		<IR name="tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="tmp_3" output_port_id="tmp_3" />
		<IR name="tmp_3" output_port_id="2" />
	</map>
	<map>
		<framework name="tmp_4" output_port_id="tmp_4" />
		<IR name="tmp_4" output_port_id="2" />
	</map>
	<map>
		<framework name="tmp_5" output_port_id="tmp_5" />
		<IR name="tmp_5" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_36.tmp_2" output_port_id="batch_norm_36.tmp_2" />
		<IR name="batch_norm_36.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_32.tmp_0" output_port_id="relu_32.tmp_0" />
		<IR name="relu_32.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Shape_8" output_port_id="Shape_8" />
		<IR name="Shape_8" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_39" output_port_id="Constant_39" />
		<IR name="Constant_39" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_40" output_port_id="Constant_40" />
		<IR name="Constant_40" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_8" output_port_id="Slice_8" />
		<IR name="Slice_8" output_port_id="4" />
	</map>
	<map>
		<framework name="Shape_7" output_port_id="Shape_7" />
		<IR name="Shape_7" output_port_id="1" />
	</map>
	<map>
		<framework name="shape_3.tmp_0" output_port_id="shape_3.tmp_0" />
		<IR name="shape_3.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_34" output_port_id="Constant_34" />
		<IR name="Constant_34" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_35" output_port_id="Constant_35" />
		<IR name="Constant_35" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_36" output_port_id="Constant_36" />
		<IR name="Constant_36" output_port_id="0" />
	</map>
	<map>
		<framework name="shape_3.tmp_0_slice_0" output_port_id="shape_3.tmp_0_slice_0" />
		<IR name="shape_3.tmp_0_slice_0" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_8" output_port_id="Cast_8" />
		<IR name="Cast_8" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_11" output_port_id="Concat_11" />
		<IR name="Concat_11" output_port_id="2" />
	</map>
	<map>
		<framework name="bilinear_interp_v2_4.tmp_0" output_port_id="bilinear_interp_v2_4.tmp_0" />
		<IR name="bilinear_interp_v2_4.tmp_0" output_port_id="3" />
	</map>
	<map>
		<framework name="mean_3.tmp_0" output_port_id="mean_3.tmp_0" />
		<IR name="mean_3.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="max_3.tmp_0" output_port_id="max_3.tmp_0" />
		<IR name="max_3.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="concat_7.tmp_0" output_port_id="concat_7.tmp_0" />
		<IR name="concat_7.tmp_0" output_port_id="4" />
	</map>
	<map>
		<framework name="batch_norm_38.tmp_2" output_port_id="batch_norm_38.tmp_2" />
		<IR name="batch_norm_38.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_34.tmp_0" output_port_id="relu_34.tmp_0" />
		<IR name="relu_34.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_39.tmp_2" output_port_id="batch_norm_39.tmp_2" />
		<IR name="batch_norm_39.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="sigmoid_1.tmp_0" output_port_id="sigmoid_1.tmp_0" />
		<IR name="sigmoid_1.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="tmp_6" output_port_id="tmp_6" />
		<IR name="tmp_6" output_port_id="2" />
	</map>
	<map>
		<framework name="tmp_7" output_port_id="tmp_7" />
		<IR name="tmp_7" output_port_id="2" />
	</map>
	<map>
		<framework name="tmp_8" output_port_id="tmp_8" />
		<IR name="tmp_8" output_port_id="2" />
	</map>
	<map>
		<framework name="tmp_9" output_port_id="tmp_9" />
		<IR name="tmp_9" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_40.tmp_2" output_port_id="batch_norm_40.tmp_2" />
		<IR name="batch_norm_40.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_35.tmp_0" output_port_id="relu_35.tmp_0" />
		<IR name="relu_35.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Shape_10" output_port_id="Shape_10" />
		<IR name="Shape_10" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_49" output_port_id="Constant_49" />
		<IR name="Constant_49" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_50" output_port_id="Constant_50" />
		<IR name="Constant_50" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_10" output_port_id="Slice_10" />
		<IR name="Slice_10" output_port_id="4" />
	</map>
	<map>
		<framework name="Shape_9" output_port_id="Shape_9" />
		<IR name="Shape_9" output_port_id="1" />
	</map>
	<map>
		<framework name="shape_4.tmp_0" output_port_id="shape_4.tmp_0" />
		<IR name="shape_4.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_44" output_port_id="Constant_44" />
		<IR name="Constant_44" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_45" output_port_id="Constant_45" />
		<IR name="Constant_45" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_46" output_port_id="Constant_46" />
		<IR name="Constant_46" output_port_id="0" />
	</map>
	<map>
		<framework name="shape_4.tmp_0_slice_0" output_port_id="shape_4.tmp_0_slice_0" />
		<IR name="shape_4.tmp_0_slice_0" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_10" output_port_id="Cast_10" />
		<IR name="Cast_10" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_13" output_port_id="Concat_13" />
		<IR name="Concat_13" output_port_id="2" />
	</map>
	<map>
		<framework name="bilinear_interp_v2_5.tmp_0" output_port_id="bilinear_interp_v2_5.tmp_0" />
		<IR name="bilinear_interp_v2_5.tmp_0" output_port_id="3" />
	</map>
	<map>
		<framework name="mean_5.tmp_0" output_port_id="mean_5.tmp_0" />
		<IR name="mean_5.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="max_5.tmp_0" output_port_id="max_5.tmp_0" />
		<IR name="max_5.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="concat_8.tmp_0" output_port_id="concat_8.tmp_0" />
		<IR name="concat_8.tmp_0" output_port_id="4" />
	</map>
	<map>
		<framework name="batch_norm_42.tmp_2" output_port_id="batch_norm_42.tmp_2" />
		<IR name="batch_norm_42.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_37.tmp_0" output_port_id="relu_37.tmp_0" />
		<IR name="relu_37.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_43.tmp_2" output_port_id="batch_norm_43.tmp_2" />
		<IR name="batch_norm_43.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="sigmoid_2.tmp_0" output_port_id="sigmoid_2.tmp_0" />
		<IR name="sigmoid_2.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="tmp_10" output_port_id="tmp_10" />
		<IR name="tmp_10" output_port_id="2" />
	</map>
	<map>
		<framework name="tmp_11" output_port_id="tmp_11" />
		<IR name="tmp_11" output_port_id="2" />
	</map>
	<map>
		<framework name="tmp_12" output_port_id="tmp_12" />
		<IR name="tmp_12" output_port_id="2" />
	</map>
	<map>
		<framework name="tmp_13" output_port_id="tmp_13" />
		<IR name="tmp_13" output_port_id="2" />
	</map>
	<map>
		<framework name="batch_norm_44.tmp_2" output_port_id="batch_norm_44.tmp_2" />
		<IR name="batch_norm_44.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_38.tmp_0" output_port_id="relu_38.tmp_0" />
		<IR name="relu_38.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="batch_norm_45.tmp_2" output_port_id="batch_norm_45.tmp_2" />
		<IR name="batch_norm_45.tmp_2" output_port_id="2" />
	</map>
	<map>
		<framework name="relu_39.tmp_0" output_port_id="relu_39.tmp_0" />
		<IR name="relu_39.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="conv2d_47.w_0" output_port_id="conv2d_47.w_0" />
		<IR name="conv2d_47.w_0" output_port_id="0" />
	</map>
	<map>
		<framework name="conv2d_95.tmp_0" output_port_id="conv2d_95.tmp_0" />
		<IR name="conv2d_95.tmp_0" output_port_id="2" />
	</map>
	<map>
		<framework name="Shape_11" output_port_id="Shape_11" />
		<IR name="Shape_11" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_55" output_port_id="Constant_55" />
		<IR name="Constant_55" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_56" output_port_id="Constant_56" />
		<IR name="Constant_56" output_port_id="0" />
	</map>
	<map>
		<framework name="Slice_11" output_port_id="Slice_11" />
		<IR name="Slice_11" output_port_id="4" />
	</map>
	<map>
		<framework name="Shape_0" output_port_id="Shape_0" />
		<IR name="Shape_0" output_port_id="1" />
	</map>
	<map>
		<framework name="shape_0.tmp_0" output_port_id="shape_0.tmp_0" />
		<IR name="shape_0.tmp_0" output_port_id="1" />
	</map>
	<map>
		<framework name="Constant_0" output_port_id="Constant_0" />
		<IR name="Constant_0" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_1" output_port_id="Constant_1" />
		<IR name="Constant_1" output_port_id="0" />
	</map>
	<map>
		<framework name="Constant_2" output_port_id="Constant_2" />
		<IR name="Constant_2" output_port_id="0" />
	</map>
	<map>
		<framework name="shape_0.tmp_0_slice_0" output_port_id="shape_0.tmp_0_slice_0" />
		<IR name="shape_0.tmp_0_slice_0" output_port_id="4" />
	</map>
	<map>
		<framework name="Cast_11" output_port_id="Cast_11" />
		<IR name="Cast_11" output_port_id="1" />
	</map>
	<map>
		<framework name="Concat_15" output_port_id="Concat_15" />
		<IR name="Concat_15" output_port_id="2" />
	</map>
	<map>
		<framework name="bilinear_interp_v2_6.tmp_0" output_port_id="bilinear_interp_v2_6.tmp_0" />
		<IR name="bilinear_interp_v2_6.tmp_0" output_port_id="3" />
	</map>
	<map>
		<framework name="argmax_0.tmp_0" output_port_id="argmax_0.tmp_0" />
		<IR name="argmax_0.tmp_0" output_port_id="2" />
	</map>
</mapping>
