# single_cloneCV
## AI贴壁克隆团算法CV
### 版本:1.0.0.0
### 调用接口:
|参数名称|参数说明|参数类型|是否必填|备注|
|----|----|----|----|----|
|exp_type|实验类型|String|是|取值为“single_cloneCV”|
|input_image_path|输入图片地址|String|是|绝对路径|
|image_save_path|输出图片地址|String|是|绝对路径|
|data_save_path|统计数据输出地址|String|是|绝对路径,csv 格式|
|fcs_save_path|类流式分析数据输出地址|String|是|绝对路径,csv 格式|
|axis_save_path|克隆团外接矩形框输出地址|String|否|绝对路径,csv 格式|
|defect_ratio|细胞类型（凹陷阈值）|Float|否|预留参数:结团分割使用.凹陷阈值,为克隆团边缘凹陷区域面积与克隆团总面积的比值.大于该值的凹陷处进行分割.取值:(0, 1)|
|standard_day|克隆标准天（克隆团长宽比阈值）|Int|否|结团分割使用.长宽比小于该值的克隆团不进行分割处理.取值:大于 1.|
|cell_changed|更换细胞类型|Int|是|1 表示细胞类型更换,0 表示不更换|
|barcode|同一条码统一实验类型的所有实验 id,测试时间,条码组合|String|是|Eg:10,2022 - 06 - 16 14:05:57,fdf;11,2022 - 06 - 17 14:05:57,fdf;.....|
|cut_info|抠图参数|String|否|格式:孔类型,倍率,是否抠孔,中间用英文逗号隔开,其中最后一位<br>0:为不抠孔<br>1:为抠孔<br>例子:”25,4,1”,或”21,10,0”|
|tid|任务 id|String|是|通过 uuid3 生成的唯一字符串|