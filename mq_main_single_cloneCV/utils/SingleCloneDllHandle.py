# 单克隆识别 DLL 库使用示例
import ctypes
########################################################
# DLL库调用接口封装类
class ImageDectionHandle:
    # dll,所有对象共有
    _dll = None

    # 初始化
    def __init__(self, dll_path):
        if self._dll is None:
            self._dll = ctypes.cdll.LoadLibrary(dll_path)

    # 贴壁单克隆识别
    def doPredictAdh(
            self, input_image_path, image_save_path,
            data_save_path, fcs_save_path, axis_save_path, barcode,
            standard_day, cell_changed
    ):
        # 返回参数
        result = {
            "ret": 0,
            "msg": ""
        }
        p_id_str = ctypes.create_string_buffer(256, '\0')
        # 调用dll中doPredict函数,进行识别运算
        result["ret"] = self._dll.doPredictAdh(
            input_image_path.encode('GBK'),
            image_save_path.encode('GBK'),
            data_save_path.encode('GBK'),
            fcs_save_path.encode('GBK'),
            barcode.encode('GBK'),
            axis_save_path.encode('GBK'),
            ctypes.c_int(standard_day),
            ctypes.c_int(cell_changed),
            p_id_str
        )
        # 解析结果
        result["msg"] = p_id_str.value.decode()

        return result

    # 悬浮单克隆识别
    def doPredictSus(
            self, input_image_path, image_save_path,
            data_save_path, fcs_save_path, axis_save_path, barcode,
            standard_day, cell_changed, cell_min_count, cell_max_count
    ):
        # 返回参数
        result = {
            "ret": 0,
            "msg": {
                "cellArea": 0,
                "updateIds": ""
            }
        }
        p_id_str = ctypes.create_string_buffer(256, '\0')
        c_int_param = ctypes.c_int(10)
        # 调用dll中doPredict函数,进行识别运算
        result["ret"] = self._dll.doPredictSus(
            input_image_path.encode('GBK'),
            image_save_path.encode('GBK'),
            data_save_path.encode('GBK'),
            fcs_save_path.encode('GBK'),
            barcode.encode('GBK'),
            axis_save_path.encode('GBK'),
            ctypes.c_int(standard_day),
            ctypes.c_int(cell_changed),
            ctypes.c_int(cell_min_count),
            ctypes.c_int(cell_max_count),
            ctypes.byref(c_int_param),
            p_id_str
        )
        # 解析结果
        result["msg"]["updateIds"] = p_id_str.value.decode()
        result["msg"]["cellArea"] = c_int_param.value

        return result

    # 类器官3D杀伤双色
    def doPredictCellKilling(
            self, bf_path, fl1_path, fl2_path,
            dst_save_path,
            bf_save_path, fl1_save_path, fl2_save_path, merge1_save_path, merge2_save_path,
            data_save_path, fcs_save_path,
            min_width, fl2_thresh,
            param1=0, param2=0, param3=0, param4=0, param5=0
    ):
        
        # 返回参数
        result = {
            "ret": 0,
            "msg": ""
        }
        # 调用dll中doPredict函数,进行识别运算
        result["ret"] = self._dll.doPredictCellKilling3d(
            bf_path.encode('GBK'),
            fl1_path.encode('GBK'),
            fl2_path.encode('GBK'),
            dst_save_path.encode('GBK'),
            bf_save_path.encode('GBK'),
            fl1_save_path.encode('GBK'),
            fl2_save_path.encode('GBK'),
            merge1_save_path.encode('GBK'),
            merge2_save_path.encode('GBK'),
            data_save_path.encode('GBK'),
            fcs_save_path.encode('GBK'),
            ctypes.c_int(min_width),
            ctypes.c_int(fl2_thresh),
            ctypes.c_float(param1),
            ctypes.c_float(param2),
            ctypes.c_float(param3),
            ctypes.c_float(param4),
            ctypes.c_float(param5)
        )

        return result

    # 类器官3D杀伤反双色（FL1、FL2交换）
    def doPredictCellKilling2(
            self, bf_path, fl1_path, fl2_path,
            dst_save_path,
            bf_save_path, fl1_save_path, fl2_save_path, merge1_save_path, merge2_save_path,
            data_save_path, fcs_save_path,
            min_width, fl2_thresh,
            param1=0, param2=0, param3=0, param4=0, param5=0
    ):
        
        # 返回参数
        result = {
            "ret": 0,
            "msg": ""
        }
        # 调用dll中doPredict函数,进行识别运算
        result["ret"] = self._dll.doPredictCellKilling3d1(
            bf_path.encode('GBK'),
            fl1_path.encode('GBK'),
            fl2_path.encode('GBK'),
            dst_save_path.encode('GBK'),
            bf_save_path.encode('GBK'),
            fl1_save_path.encode('GBK'),
            fl2_save_path.encode('GBK'),
            merge1_save_path.encode('GBK'),
            merge2_save_path.encode('GBK'),
            data_save_path.encode('GBK'),
            fcs_save_path.encode('GBK'),
            ctypes.c_int(min_width),
            ctypes.c_int(fl2_thresh),
            ctypes.c_float(param1),
            ctypes.c_float(param2),
            ctypes.c_float(param3),
            ctypes.c_float(param4),
            ctypes.c_float(param5)
        )

        return result

    # 类器官3D杀伤三色
    def doPredictCellKilling3(
            self, bf_path, fl1_path, fl2_path, fl3_path,
            dst_save_path,
            bf_save_path, fl1_save_path, fl2_save_path, fl3_save_path, merge1_save_path, merge2_save_path,
            data_save_path, fcs_save_path,
            min_width, fl1_thresh, fl2_thresh, fl3_thresh,
            param1=0, param2=0, param3=0, param4=0, param5=0
    ):
        
        # 返回参数
        result = {
            "ret": 0,
            "msg": ""
        }
        # 调用dll中doPredict函数,进行识别运算
        result["ret"] = self._dll.doPredictCellKilling(
            bf_path.encode('GBK'),
            fl1_path.encode('GBK'),
            fl2_path.encode('GBK'),
            fl3_path.encode('GBK'),
            dst_save_path.encode('GBK'),
            bf_save_path.encode('GBK'),
            fl1_save_path.encode('GBK'),
            fl2_save_path.encode('GBK'),
            fl3_save_path.encode('GBK'),
            merge1_save_path.encode('GBK'),
            merge2_save_path.encode('GBK'),
            data_save_path.encode('GBK'),
            fcs_save_path.encode('GBK'),
            ctypes.c_int(min_width),
            ctypes.c_int(fl1_thresh),
            ctypes.c_int(fl2_thresh),
            ctypes.c_int(fl3_thresh),
            ctypes.c_float(param1),
            ctypes.c_float(param2),
            ctypes.c_float(param3),
            ctypes.c_float(param4),
            ctypes.c_float(param5)
        )

        return result

    # 版本号
    def getVersion(self):
        self._dll.version.restype = ctypes.c_char_p
        p_version = self._dll.version()
        return ctypes.c_char_p(p_version).value.decode()

    # 释放内存
    def release(self):
        self._dll.cleanup()
