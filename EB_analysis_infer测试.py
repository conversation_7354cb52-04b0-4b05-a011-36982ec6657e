import json
import math
import os.path as osp
import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
from copy import deepcopy
import tifffile
from imutils.paths import list_images
from tqdm import tqdm
import numpy as np
import pycuda.driver as cuda #cuda和tensorrt放在cupy之前,否则运行会出错
import tensorrt as trt
import cupyx.scipy.ndimage._interpolation as cusi
import cupy as cp
import cv2
from loguru import logger as log
import traceback
from utils.GPU_Predictor_dynamic import GPUPredictorUtil
from utils.crop_main_body import crop_hole, crop_muti_image
from sahi.slicing import slice_image
from lsnms import nms as large_nms
import time
from PIL import ImageFile
from PIL import Image
ImageFile.LOAD_TRUNCATED_IMAGES = True
Image.MAX_IMAGE_PIXELS = None
debug=False
def convert_from_cv2_to_image(img: np.ndarray) -> Image:
    return Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))


def convert_from_image_to_cv2(img: Image) -> np.ndarray:
    return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

class HostDeviceMem(object):
    def __init__(self, host_mem, device_mem):
        self.host = host_mem
        self.device = device_mem

    def __str__(self):
        return "Host:\n" + str(self.host) + "\nDevice:\n" + str(self.device)

    def __repr__(self):
        return self.__str__()


class TrtModel():
    def __init__(self, engine_path, gpu_id=0, max_batch_size=1):
        self.inputs = None
        self.outputs = None
        self.bindings = None
        self.stream = None
        print("TensorRT version: %s" % trt.__version__)
        cuda.init()
        self.cfx = cuda.Device(gpu_id).retain_primary_context()
        self.engine_path = engine_path
        self.trt_logger = trt.Logger(trt.Logger.ERROR)
        self.runtime = trt.Runtime(self.trt_logger)
        self.engine = self.load_engine(self.runtime, self.engine_path)
        self.context = self.engine.create_execution_context()
        self.max_batch_size = max_batch_size


    @staticmethod
    def load_engine(trt_runtime, engine_path):
        trt.init_libnvinfer_plugins(None, "")
        try:
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            engine = trt_runtime.deserialize_cuda_engine(engine_data)
            assert engine
        except Exception as e:
            log.error("load SAM model error")
            from utils.export_trt import EngineBuilder
            if os.path.exists(engine_path.replace(".engine", ".onnx")):
                log.info("find onnx model,try to load onnx model convert to engine")
                builder = EngineBuilder(verbose=False)
                builder.get_engine(engine_path.replace(".engine", ".onnx"),engine_path)
            else:
                log.error(f"{engine_path.replace('.engine', '.onnx')} file is not exists,not convert model to engine,pls check")
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            engine = trt_runtime.deserialize_cuda_engine(engine_data)
        return engine

    def allocate_buffers(self):
        self.cfx.push()
        inputs = []
        outputs = []
        bindings = []
        stream = cuda.Stream()
        for binding in self.engine:
            if binding in ["point_coords", "point_labels"]:
                size = abs(trt.volume(self.engine.get_tensor_shape(binding))) * self.max_batch_size
            else:
                size = abs(trt.volume(self.engine.get_tensor_shape(binding)))
            # print(f"binding: {binding}, size: {size}")
            dtype = trt.nptype(self.engine.get_tensor_dtype(binding))
            host_mem = cuda.pagelocked_empty(size, dtype)
            device_mem = cuda.mem_alloc(host_mem.nbytes)
            bindings.append(int(device_mem))
            if self.engine.binding_is_input(binding):
                inputs.append(HostDeviceMem(host_mem, device_mem))
            else:
                outputs.append(HostDeviceMem(host_mem, device_mem))
        return inputs, outputs, bindings, stream
    def sam_infer(self, inf_in_list, binding_shape_map=None):
        self.inputs, self.outputs, self.bindings, self.stream = self.allocate_buffers()
        if binding_shape_map:
            self.context.set_optimization_profile_async(0,self.stream.handle)
            for binding_name, shape in binding_shape_map.items():
                binding_idx = self.engine[binding_name]
                # print(f"binding_name: {binding_name}, binding_idx: {binding_idx}, shape: {shape}")
                self.context.set_binding_shape(binding_idx, shape)
        for i in range(len(self.inputs)):
            self.inputs[i].host = inf_in_list[i]
            cuda.memcpy_htod_async(self.inputs[i].device, self.inputs[i].host, self.stream)
        self.context.execute_async_v2(bindings=self.bindings, stream_handle=self.stream.handle)
        for i in range(len(self.outputs)):
            cuda.memcpy_dtoh_async(self.outputs[i].host, self.outputs[i].device, self.stream)
        self.stream.synchronize()
        self.cfx.pop()
        return [out.host.copy() for out in self.outputs]
class SegmentAnythingTRT:
    """Segmentation model using SegmentAnything"""
    def __init__(self, encoder_model_path, decoder_model_path) -> None:
        self.target_size = 1024
        self.input_size = (1024, 1024)
        self.batch_size=1
        # embedding init
        self.encoder_session = TrtModel(engine_path=encoder_model_path)
        # sam init
        self.decoder_session = TrtModel(engine_path=decoder_model_path,max_batch_size=20)
        log.info(f"{encoder_model_path} {self.encoder_session}")
        log.info(f"{decoder_model_path} {self.decoder_session}")
    def clip_segments(self, boxes, shape):
        # Clip segments (xy1,xy2,...) to image shape (height, width)
        boxes[:, 0] = boxes[:, 0].clip(0, shape[1])  # x
        boxes[:, 1] = boxes[:, 1].clip(0, shape[0])  # y
    def scale_segments(self, img1_shape, segments, img0_shape, shift_amount=None, ratio_pad=None):
        # Rescale coords (xyxy) from img1_shape to img0_shape
        if ratio_pad == 0:
            gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
            segments /= gain
            self.clip_segments(segments, img0_shape)
            if shift_amount is not None:
                segments[:, 0] += shift_amount[0]  # x padding
                segments[:, 1] += shift_amount[1]  # y padding
            return segments
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]
        segments[:, 0] -= pad[0]  # x padding
        segments[:, 1] -= pad[1]  # y padding
        segments /= gain
        self.clip_segments(segments, img0_shape)
        if shift_amount is not None:
            segments[:, 0] += shift_amount[0]  # x padding
            segments[:, 1] += shift_amount[1]  # y padding
        return segments

    def expand_box(self, box, image_shape, expand_pixels=10, box_expand=1):
        x1, y1, x2, y2 = box.astype(int)
        img_h, img_w, _ = image_shape
        # 计算扩大后的新box
        x1_new = max(0, x1 - expand_pixels)
        y1_new = max(0, y1 - expand_pixels)
        x2_new = min(img_w - 1, x2 + expand_pixels)
        y2_new = min(img_h - 1, y2 + expand_pixels)
        box_extended = [x1_new, y1_new, x2_new, y2_new]
        # 计算box_map,检查边界
        new_box_w = x2_new - x1_new
        new_box_h = y2_new - y1_new
        x1_map = max(x1 - x1_new - box_expand, 0)
        y1_map = max(y1 - y1_new - box_expand, 0)
        x2_map = min(x2 - x1_new + box_expand, new_box_w)
        y2_map = min(y2 - y1_new + box_expand, new_box_h)
        new_box_map = [x1_map, y1_map, x2_map, y2_map]
        return box_extended,new_box_map
    def transform_segments(self, masks,box,original_size,shift_amount=None):
        """Transform masks
        Transform the masks back to the original image size.
        """
        if len(masks.shape) == 4:
            origin_masks = np.transpose(masks[0],(1,2,0))
        else:
            masks = cusi.zoom(cp.array(masks), (4, 4), order=1, mode="nearest")
            masks = cp.asnumpy(masks)
            masks = masks[None]
            #print(masks.shape)
            origin_masks = np.transpose(masks,(1,2,0))
        # print(origin_masks.shape)
        origin_masks[origin_masks > 0.0] = 255
        origin_masks[origin_masks <= 0.0] = 0
        origin_masks=origin_masks.astype(np.uint8)
        cnt = cv2.findContours(origin_masks, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
        x1, y1, x2, y2 = box.astype(int)
        center_x = int((x1 + x2) / 2)
        center_y = int((y1 + y2) / 2)
        major_box_length = max((x2 - x1), (y2 - y1))
        minor_box_length = min((x2 - x1), (y2 - y1))
        end_a = 0
        if major_box_length == (y2 - y1):
            end_a = 90
        ell_con = cv2.ellipse2Poly((center_x, center_y),
                                   (int(major_box_length / 2), int(minor_box_length / 2)), end_a, 0, 360, 1)
        c = [ell_con]
        segment = np.array(c[np.array([len(x) for x in c]).argmax()]).reshape(-1, 2)
        if len(cnt):
            # 计算每个轮廓的面积
            areas = [cv2.contourArea(c) for c in cnt]
            # 找到面积最大的轮廓
            largest_contour = cnt[np.argmax(areas)]
            # 计算最大轮廓的面积
            largest_area = areas[np.argmax(areas)]
            # 检查最大轮廓的面积是否大于总面积的一半
            #print("len(cnt)",len(cnt),np.unique(origin_masks),largest_area)
            if largest_area < 10:
                return segment
            segment = [self.scale_segments(origin_masks.shape, np.array(x, dtype='float32'), original_size,shift_amount=shift_amount, ratio_pad=0) for num, x in enumerate(largest_contour)]
            segment = np.array(segment, dtype='int32')
            epsilon = (1e-100) * cv2.arcLength(segment, True)
            segment = cv2.approxPolyDP(segment, epsilon, True)
        return segment
    def get_input_points(self, prompt):
        """Get input points"""
        points = []
        labels = []
        # print("prompt",prompt)
        if len(prompt) == 2:
            # points.append([int(prompt[0]/self.input_size[0]),int(prompt[1]/self.input_size[1])])
            points.append([prompt[0], prompt[1]])
            labels.append(1)
        elif len(prompt) == 4:
            # points.append([int(prompt[0]/self.input_size[0]),int(prompt[1]/self.input_size[1])])  # top left
            # points.append([int(prompt[2]/self.input_size[0]),int(prompt[3]/self.input_size[1])])  # bottom right
            points.append([prompt[0], prompt[1]])  # top left
            points.append([prompt[2], prompt[3]])  # bottom right
            labels.append(2)
            labels.append(3)
            # points.append([int((prompt[0]+prompt[2])/2),int((prompt[1]+prompt[3])/2)])
            # labels.append(2)
        else:
            pass
            # print("prompt fail", prompt)
        points, labels = np.array(points), np.array(labels)
        # print(points,labels)
        return points, labels

    def run_encoder(self, encoder_inputs):
        """Run encoder"""
        encoder_inputs = encoder_inputs.transpose(2, 0, 1)
        encoder_inputs = np.ascontiguousarray(encoder_inputs)[None, :, :, :]
        pixel_mean = [123.675, 116.28, 103.53],
        pixel_std = [58.395, 57.12, 57.375]
        pixel_mean = np.array(pixel_mean).reshape(-1, 1, 1)
        pixel_std = np.array(pixel_std).reshape(-1, 1, 1)
        encoder_inputs = ((encoder_inputs - pixel_mean) / pixel_std).astype(np.float32)
        output = self.encoder_session.sam_infer(encoder_inputs)
        image_embedding = output[0]
        return image_embedding

    @staticmethod
    def get_preprocess_shape(oldh: int, oldw: int, long_side_length: int):
        """
        Compute the output size given input size and target long side length.
        """
        scale = long_side_length * 1.0 / max(oldh, oldw)
        newh, neww = oldh * scale, oldw * scale
        neww = int(neww + 0.5)
        newh = int(newh + 0.5)
        return (newh, neww)

    def apply_coords(self, coords: np.ndarray, original_size, target_length):
        """
        Expects a numpy array of length 2 in the final dimension. Requires the
        original image size in (H, W) format.
        """
        old_h, old_w = original_size
        new_h, new_w = self.get_preprocess_shape(
            original_size[0], original_size[1], target_length
        )
        coords = deepcopy(coords).astype(float)
        coords[..., 0] = coords[..., 0] * (new_w / old_w)
        coords[..., 1] = coords[..., 1] * (new_h / old_h)
        return coords

    def run_decoder(
            self, image_embedding, original_size, transform_matrix, prompt
    ):
        """Run decoder"""
        input_points, input_labels = self.get_input_points(prompt)

        # Add a batch index, concatenate a padding point, and transform.
        onnx_coord = np.concatenate(
            [input_points, np.array([[0.0, 0.0]])], axis=0
        )[None, :, :]
        onnx_label = np.concatenate([input_labels, np.array([-1])], axis=0)[
                     None, :
                     ].astype(np.float32)
        onnx_coord = self.apply_coords(
            onnx_coord, self.input_size, self.target_size
        ).astype(np.float32)

        # Apply the transformation matrix to the coordinates.
        onnx_coord = np.concatenate(
            [
                onnx_coord,
                np.ones((1, onnx_coord.shape[1], 1), dtype=np.float32),
            ],
            axis=2,
        )
        onnx_coord = np.matmul(onnx_coord, transform_matrix.T)
        onnx_coord = onnx_coord[:, :, :2].astype(np.float32)

        # Create an empty mask input and an indicator for no mask.
        onnx_mask_input = np.zeros((1, 1, 256, 256), dtype=np.float32)
        onnx_has_mask_input = np.zeros(1, dtype=np.float32)

        decoder_inputs = {
            "image_embeddings": image_embedding,
            "point_coords": onnx_coord,
            "point_labels": onnx_label,
            "mask_input": onnx_mask_input,
            "has_mask_input": onnx_has_mask_input,
            "orig_im_size": np.array(self.input_size, dtype=np.float32),
        }
        masks, scores, _= self.decoder_session.sam_infer(list(decoder_inputs.values()), binding_shape_map={key:value.shape for key,value in decoder_inputs.items()})
        masks=masks.reshape(self.batch_size, -1).reshape(4, 256, 256)
        scores =scores.reshape(self.batch_size, -1).squeeze(0)
        output = scores.argmax()
        masks = masks[output]
        #print(masks.shape)
        return masks

    def encode(self, cv_image):
        """
        Calculate embedding and metadata for a single image.
        """
        original_size = cv_image.shape[:2]

        # Calculate a transformation matrix to convert to self.input_size
        scale_x = self.input_size[1] / cv_image.shape[1]
        scale_y = self.input_size[0] / cv_image.shape[0]
        scale = min(scale_x, scale_y)
        transform_matrix = np.array(
            [
                [scale, 0, 0],
                [0, scale, 0],
                [0, 0, 1],
            ]
        )
        cv_image = cv2.warpAffine(
            cv_image,
            transform_matrix[:2],
            (self.input_size[1], self.input_size[0]),
            flags=cv2.INTER_LINEAR,
        )

        encoder_inputs = cv_image.astype(np.float32)
        image_embedding = self.run_encoder(encoder_inputs).reshape(1, 256, 64, 64)
        return {
            "image_embedding": image_embedding,
            "original_size": original_size,
            "transform_matrix": transform_matrix,
        }

    def predict_masks(self, embedding, prompt):
        """
        Predict masks for a single image.
        """
        box_masks = self.run_decoder(
            embedding["image_embedding"],
            embedding["original_size"],
            embedding["transform_matrix"],
            prompt,
        )
        x1_map, y1_map, x2_map, y2_map=prompt
        point_prompt=[(x1_map+x2_map)/2,(y1_map+y2_map)/2]
        points_mask=self.run_decoder(
            embedding["image_embedding"],
            embedding["original_size"],
            embedding["transform_matrix"],
            point_prompt,
        )
        # 同为正的情况
        condition_both_positive = (box_masks > 0) & (points_mask > 0)
        # 同为负的情况
        condition_both_negative = (box_masks < 0) & (points_mask < 0)
        # 一正一负的情况
        condition_one_pos_one_neg = (box_masks * points_mask < 0)

        # 构建最终结果
        mask = np.where(condition_both_positive, np.maximum(box_masks, points_mask),
                              np.where(condition_both_negative, np.minimum(box_masks, points_mask),
                                       np.where(condition_one_pos_one_neg,
                                                np.where(np.abs(box_masks*1.3) > np.abs(points_mask*0.7), box_masks,
                                                         points_mask),
                                                box_masks)))
        return mask
class EBanyalysisInfer(GPUPredictorUtil):
    def __init__(self,seg_engine,sam_decode_engine=None,sam_encode_engine=None):
        super().__init__()
        self.image0 = None
        self.input_data = None
        self.image_embedding = None
        self.jyh_img = None
        self.img0 = None
        self.crop_hole_mask = None
        self.crop_hole_image = None
        self.crop_bbox = None
        self.jiaodi_img = None
        self.jyh_jiaodi_img = None
        self.masked_image = None
        self.max_shape = 2560
        self.conf_thres = 0.45
        self.iou_thres = 0.25
        self.slice_pred_thed_max = None
        self.process_type = None
        self.image_embedding = None
        cuda.init()
        self.device = cuda.Device(0)
        self.ctx = self.device.retain_primary_context()
        self.trt_logger = trt.Logger(trt.Logger.ERROR)
        self.batch_size=1
        self.pre_process_type=None
        self.model_file=dict()
        self.model_file["seg"]=seg_engine
        self.SAMmodel = None
        try:
            if isinstance(sam_encode_engine,str) and isinstance(sam_decode_engine,str):
                self.SAMmodel = SegmentAnythingTRT(encoder_model_path=sam_encode_engine,
                                                   decoder_model_path=sam_decode_engine)
                log.info(f"SAMmodel {self.SAMmodel}")
            else:
                log.error(f"sam_encode_engine {sam_encode_engine} or sam_decode_engine {sam_decode_engine} Path error")
        except Exception as e:
            log.error(f"load sam model error {e}")
    def init_engine(self,model_file_path):
        del self.engine
        del self.context
        self.engine = None
        self.context = None
        if not osp.exists(model_file_path):
            log.error("model engine file is not exists in {}".format(model_file_path))
        else:
            log.info("loading model from {}".format(model_file_path))
        try:
            with open(model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                engine = runtime.deserialize_cuda_engine(f.read())
            assert engine
            log.info(f"context shape {engine.create_execution_context().get_binding_shape(0)}")
        except:
            log.error("load model error")
            from utils.export_trt import EngineBuilder
            if os.path.exists(model_file_path.replace(".engine", ".onnx")):
                log.info("find onnx model,try to load onnx model convert to engine")
                builder = EngineBuilder(verbose=False)
                builder.get_engine(model_file_path.replace(".engine", ".onnx"),model_file_path)
            else:
                log.error(f"{model_file_path.replace('.engine', '.onnx')} file is not exists,not convert model to engine,pls check")
            with open(model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                engine = runtime.deserialize_cuda_engine(f.read())
        try:
            assert engine
            context = engine.create_execution_context()
            assert context
        except Exception as e:
            with open(model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                engine = runtime.deserialize_cuda_engine(f.read())
            context = engine.create_execution_context()
        return engine,context

    def allocate_buffers(self, input_shape=None):
        self.inputs = []
        self.outputs = []
        self.allocations = []
        self.ctx.push()
        for i in range(self.engine.num_bindings):
            name = self.engine.get_binding_name(i)
            dtype = self.engine.get_binding_dtype(i)
            if self.engine.binding_is_input(i):
                if -1 in tuple(self.engine.get_binding_shape(i)):  # dynamic
                    self.context.set_binding_shape(i, tuple(input_shape))
            shape = tuple(self.context.get_binding_shape(i))
            size = np.dtype(trt.nptype(dtype)).itemsize
            for s in shape:
                size *= s
            allocation = cuda.mem_alloc(size)
            binding = {
                'index': i,
                'name': name,
                'dtype': np.dtype(trt.nptype(dtype)),
                'shape': list(shape),
                'allocation': allocation,
            }
            self.allocations.append(allocation)
            if self.engine.binding_is_input(i):
                self.inputs.append(binding)
            else:
                self.outputs.append(binding)
        self.ctx.pop()
        assert len(self.inputs) > 0
        assert len(self.outputs) > 0
        assert len(self.allocations) > 0

    def tesnsort_infer(self, input_image,model_type):
        print("pre_process_type",self.pre_process_type,"current_process_type",self.process_type)
        if self.engine is not None:
            if self.pre_process_type!=self.process_type:
                self.engine,self.context=self.init_engine(self.model_file[model_type])
        else:
            self.engine, self.context = self.init_engine(self.model_file[model_type])
        try:
            #print(input_image.shape)
            self.allocate_buffers(input_image.shape)
            cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(input_image))
        except Exception as e:
            log.error(f"reboot engine {self.model_file_path}",exc_info=True)
            self.release()
            self.__init__(self.model_file_path)
            self.allocate_buffers(input_image.shape)
            cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(input_image))
        self.context.execute_v2(self.allocations)
        if model_type in ["class","detect"]:
            output = np.zeros(self.outputs[0]['shape'], dtype=self.outputs[0]['dtype'])
            cuda.memcpy_dtoh(output, self.outputs[0]['allocation'])
        else:
            pred_det = np.zeros(self.outputs[1]['shape'], dtype=self.outputs[1]['dtype'])
            cuda.memcpy_dtoh(pred_det, self.outputs[1]['allocation'])
            proto = np.zeros(self.outputs[0]['shape'], dtype=self.outputs[0]['dtype'])
            cuda.memcpy_dtoh(proto, self.outputs[0]['allocation'])
            output = [pred_det, proto]
        self.pre_process_type=model_type
        return output

    def letterbox(self,im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True, stride=32):
        # Resize and pad image while meeting stride-multiple constraints
        shape = im.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)
        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:  # only scale down, do not scale up (for better val mAP)
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2
        if shape[::-1] != new_unpad:  # resize
            im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
        return im, ratio, (dw, dh)
    def empty_like(self, x):
        """Creates empty torch.Tensor or np.ndarray with same shape as input and float32 dtype."""
        return np.empty_like(x, dtype=np.float32) # numpy only
    def xywh2xyxy(self, x):
        """
        Convert bounding box coordinates from (x, y, width, height) format to (x1, y1, x2, y2) format where (x1, y1) is the
        top-left corner and (x2, y2) is the bottom-right corner. Note: ops per 2 channels faster than per channel.

        Args:
            x (np.ndarray): The input bounding box coordinates in (x, y, width, height) format.

        Returns:
            y (np.ndarray): The bounding box coordinates in (x1, y1, x2, y2) format.
        """
        y = self.empty_like(x)  # faster than clone/copy
        xy = x[..., :2]  # centers
        wh = x[..., 2:] / 2  # half width-height
        y[..., :2] = xy - wh  # top left xy
        y[..., 2:] = xy + wh  # bottom right xy
        return y
    def box_area(self, box):
        # box = xyxy(4,n)
        return (box[2] - box[0]) * (box[3] - box[1])
    def box_iou(self, box1, box2, eps=1e-7):
        """
        Return intersection-over-union (Jaccard index) of boxes.
        Both sets of boxes are expected to be in (x1, y1, x2, y2) format.
        Arguments:
            box1 (ndarray[N, 4])
            box2 (ndarray[M, 4])
        Returns:
            iou (ndarray[N, M]): the NxM matrix containing the pairwise
                IoU values for every element in boxes1 and boxes2
                :param box1:
                :param box2:
                :param eps:
        """

        # inter(N,M) = (rb(N,M,2) - lt(N,M,2)).clamp(0).prod(2)
        (a1, a2), (b1, b2) = np.split(box1[:, None], 2,
                                      axis=2), np.split(box2, 2, axis=1)
        array = np.minimum(a2, b2) - np.maximum(a1, b1)
        inter = array.clip(0)
        inter = inter.prod(2)

        # IoU = inter / (area1 + area2 - inter)
        return inter / (self.box_area(box1.T)[:, None] +
                        self.box_area(box2.T) - inter + eps)
    def crop_mask(self, masks, boxes):
        """
        "Crop" predicted masks by zeroing out everything not in the predicted bbox.
        Vectorized by Chong (thanks Chong).
        Args:
            - masks should be a size [h, w, n] ndarray of masks
            - boxes should be a size [n, 4] ndarray of bbox coords in relative point form
        """

        n, h, w = masks.shape
        x1, y1, x2, y2 = np.split(boxes[:, :, None], 4, axis=1)  # x1 shape(n,1,1)
        r = np.arange(w, dtype=x1.dtype)[None, None, :]  # rows shape(1,w,1)
        c = np.arange(h, dtype=x1.dtype)[None, :, None]  # cols shape(h,1,1)
        return masks * ((r >= x1) * (r < x2) * (c >= y1) * (c < y2))
    def clip_boxes(self, boxes, shape):
        # Clip boxes (xyxy) to image shape (height, width)
        if isinstance(boxes, np.ndarray):  # faster individually
            # np.array (faster grouped)
            boxes[:, [0, 2]] = boxes[:, [0, 2]].clip(0, shape[1])  # x1, x2
            boxes[:, [1, 3]] = boxes[:, [1, 3]].clip(0, shape[0])  # y1, y2
        else:
            print("type wont be supported")
    def scale_boxes(self, img1_shape, boxes, img0_shape, ratio_pad=None):
        # Rescale boxes (xyxy) from img1_shape to img0_shape
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0],
                       img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (
                    img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]

        boxes[:, [0, 2]] -= pad[0]  # x padding
        boxes[:, [1, 3]] -= pad[1]  # y padding
        boxes[:, :4] /= gain
        self.clip_boxes(boxes, img0_shape)
        return boxes
    def preprocess(self, img_src_bgr):
        """_summary_
        Args:
            img_src_bgr (numpy array uint8): bgr

        """
        self.image0 = img_src_bgr
        # shape = max(640, max(self.image0.shape) - max(self.image0.shape) % 32)
        shape = max(640, max(self.image0.shape))
        if shape % 32 != 0:
            shape = int(shape / 32) * 32
        shape = min(shape, self.max_shape)
        letterbox_img = self.letterbox(self.image0, shape, stride=64, auto=False)[0]  # padded resize
        img = letterbox_img.transpose(2, 0, 1)[::-1]  # hwc->chw,bgr->rgb
        self.input_data = np.ascontiguousarray(img)
        self.input_data = self.input_data.astype('float32')
        self.input_data /= 255
        if len(self.input_data.shape) == 3:
            self.input_data = self.input_data[None]  # expand for batch dim
        return self.input_data
    def masks2segments(self, masks, strategy='largest'):
        segments = []
        for x in masks:
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            # # mask = cv2.dilate(x.astype("uint8"), kernel,2)MORPH_OPEN MORPH_OPEN
            # # mask = cv2.morphologyEx(x.astype("uint8"), cv2.MORPH_CLOSE, kernel)
            x = cv2.morphologyEx(x.astype("uint8"), cv2.MORPH_OPEN, kernel)
            # c = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
            mask = np.squeeze(x)
            mask = mask.astype(np.uint8)
            mask = cv2.copyMakeBorder(mask, 1, 1, 1, 1, cv2.BORDER_CONSTANT, value=0)
            c = cv2.findContours(mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE, offset=(-1, -1))[0]
            #c=[cv2.approxPolyDP(x, (1e-100) * cv2.arcLength(x, True), True) for x in c] #直线变曲线
            # c = cv2.findContours(x.astype("uint8"), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
            if c:
                if strategy == 'concat':  # concatenate all segments
                    c = np.concatenate([x.reshape(-1, 2) for x in c])
                elif strategy == 'largest':  # select largest segment
                    c = np.array(c[np.array([len(x) for x in c]).argmax()]).reshape(-1, 2)
            else:
                c = np.zeros((0, 2))  # no segments found
            segments.append(c.astype('float32'))
        return segments

    def clip_segments(self, boxes, shape):
        # Clip segments (xy1,xy2,...) to image shape (height, width)
        boxes[:, 0] = boxes[:, 0].clip(0, shape[1])  # x
        boxes[:, 1] = boxes[:, 1].clip(0, shape[0])  # y

    def scale_segments(self, img1_shape, segments, img0_shape, shift_amount=None, ratio_pad=None):
        # Rescale coords (xyxy) from img1_shape to img0_shape
        if ratio_pad == 0:
            gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
            segments /= gain
            self.clip_segments(segments, img0_shape)
            if shift_amount is not None:
                segments[:, 0] += shift_amount[0]  # x padding
                segments[:, 1] += shift_amount[1]  # y padding
            return segments
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]
        segments[:, 0] -= pad[0]  # x padding
        segments[:, 1] -= pad[1]  # y padding
        segments /= gain
        self.clip_segments(segments, img0_shape)
        if shift_amount is not None:
            segments[:, 0] += shift_amount[0]  # x padding
            segments[:, 1] += shift_amount[1]  # y padding
        return segments
    def mask_crop_processing(self,img0,object_prediction_list,conf_thres=0.1,iou_thres=0.1):
        object_prediction_list = [object_prediction for object_prediction in object_prediction_list if object_prediction]
        if not object_prediction_list:
            return img0
        det = np.concatenate([x[0] for x in object_prediction_list], axis=0)
        old_det = np.concatenate([x[2] for x in object_prediction_list], axis=0)
        keep = large_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
        # print('keep:', keep)  # [...]
        proto_info = []
        temp = [proto_info.extend(x[1]) for x in object_prediction_list]
        end_segments=[]
        # new_input_shape=(new_h,new_w)
        self.image_embedding = self.SAMmodel.encode(self.jyh_img)
        for keep_number in tqdm(keep):
            # print(f'+++++++++++++++++++++++++++/{list(keep).index(keep_number)}/+++++++++++++++++++++++++++')
            box = det[keep_number, :4]
            extend_pix = int(min(box[2] - box[0], box[3] - box[1]) * 2)
            if extend_pix < 150:
                extend_pix = 150
            box_extend, new_box_map = self.SAMmodel.expand_box(box, self.jyh_img.shape, expand_pixels=extend_pix)
            origin_masks = self.SAMmodel.predict_masks(self.image_embedding, box_extend)
            c = self.SAMmodel.transform_segments(origin_masks, box, self.image_embedding["original_size"])
            end_segments.append(c.astype('float32'))
        if len(end_segments) == 0:
            return img0
        img_copy=img0.copy()
        print("len(end_segments)",len(end_segments))
        for cell_num in range(len(end_segments)):
            if len(np.unique(end_segments[cell_num])) < 3:
                continue
            img_copy = cv2.drawContours(img_copy, np.array([end_segments[cell_num]]).astype(int), -1,(255,255,255), -1)
        #cv2.imwrite("test.jpg",img_copy)
        return img_copy
    def computer_black(self,roi):
        if len(roi.shape) > 2:
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            mask_flag = False
        else:
            gray = roi
            mask_flag = True
        height, width = roi.shape[:2]
        center_x = width // 2
        center_y = height // 2
        if cv2.countNonZero(gray) != 0:
            if gray[int(center_y), int(center_x)] < 1:
                return True
            else:
                gray_sum = gray.sum()
                if mask_flag:
                    if gray_sum < cv2.countNonZero(gray):
                        return True
                else:
                    if gray_sum < 10:
                        return True
                return False
        else:
            return True
    def find_cricel_mask(self, mask, conuter):
        mask = mask.astype(np.uint8)
        _, _, bw, bh = cv2.boundingRect(conuter)
        kennel_size = int(min(bw, bh) / 5) if (min(bw, bh) / 5) > 3 else 3  # 滤波器大小
        kennel_size |= 1
        kennel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kennel_size, kennel_size))
        kennel3 = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        eroded_mask = cv2.erode(mask, kennel)
        dilated_mask = cv2.dilate(mask, kennel3)
        cricle_mask = dilated_mask - eroded_mask
        return cricle_mask
    def sigmoid(self, x):
        s = 1 / (1 + np.exp(-x))
        return s
    def process_mask(self, protos, masks_in, bboxes, shape, upsample=False):
        """
        上采样之前先进行crop裁剪,再上采样（插值法）
        proto_out(ie. protos): [mask_dim, mask_h, mask_w],[32,160,160]
        out_masks(ie. masks_in): [n, mask_dim], n is number of masks after nms,[7,32]
        bboxes: [n, 4], n is number of masks after nms
        shape:input_image_size, (h, w)
        return: h, w, n
        """

        c, mh, mw = protos.shape  # CHW
        ih, iw = shape
        # @就是matmul的另一种写法,torch和numpy都有matmul,
        # masks_in:(3,32)、protos:(32,160,160)
        # 想要的ttt的shape为:[32, 3, 160]
        # ttt = np.matmul(masks_in, protos) #错误
        # 改变维度为c行、1列
        masks = self.sigmoid((masks_in @ protos.astype(np.float32).reshape(c, -1))).reshape(-1, mh, mw)  # CHW
        downsampled_bboxes = bboxes.copy()
        downsampled_bboxes[:, 0] *= mw / iw
        downsampled_bboxes[:, 2] *= mw / iw
        downsampled_bboxes[:, 3] *= mh / ih
        downsampled_bboxes[:, 1] *= mh / ih
        masks = self.crop_mask(masks, downsampled_bboxes)  # CHW
        # tt = masks.transpose(2, 1, 0)  # CHW->HWC,便于opencv的resize操作（仅可用于hwc）
        if upsample:
            # masks = ndimage.zoom(masks[None], (1, 1, 4, 4),
            #                      order=1,
            #                      mode="nearest")[0]
            # masks = masks.transpose(2, 0, 1)  #HWC->CHW #慢的地方
            masks = cusi.zoom(cp.array(masks[None]), (1, 1, 4, 4), order=1, mode="nearest")[0]
            masks = cp.asnumpy(masks)
        tttt = masks.__gt__(0.5).astype(np.float32)
        return tttt  # 大于0.5的
    def non_max_suppression(self,
                            prediction,
                            conf_thres=0.25,
                            iou_thres=0.45,
                            max_det=1000,
                            nm=32):

        bs = prediction.shape[0]  # batch size
        nc = prediction.shape[2] - nm - 5  # number of classes
        xc = prediction[..., 4] > conf_thres  # candidates
        #print("bs",bs, "nc", nc, "xc", xc.shape)
        if nc>1000:
            assert nc == 1000, f"nc={nc} 不是v5模型"
        # Settings
        # min_wh = 2  # (pixels) minimum box width and height
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into cv2.dnn.NMSBoxes
        time_limit = 0.5 + 0.05 * bs  # seconds to quit after
        redundant = True  # require redundant detections
        merge = False  # use merge-NMS,False

        t = time.time()
        mi = 5 + nc  # mask start index,117中,前面是85（80类cls score, 4box, 1个obj score）,后面是32(mask coeffient)

        # numpy array不支持空数组的调用
        # https://blog.csdn.net/weixin_31866177/article/details/107380707
        # https://bobbyhadz.com/blog/python-indexerror-index-0-is-out-of-bounds-for-axis-0-with-size-0
        # https://blog.csdn.net/weixin_38753213/article/details/106754787
        # 不能对数组的元素赋值,比如 a=np.empty((0,6)),a[0]=1,这样会出错
        # output = np.zeros((0, 6 + nm), np.float32) * bs
        # 因此我们使用列表-》append-》转为numpy array
        output = []
        output_final = []
        for xi, x in enumerate(prediction):  # image index, image inference

            # confidence, xc的shape:(1, 25200), xi = 0, x的shape:(25200, 117)
            # 下面这句话就是筛选出obj score > conf_thres的instances
            # 经过筛选后的x的shape为:(44, 117)
            x = x[xc[xi]]

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Compute conf
            x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf

            # Box/Mask
            # center_x, center_y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(x[:, :4])  # shape[44, 4]

            # 计算宽高比并过滤
            w = box[:, 2] - box[:, 0]  # 宽度
            h = box[:, 3] - box[:, 1]  # 高度
            max_side = np.maximum(w, h)  # 最大边
            min_side = np.minimum(w, h)  # 最小边
            aspect_ratio = max_side / min_side
            valid_indices = (aspect_ratio <= 2.1)
            x = x[valid_indices]
            box = box[valid_indices]

            # zero columns if no masks,从第index=85(第86个数)开始至117
            mask = x[:, mi:]  # mask shape[44, 32]

            # Detections matrix nx6 (xyxy, conf, cls)

            # best class only
            # x[:, 5:mi]是去除了4box + 1obj score的,就是cls score的从5到85
            # 下面这个max的第一个参数1,表示axis=1,就是按照列进行筛选cls中的最大值,且返回索引.
            # keepdim 表示是否需要保持输出的维度与输入一样,keepdim=True表示输出和输入的维度一样,
            # keepdim=False表示输出的维度被压缩了,也就是输出会比输入低一个维度.
            # j:Get the class with the highest confidence
            conf, j = x[:, 5:mi].max(axis=1), x[:, 5:mi].argmax(axis=1)
            conf, j = conf.reshape(-1, 1), j.reshape(-1, 1)

            # x的shape从[44, 38]经过conf.reshape(-1) > conf_thres筛选后变为
            # [43, 38],且:38 = 4box + 1conf + 1类别id + 32coeffients
            x = np.concatenate((box, conf, j.astype(float), mask),
                               axis=1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            elif n > max_nms:  # excess boxes
                x = x[(-x[:, 4]).argsort()[:max_nms]]  # sort by confidence
            else:
                x = x[(-x[:, 4]).argsort()]  # sort by confidence

            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            # boxes (offset by class), scores
            boxes, scores = x[:, :4] + c, x[:, 4]
            # i = cv2.dnn.NMSBoxes(boxes.tolist(), scores.tolist(),
            #                      self.conf_thres, self.iou_thres)  # NMS
            i = large_nms(boxes, scores,score_threshold=conf_thres,iou_threshold=iou_thres)
            # if i.shape[0] > max_det:  # limit detections
            #     i = i[:max_det]
            if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)
                x[i, :4] = np.dot(weights, x[:, :4]).astype(np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy

            # output[xi] = x[i]
            output.append(x[i])
            # if (time.time() - t) > time_limit:
            #     print(f'WARNING ⚠️ NMS time limit {time_limit:.3f}s exceeded')
            #     break  # time limit exceeded

        output = np.array(output).reshape(-1, 6 + nm)
        output_final.append(output)

        return output_final
    def non_max_suppression_v8(self,
                            prediction,
                            conf_thres=0.25,
                            iou_thres=0.45,
                            max_det=1000,
                            nm=32):
        assert 0 <= conf_thres <= 1, f'Invalid Confidence threshold {conf_thres}, valid values are between 0.0 and 1.0'
        assert 0 <= iou_thres <= 1, f'Invalid IoU {iou_thres}, valid values are between 0.0 and 1.0'
        if isinstance(prediction,(list, tuple)):  # YOLOv8 model in validation model, output = (inference_out, loss_out)
            prediction = prediction[0]  # select only inference output
        bs = prediction.shape[0]  # batch size
        nc = prediction.shape[1] - nm - 4  # number of classes
        mi = 4 + nc  # mask start index
        # xc = prediction[:, 4:mi].max(1) > conf_thres  # candidates
        xc = prediction[:, 4:mi].max(1) > conf_thres
        # Settings
        # min_wh = 2  # (pixels) minimum box width and height
        time_limit = 0.5 + 0.05 * bs  # seconds to quit after
        redundant = True  # require redundant detections
        merge = False  # use merge-NMS
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into torchvision.ops.nms()

        t = time.time()
        # output = [torch.zeros((0, 6), device=prediction.device)] * bs
        output = [np.zeros((0, 6 + nm))] * bs
        for xi, x in enumerate(prediction):  # image index, image inference
            # Apply constraints
            # x[((x[..., 2:4] < min_wh) | (x[..., 2:4] > max_wh)).any(1), 4] = 0  # width-height
            x = np.transpose(x)[xc[xi]]  # confidence

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Detections matrix nx6 (xyxy, conf, cls)
            box, cls, mask = x[:, :4], x[:, 4:nc + 4], x[:, nc + 4:]

            # Box (center x, center y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(box)

            # Detections matrix nx6 (xyxy, conf, cls)
            conf = cls.max(1, keepdims=True)
            j_argmax = cls.argmax(1)
            j = j_argmax if j_argmax.shape == x[:, 5:].shape else \
                np.expand_dims(j_argmax, 1)  # for argmax(axis, keepdims=True)
            x = np.concatenate((box, conf, j.astype(np.float32), mask), 1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            # elif n > max_nms:  # excess boxes
            #     x_argsort = np.argsort(x[:, 4])[:max_nms] # sort by confidence
            #     x = x[x_argsort]
            x_argsort = np.argsort(x[:, 4])[::-1][:max_nms]  # sort by confidence
            x = x[x_argsort]
            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores

            #############################################
            # i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
            i = large_nms(boxes, scores, iou_thres)
            ############################################

            # if i.shape[0] > max_det:  # limit detections
            #     i = i[:max_det]
            if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)
                x[i, :4] = np.dot(weights, x[:, :4]).astype(np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy
            output[xi] = x[i]
        return output
    def get_prediction(self,image,
                       input_size: int = None,
                       shift_amount=None,
                       conf_thres: float = 0.5,  # confidence threshold
                       iou_thres: float = 0.45,  # NMS IOU threshold
                       model_type="class"
                       ):
        if shift_amount is None:
            shift_amount=[0,0]
        if model_type == "class":
            net_input_data = self.preprocess(image)
            detections = self.tesnsort_infer(net_input_data,model_type)
            exp_sums = np.sum(np.exp(detections), axis=-1, keepdims=True)
            softmax_outputs = np.exp(detections) / exp_sums
            output = np.argsort(-softmax_outputs[0])[0]
            print(softmax_outputs)
            print(output)
            return output
        elif model_type=="detect":
            net_input_data = self.preprocess(image)
            pred_det = self.tesnsort_infer(net_input_data,model_type)
            #print("pred_det.shape",pred_det.shape)
            output=[]
            try:
                pred = self.non_max_suppression(pred_det,
                                                conf_thres,
                                                iou_thres,nm=0)
            except Exception as e:
                print("切换成v8解析", e)
                pred = self.non_max_suppression_v8(pred_det,
                                                   conf_thres,
                                                   iou_thres,nm=0)
            # print("单张的nms处理完成")
            for i, det in enumerate(pred):
                if len(det):
                    det[:, :4] = self.scale_boxes(self.input_data.shape[2:], det[:, :4], self.image0.shape).round()
                    det[:, 0] = det[:, 0] + shift_amount[0]
                    det[:, 1] = det[:, 1] + shift_amount[1]
                    det[:, 2] = det[:, 2] + shift_amount[0]
                    det[:, 3] = det[:, 3] + shift_amount[1]
                    output.append(det)
            return output
        else:
            net_input_data = self.preprocess(image)
            pred_det, proto = self.tesnsort_infer(net_input_data,model_type)
            print("pred_det.shape", pred_det.shape, "proto.shape", proto.shape)
            object_prediction = []
            try:
                pred = self.non_max_suppression(pred_det,
                                                conf_thres,
                                                iou_thres)
            except Exception as e:
                print("切换成v8解析", e)
                pred = self.non_max_suppression_v8(pred_det,
                                                   conf_thres,
                                                   iou_thres)
            # print("单张的nms处理完成")
            for i, det in enumerate(pred):
                if len(det):
                    # print('nms_det, nms_mask:', det.shape[0], proto[i].shape)
                    # Rescale boxes from img_size to im0 size
                    # 就是将当前在letterbox等处理后的图像上检测的box结果,映射返回原图大小上
                    old_det = det.copy()
                    proto_mask = [proto[i], self.input_data.shape[2:], self.image0.shape, shift_amount]
                    det[:, :4] = self.scale_boxes(self.input_data.shape[2:], det[:, :4], self.image0.shape).round()
                    det[:, 0] = det[:, 0] + shift_amount[0]
                    det[:, 1] = det[:, 1] + shift_amount[1]
                    det[:, 2] = det[:, 2] + shift_amount[0]
                    det[:, 3] = det[:, 3] + shift_amount[1]
                    # if full_pred:  # 切片时 整张图的conf
                    #     det[:, 4] = (det[:, 4] + 0.3).clip(0, 1)
                    # print(det.shape,len(protos_mask),protos_mask[0])
                    object_prediction.append([det, [proto_mask for _ in range(det.shape[0])], old_det])
            return object_prediction
    def read_image(self,input_src,jiaodi_corp=False,slice_pred_thed_max=4218):
        if isinstance(input_src,str):
            self.jyh_img = cv2.imdecode(np.fromfile(input_src, dtype=np.uint8), cv2.IMREAD_COLOR)
            try:
                if "_cut.jpg" in input_src:
                    self.img0 = cv2.imdecode(np.fromfile(input_src[:-8] + "_merge_src.jpg", dtype=np.uint8),cv2.IMREAD_COLOR)
                else:
                    self.img0 = cv2.imdecode(np.fromfile(input_src[:-4] + "_src.jpg", dtype=np.uint8), cv2.IMREAD_COLOR)
            except Exception as e:
                self.img0=self.jyh_img
                log.error(f"不存在均一化图,使用原图进行分析 {e}")
            print("img0.shape", self.img0.shape)
            log.info(f"img0.shape {self.img0.shape}")
            try:
                CutoutConfig = f"{os.sep.join(os.path.dirname(input_src).split(os.sep)[:-1])}\CutoutConfig.txt"
                if os.path.exists(
                        CutoutConfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp', 'imagesource')):
                    CutoutConfig = CutoutConfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp',
                                                                                                'imagesource')
                log.info(f"检查CutoutConfig.txt {CutoutConfig.replace('imageTemp', 'imagesource')}")
                if os.path.exists(CutoutConfig.replace('imageTemp', 'imagesource')):
                    with open(CutoutConfig.replace('imageTemp', 'imagesource'), "r") as f:
                        cut_info = f.read().split(",")
                else:
                    log.error("不存在CutoutConfig.txt")
                    cut_info = self.cut_info.split(",")
                log.info(f"cut info {cut_info}")
                self.crop_hole_mask=crop_hole(self.img0, kong_num=cut_info[0],lens=cut_info[1],view_judge=int(cut_info[2]))
                self.crop_hole_image = cv2.bitwise_and(self.img0, self.img0,mask=self.crop_hole_mask)
            except Exception as e:
                log.info('no crop hole')
                self.crop_hole_mask = np.ones((self.img0.shape[0], self.img0.shape[1]), dtype=np.uint8)
                self.crop_hole_image = self.img0
                log.error(f"crop hole fail {e}")
            self.crop_bbox = [0, 0, self.img0.shape[1], self.img0.shape[0]]
            self.jyh_jiaodi_img=self.jyh_img
            self.jiaodi_img = self.crop_hole_image
            if jiaodi_corp:
                if max(self.img0.shape) > slice_pred_thed_max:
                    print('------------------------------crop img-------------------------------------')
                    try:
                        self.crop_bbox = crop_muti_image(self.img0)[0]
                        self.jiaodi_img = self.crop_hole_image[self.crop_bbox[1]:self.crop_bbox[3],self.crop_bbox[0]:self.crop_bbox[2]]
                        self.jyh_jiaodi_img = self.jyh_img[self.crop_bbox[1]:self.crop_bbox[3],self.crop_bbox[0]:self.crop_bbox[2]]
                        print("crop_main_image ", self.crop_bbox, self.img0.shape, self.jiaodi_img.shape)
                    except Exception as e:
                        log.error("crop jiaodi fail")
                else:
                    log.info('no crop jiaodi')
        else:
            log.error(f"img error {input_src}")
    def get_obj_list(self,perform_full_pred,mask_crop_processing=False,slice_pred=True):
        durations_in_seconds = dict()
        time_start = time.time()
        # perform sliced prediction
        object_prediction_list = []
        det_time = time.time()
        if perform_full_pred:
            # perform full prediction
            log.info('----------------------------------只进行整图预测---------------------------------')
            for i_shap in list(set(list([960, 1280, self.max_shape]))):
                self.max_shape = i_shap
                print("full predict max_shape", self.max_shape)
                prediction_result = self.get_prediction(
                    image=self.crop_hole_image,
                    input_size=self.max_shape,
                    conf_thres=self.conf_thres,
                    iou_thres=self.iou_thres,
                    model_type=self.process_type
                )
                object_prediction_list.extend(prediction_result)
        else:
            log.info('-------------------------------------切片预测1.整图预测-------------------------------------')
            for i_shap in list(set(list([960, 1280, self.max_shape]))):
                self.max_shape = i_shap
                print("full predict self.max_shape:", self.max_shape)
                prediction_result = self.get_prediction(
                    image=self.crop_hole_image,
                    input_size=self.max_shape,
                    conf_thres=self.conf_thres,
                    iou_thres=self.iou_thres,
                    model_type=self.process_type,
                )
                object_prediction_list.extend(prediction_result)
            if slice_pred:
                if mask_crop_processing:
                    # print('-------------------------------------切片预测1-1.蒙版-------------------------------------')
                    # self.masked_image = self.mask_crop_processing(self.crop_hole_image, object_prediction_list,
                    #                                               self.conf_thres, self.iou_thres)
                    self.masked_image = self.crop_hole_image
                else:
                    self.masked_image = self.crop_hole_image
                log.info('-------------------------------------切片预测2.切片推理-------------------------------------')
                crop_shape = self.max_shape
                print("crop_shape", crop_shape)
                slice_image_result = slice_image(
                    image=convert_from_cv2_to_image(self.masked_image),
                    slice_height=crop_shape,
                    slice_width=crop_shape,
                    overlap_height_ratio=0,
                    overlap_width_ratio=0
                )
                for overlap_rate in [0.5]:
                    log.info(f"overlap rate {overlap_rate}")
                    temp_slice_image_result = slice_image(
                        image=convert_from_cv2_to_image(self.masked_image),
                        slice_height=crop_shape,
                        slice_width=crop_shape,
                        overlap_height_ratio=overlap_rate,
                        overlap_width_ratio=overlap_rate
                    )
                    log.info(
                        f"slice_image_result {len(slice_image_result._sliced_image_list)} temp {len(temp_slice_image_result._sliced_image_list)}")
                    slice_image_result._sliced_image_list.extend(temp_slice_image_result._sliced_image_list)
                log.info(f"end slice_image_result {len(slice_image_result.sliced_image_list)}")
                num_slices = len(slice_image_result)
                log.info("Number of slices:", num_slices)
                durations_in_seconds["slice"] = time.time() - time_start
                # create prediction input
                num_batch = min(1, num_slices)
                log.info(
                    f"Number of slices:{num_slices} num_batch {num_batch} batchsize {self.batch_size}input_size {self.max_shape}")
                # perform sliced prediction
                log.info("处理中...")
                for num_id in tqdm(range(0, num_slices, num_batch)):
                    image_list = slice_image_result.images[num_id:num_id + num_batch]
                    shift_amount_list = slice_image_result.starting_pixels[num_id:num_id + num_batch]
                    for i_shape in [960]:
                        self.max_shape = i_shape
                        prediction_result = self.get_prediction(
                            image=image_list[0],
                            input_size=self.max_shape,
                            shift_amount=shift_amount_list[0],
                            conf_thres=self.conf_thres,
                            iou_thres=self.iou_thres,
                            model_type=self.process_type
                        )
                        object_prediction_list.extend(prediction_result)

        detection_start = time.time()
        durations_in_seconds["detection"] = detection_start - det_time
        # remove empty predictions
        object_prediction_list = [object_prediction for object_prediction in object_prediction_list if object_prediction is not None]
        return object_prediction_list,durations_in_seconds
    def predict(self,input_img,
               output_img_path,
               data_save_path,
               fcs_save_path,
               conf_thres=0.2,
               iou_thres=0.1,
               max_shape=1280,
               slice_pred_thed_max=4128,
               gray_thred = 0,
               sharpne_thresh = 0,
               mask_save_path=None,
               input_labelme_path = "",
               ):
        self.max_shape = max_shape
        self.conf_thres=conf_thres
        self.iou_thres=iou_thres
        self.slice_pred_thed_max = slice_pred_thed_max
        time_start=time.time()
        print("conf_thres:", self.conf_thres, "iou_thres:", self.iou_thres, "max_shape:", self.max_shape,"slice_pred_thed_max:", self.slice_pred_thed_max)
        self.read_image(input_img, jiaodi_corp=True, slice_pred_thed_max=self.slice_pred_thed_max)
        self.process_type="seg" #seg
        print("image_path",input_img,"process_type",self.process_type)
        perform_full_pred = True if max(self.crop_hole_image.shape) < slice_pred_thed_max else False  # whole pic det
        if os.path.exists(input_labelme_path):
            object_prediction_list=[]
            durations_in_seconds={}
        else:
            try:
                self.send_response("MsgTime", self.msg_id, {"tid": self.task_id, "Time": 600})
            except Exception as e:
                log.error(f"send MsgTime error:{e}")
            object_prediction_list, durations_in_seconds =self.get_obj_list(perform_full_pred,mask_crop_processing=True)
        get_infostart = time.time()
        self.get_info(self.img0, object_prediction_list,output_img_path,mask_save_path,data_save_path,fcs_save_path,gray_thred=gray_thred,sharpne_thresh=sharpne_thresh,input_labelme_path=input_labelme_path)
        get_infoend = time.time()
        durations_in_seconds["get_info"] = get_infoend-get_infostart
        durations_in_seconds["cost time"]=time.time()-time_start
        log.info(durations_in_seconds)

    def img_to_rgb_cv_img(self,input_img, img_path=None):
        """
        Convert 8bit/16bit RGB image or 8bit/16bit Gray image to 8bit RGB image
        """
        if img_path is not None and os.path.exists(img_path):
            # Load Image From Path Directly
            cv_image = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
            cv_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
        else:
            cv_image = input_img
        # To uint8
        if cv_image.dtype != np.uint8:
            cv2.normalize(cv_image, cv_image, 0, 255, cv2.NORM_MINMAX)
            cv_image = np.array(cv_image, dtype=np.uint8)
        # To RGB
        if len(cv_image.shape) == 2 or cv_image.shape[2] == 1:
            cv_image = cv2.merge([cv_image, cv_image, cv_image])
        return cv_image
    def box_containment_cuda(self, boxes1, boxes2,ratio=0.7):
        """使用CUDA加速的包含度检查"""
        # 转移数据到GPU
        boxes1_gpu = cp.asarray(boxes1)
        boxes2_gpu = cp.asarray(boxes2)

        # 计算面积
        area1 = (boxes1_gpu[:, 2] - boxes1_gpu[:, 0]) * (boxes1_gpu[:, 3] - boxes1_gpu[:, 1])

        # 计算交集
        x1 = cp.maximum(boxes1_gpu[:, None, 0], boxes2_gpu[:, 0])
        y1 = cp.maximum(boxes1_gpu[:, None, 1], boxes2_gpu[:, 1])
        x2 = cp.minimum(boxes1_gpu[:, None, 2], boxes2_gpu[:, 2])
        y2 = cp.minimum(boxes1_gpu[:, None, 3], boxes2_gpu[:, 3])

        intersection = cp.maximum(0, x2 - x1) * cp.maximum(0, y2 - y1)
        containment_ratio = intersection / area1[:, None]

        return cp.asnumpy(containment_ratio > ratio)
    def get_info(self, img, object_prediction_list, output_img_path,mask_save_path,data_save_path,fcs_save_path, gray_thred=0,
                 sharpne_thresh=0, fix_size=1280, line_thrkness=-1,input_labelme_path=""):
        det = []
        segments = []
        sstime=time.time()
        color_mask = np.zeros(img.shape, dtype=np.uint8)
        # slice_patch_dir=output_img_path[:-4]
        # os.makedirs(slice_patch_dir,exist_ok=True)
        if len(object_prediction_list) > 0:
            if self.process_type=="seg":
                concat_det = np.concatenate([x[0] for x in object_prediction_list], axis=0)
            else:
                concat_det = np.concatenate(object_prediction_list, axis=0)
            log.info("开始执行nms")
            i = large_nms(concat_det[:, :4], concat_det[:, 4], iou_threshold=self.iou_thres,
                          score_threshold=self.conf_thres)
            log.info("nms处理完成")
            log.info(f"{len(i)},开始滤除nms后的重叠框")
            if len(i) > 1:
                sorted_boxes = concat_det[i, :4]
                sorted_scores = concat_det[i, 4]
                # 使用CUDA加速计算包含关系
                containment_matrix = self.box_containment_cuda(sorted_boxes, sorted_boxes, ratio=0.7)
                score_matrix = sorted_scores[:, None] < sorted_scores[None, :]
                should_remove = (containment_matrix & score_matrix).any(axis=1)
                keep = i[~should_remove]
            else:
                keep = i
            det = concat_det[keep]
            log.info(f"Number of Cell:{det.shape[0]}")
            try:
                sstime = time.time()
                for box in tqdm(det[:, :4]):
                    extend_pix=int(min(box[2]-box[0],box[3]-box[1])*2)
                    if extend_pix<150:
                        extend_pix=150
                    #print("extend_pix",extend_pix)
                    box_extend, new_box_map = self.SAMmodel.expand_box(box, img.shape, expand_pixels=extend_pix)
                    slice_x1, slice_y1, slice_x2, slice_y2 = box_extend
                    self.image_embedding = self.SAMmodel.encode(img[slice_y1:slice_y2, slice_x1:slice_x2])
                    origin_masks = self.SAMmodel.predict_masks(self.image_embedding, new_box_map)
                    c = self.SAMmodel.transform_segments(origin_masks, box, self.image_embedding["original_size"],
                                                shift_amount=[slice_x1, slice_y1])
                    segments.append(c.astype('float32'))
                    #print(f"{num} cost time {time.time() - sstime}")
            except Exception as e:
                log.error(e)
        else:
            if os.path.exists(input_labelme_path):
                try:
                    log.info(f"use {input_labelme_path} result computer info")
                    with open(input_labelme_path, "r") as f:
                        labelme_json = json.load(f)
                    shapes = labelme_json["shapes"]
                    det = np.empty([len(shapes), 6])
                    for i in range(len(shapes)):
                        score = 1.0
                        if labelme_json["shapes"][i]["shape_type"] == "polygon":
                            points = labelme_json["shapes"][i]["points"]
                            segments.append(np.array(points).astype('float32'))
                            xs = [p[0] for p in points]
                            ys = [p[1] for p in points]
                            minx = min(xs)
                            maxx = max(xs)
                            miny = min(ys)
                            maxy = max(ys)
                            class_id = 0
                            det[i] = np.array([minx, miny, maxx, maxy, score, class_id])
                except Exception as e:
                    error_msg = traceback.format_exc()
                    log.error(f"labelme_json error {error_msg}")
        try:
            self.send_response("MsgTime", self.msg_id, {"tid": self.task_id, "Time": int(len(segments) * 1) + 600})
        except Exception as e:
            log.error(f"send MsgTime error:{e}")
        print(f"sam cost time:", time.time() - sstime)
        analysis_result_title = ','.join(['细胞编号', '属性值', '通道', 'X坐标', 'Y坐标', '团属性', '面积',
                                        '周长', '长轴', '短轴', '圆度', '边缘锐利度', '边缘差异率', '平均灰度',
                                        '累计灰度',
                                        '最大灰度', '最小灰度', '灰度差', '背景差异', '正圆率', '亮度','轮廓']) + "\n"
        analysis_result = ""

        img_copy_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        Laplacian_img = cv2.Laplacian(img_copy_gray, cv2.CV_64F)

        if max(img.shape) > fix_size:
            scale = max(img.shape) / fix_size
            resized_img = cv2.resize(img, None, fx=1 / scale, fy=1 / scale)  # 缩小
            Laplacian_img = cv2.resize(Laplacian_img, None, fx=1 / scale, fy=1 / scale)
        else:
            resized_img = img
            scale = 1
        del img_copy_gray
        if len(det):
            if mask_save_path is None or mask_save_path=="":
                mask_save_path = output_img_path[:-4]
            obj_mask = tifffile.TiffWriter(f'{mask_save_path}_multimasks.tif')
            boxes, scores, class_ids = det[:, :4], det[:, 4], det[:, 5]
            cell_number=0
            for cell_num, (box, score, class_id) in enumerate(zip(boxes, scores, class_ids)):
                #start_time=time.time()
                # color=Colors()
                # custom_color = color(random.randint(0, 255))
                ctype = 1
                custom_color = (0, 100, 0)
                x1, y1, x2, y2 = box.astype(int)

                # 缩小的bbox
                scale_x1, scale_y1, scale_x2, scale_y2 = list(map(int, [i / scale for i in [x1, y1, x2, y2]]))
                if scale_x2 - scale_x1 == 0 or scale_y2 - scale_y1 == 0:
                    continue
                local_channel = 0
                center_x = int((x1 + x2) / 2)
                center_y = int((y1 + y2) / 2)
                radius = int(((x2 - x1) + (y2 - y1)) / 4)
                group_type = ""  # 团属性
                if len(np.unique(segments[cell_num])) < 3:
                    continue
                try:
                    rect=cv2.minAreaRect(segments[cell_num])
                    # rect 的格式是 (center (x,y), (width, height), angle of rotation)
                    _, (minArea_width, minArea_height), _ = rect
                    major_axis_length=max(minArea_height,minArea_width)
                    minor_axis_length=min(minArea_height,minArea_width)
                    print(rect,major_axis_length,minor_axis_length)
                    box = cv2.boxPoints(rect)
                    box = np.int0(box)
                    if int(major_axis_length)==int(minor_axis_length):
                        cv2.drawContours(self.jyh_jiaodi_img, [box], 0, (0, 0, 255), 2)
                        star_size = 20  # 调整这个值来控制星星大小
                        points = np.array([[0, -star_size], [star_size, -star_size // 2], [star_size * 2, 0],
                                           [star_size, star_size // 2], [0, star_size], [-star_size, star_size // 2],
                                           [-star_size * 2, 0], [-star_size, -star_size // 2]], np.int32)
                        center = tuple(map(int, rect[0]))
                        points += center
                        cv2.fillPoly(self.jyh_jiaodi_img, [points], (255, 255, 0))
                    else:
                        cv2.drawContours(self.jyh_jiaodi_img, [box], 0, (255, 0, 0), 2)
                    #print("major_axis_length",major_axis_length,minor_axis_length)
                except Exception as e:
                    print(e)
                    major_axis_length = max((x2 - x1), (y2 - y1))
                    minor_axis_length = min((x2 - x1), (y2 - y1))
                # 绘图

                bbox_x, bbox_y, bbox_w, bbox_h = cv2.boundingRect(segments[cell_num])
                #print(bbox_x, bbox_y, bbox_w, bbox_h)
                area = int(cv2.contourArea(segments[cell_num] / scale))  # scale放大倍数
                mask = np.zeros(resized_img.shape[:2], dtype=np.uint8)
                mask = cv2.drawContours(mask, np.array([segments[cell_num] / scale]).astype(int), -1, 1, -1)  # 绘图
                circle_mask = self.find_cricel_mask(mask, segments[cell_num])
                mask_crop = cv2.bitwise_and(resized_img, resized_img, mask=mask.astype(np.uint8))
                if max(mask_crop.shape) <= 640:
                    mask_scale = 1
                else:
                    mask_scale = max(mask_crop.shape) / 640
                mask_crop_reize = cv2.resize(mask_crop, (640, 640))
                edge_diff = cv2.Laplacian(mask_crop_reize, cv2.CV_64F).var()*mask_scale  # 整体锐利度

                # x, y, w, h = cv2.boundingRect(segments[cell_num]/scale)
                crop_img = mask_crop[scale_y1:scale_y2, scale_x1:scale_x2]  # resize的bbox
                crop_mask_gray = cv2.cvtColor(crop_img, cv2.COLOR_BGR2GRAY)
                ret, thresh = cv2.threshold(crop_mask_gray, gray_thred, 255, cv2.THRESH_BINARY)
                pxilecount = cv2.countNonZero(thresh)
                mean, std = cv2.meanStdDev(Laplacian_img * circle_mask, mask=circle_mask)
                sharpne = std[0][0]  # 边缘锐利度 轮廓模糊度
                if sharpne <= sharpne_thresh:
                    continue
                avg_gray = np.mean(crop_mask_gray)  # 平均灰度
                sum_gray = np.sum(crop_mask_gray)  # 累计灰度
                min_gray = int(np.min(crop_mask_gray))  # 最小灰度
                max_gray = int(np.max(crop_mask_gray))  # 最大灰度
                diff_gray = pxilecount / area if area != 0 else 0  # 灰度差
                diff_gray = 1 if diff_gray > 1 else diff_gray
                background_diff = ""
                luminance = np.mean(cv2.cvtColor(crop_img, cv2.COLOR_BGR2HSV)[:, :, 2])  # 亮度
                perimeter = cv2.arcLength(segments[cell_num], True)  # 周长
                origin_area = int(cv2.contourArea(segments[cell_num])) # 原始面积
                roundness = (4 * math.pi * origin_area) / perimeter ** 2 if perimeter != 0 else 0  # 圆度
                roundness = 1 if roundness > 1 else roundness
                try:
                    centre, axes, angle = cv2.fitEllipse(segments[cell_num])  # 椭圆拟合
                    if int(major_axis_length)==int(minor_axis_length):
                        cv2.ellipse(self.jyh_jiaodi_img, (centre, axes, angle), (0, 255, 255), 2)
                    else:
                        cv2.ellipse(self.jyh_jiaodi_img, (centre, axes, angle), (255, 0, 255), 2)
                    MAJ = np.argmax(axes)  # this is MAJor axis, 1 or 0
                    MIN = 1 - MAJ  # 0 or 1, minor axis
                    Eccentricity = np.sqrt(1 - (axes[MIN] / axes[MAJ]) ** 2)
                    circle_rate = Eccentricity  # 正圆率
                except Exception as e:
                    print(e)
                    circle_rate = np.sqrt(1 - (minor_axis_length / major_axis_length) ** 2) if major_axis_length != 0 else 0 # 正圆率
                del circle_mask, mask_crop, mask, crop_mask_gray, crop_img
                if debug:
                    #self.jyh_jiaodi_img = cv2.rectangle(self.jyh_jiaodi_img, (x1, y1), (x2, y2), (0, 0, 255), 13)
                    cv2.putText(self.jyh_jiaodi_img,
                                # str(cell_num + 1), (x1, y1 - 10),
                                str(f'{cell_number + 1}-{int(major_axis_length)}-{int(minor_axis_length)}-{circle_rate:.2f}-{roundness:.2f}'), (x1, y1 - 10),
                                cv2.FONT_HERSHEY_SIMPLEX,
                                1, (255, 0, 0),
                                thickness=1)
                # 绘图
                try:
                    # img = cv2.drawContours(img,np.array([segments[cell_num]]).astype(int), -1,custom_color, 2)
                    color_mask = cv2.drawContours(color_mask, np.array([segments[cell_num]]).astype(int), -1,
                                                  custom_color, line_thrkness)  # all zeros np draw cont 浅绿
                    cv2.drawContours(self.jyh_jiaodi_img, np.array([segments[cell_num]]).astype(int), -1,
                                     custom_color, 1)  # all zeros np draw cont 浅绿
                    #生成mask
                    if not debug:
                        print(f"debug {debug}")
                        save_mask = np.zeros(img.shape[:2], dtype=np.uint8)
                        save_mask = cv2.drawContours(save_mask, np.array([segments[cell_num]]).astype(int), -1,1, -1)[bbox_y:bbox_y+bbox_h, bbox_x:bbox_x+bbox_w] # all zeros np draw cont 浅绿
                        obj_mask.write(save_mask, metadata={'cell_number': cell_number+1}, compression="zlib",
                                       compressionargs={'level': 9})
                        del save_mask
                except Exception as e:
                    log.error("drawContours error",exc_info=True)
                res = [ctype, local_channel, center_x + self.crop_bbox[0], center_y + self.crop_bbox[1], group_type,
                       int(origin_area), int(perimeter),
                       int(major_axis_length), int(minor_axis_length), roundness, sharpne, edge_diff,
                       avg_gray, int(sum_gray * scale * scale), max_gray, min_gray, diff_gray, background_diff, circle_rate,
                       luminance,f'{bbox_x+ self.crop_bbox[0]}|{bbox_y+ self.crop_bbox[1]}|{bbox_x+bbox_w+ self.crop_bbox[0]}|{bbox_y+bbox_h+ self.crop_bbox[1]}|#006400|21|0']
                analysis_result += str(cell_number + 1) + "," + ",".join(map(str, res)) + "\n"
                cell_number=cell_number+1
            log.info(f"info cell number:{cell_number}")
            obj_mask.close()
                #print("cost_time",time.time()-start_time)
        #start_time = time.time()
        color_mask = (color_mask * 0.5).astype(np.uint8)
        #img = cv2.add(img, color_mask)  # 合并zeros np 带轮廓（mask or ellipse）
        #img=cv2.add(self.jyh_jiaodi_img, color_mask)
        img=self.jyh_jiaodi_img
        del color_mask, Laplacian_img, resized_img
        #print("cost_time2", time.time() - start_time)
        try:
            self.jyh_img[self.crop_bbox[1]:self.crop_bbox[3],self.crop_bbox[0]:self.crop_bbox[2]] = img  # 改变原图的抠图部分
            cv2.imencode('.jpg', self.jyh_img)[1].tofile(output_img_path)
        except Exception as e:
            print(e)
            log.error(f"结果保存失败{e}")
        end_analysis_result = analysis_result_title + analysis_result
        try:
            if os.path.exists(data_save_path):
                os.remove(data_save_path)
            with open(data_save_path, 'a+', encoding='utf-8') as f:
                f.write(end_analysis_result)
        except Exception as e:
            log.error(f"data_save_path {data_save_path} not exists", exc_info=True)
        try:
            if fcs_save_path != '':
                if os.path.exists(fcs_save_path):
                    os.remove(fcs_save_path)
                with open(fcs_save_path, 'a+', encoding='utf-8') as f:
                    f.write(end_analysis_result)
        except Exception as e:
            log.error(f"fcs_save_path {fcs_save_path} not exists", exc_info=True)
if __name__ == '__main__':
    debug=False
    v5_detmodel=EBanyalysisInfer(seg_engine=r"EB_analysis_APP/weights_seg2/best_dynamic.engine",sam_decode_engine="mobileSAM_APP/mobile_sam_simplify.engine",
                          sam_encode_engine="mobileSAM_APP/vit_t_embedding_simplify.engine")
    v5_detmodel.cut_info="25,4,1"
    for impath in list_images(r"H:\EB球\EB球测试集"):
        if "result" in impath:
            continue
        print(impath)
        # if os.path.exists(impath[:-4] + f"_result0814.jpg"):
        #     continue
        v5_detmodel.predict(impath,
                           output_img_path=impath[:-4] + f"_result.jpg",
                           data_save_path=impath[:-4] + f"_data.csv",
                           fcs_save_path=impath[:-4] + f"_fcs_data.csv",
                           conf_thres=0.5,
                           iou_thres=0.25,
                           max_shape=2560,
                           slice_pred_thed_max=4128)
        #break
