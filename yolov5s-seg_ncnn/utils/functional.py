import numpy as np


def xywh2xyxy(x):
    # Convert nx4 boxes from [x, y, w, h] to [x1, y1, x2, y2] where xy1=top-left, xy2=bottom-right
    y = np.zeros_like(x)
    y[:, 0] = x[:, 0] - x[:, 2] / 2  # top left x
    y[:, 1] = x[:, 1] - x[:, 3] / 2  # top left y
    y[:, 2] = x[:, 0] + x[:, 2] / 2  # bottom right x
    y[:, 3] = x[:, 1] + x[:, 3] / 2  # bottom right y
    return y


def xyxy2xywh(x):
    # Convert nx4 boxes from [x1, y1, x2, y2] to [x, y, w, h] where xy1=top-left, xy2=bottom-right
    y = np.zeros_like(x)
    y[:, 0] = (x[:, 0] + x[:, 2]) / 2  # x center
    y[:, 1] = (x[:, 1] + x[:, 3]) / 2  # y center
    y[:, 2] = x[:, 2] - x[:, 0]  # width
    y[:, 3] = x[:, 3] - x[:, 1]  # height
    return y


def make_grid(nx=20, ny=20):
    xv1, yv1 = np.meshgrid(np.arange(nx), np.arange(ny))
    z1 = np.stack((xv1, yv1), 2).reshape((1, ny, nx, 2)).astype(np.float32)
    return z1


def sigmoid(x):
    return 1 / (1 + np.exp(-x))


def softmax(x):
    max_value = np.max(x, axis=-1)
    x -= max_value.reshape((x.shape[0], 1))
    x = np.exp(x)
    sum_value = np.sum(x, axis=-1)
    x /= sum_value.reshape((x.shape[0], 1))
    return x


def iou_of(boxes0, boxes1, eps=1e-5):
    """Return intersection-over-union (Jaccard index) of boxes.
    Args:
        boxes0 (N, 4): ground truth boxes.
        boxes1 (N or 1, 4): predicted boxes.
        eps: a small number to avoid 0 as denominator.
    Returns:
        iou (N): IoU values.
    """
    overlap_left_top = np.maximum(boxes0[..., :2], boxes1[..., :2])
    overlap_right_bottom = np.minimum(boxes0[..., 2:], boxes1[..., 2:])

    overlap_area = area_of(overlap_left_top, overlap_right_bottom)
    area0 = area_of(boxes0[..., :2], boxes0[..., 2:])
    area1 = area_of(boxes1[..., :2], boxes1[..., 2:])
    return overlap_area / (area0 + area1 - overlap_area + eps)


def area_of(left_top, right_bottom):
    """Compute the areas of rectangles given two corners.
    Args:
        left_top (N, 2): left top corner.
        right_bottom (N, 2): right bottom corner.
    Returns:
        area (N): return the area.
    """
    hw = np.clip(right_bottom - left_top, 0.0, None)
    return hw[..., 0] * hw[..., 1]


# def intersection(boxA, boxB):
#     """
#     Compute area of intersection of two boxes
#     Parameters
#     ----------
#     boxA : np.array
#         First boxes
#     boxB : np.array
#         Second box
#     Returns
#     -------
#     float64
#         Area of intersection
#     """
#     xA = max(boxA[..., 0], boxB[..., 0])
#     xB = min(boxA[..., 2], boxB[..., 2])
#     dx = xB - xA
#     if dx <= 0:
#         return 0.0
#
#     yA = max(boxA[..., 1], boxB[..., 1])
#     yB = min(boxA[..., 3], boxB[..., 3])
#     dy = yB - yA
#     if dy <= 0.0:
#         return 0.0
#
#     # compute the area of intersection rectangle
#     return dx * dy
# def naive_nms(
#     boxes: np.array, scores: np.array, iou_threshold: float = 0.5, score_threshold: float = 0.0
# ) -> np.array:
#     """
#     Naive nms, for timing and comparisons only.
#     Parameters
#     ----------
#     boxes : np.array
#         Array of boxes, in format (x0, y0, x1, y1) with x1 >= x0, y1 >= y0
#     scores : np.array
#         One-dimensional array of confidence scores. Note that in the case of multiclass,
#         this function must be applied class-wise.
#     iou_threshold : float, optional
#         Threshold used to consider two boxes to be overlapping, by default 0.5
#     score_threshold : float, optional
#         Threshold from which boxes are discarded, by default 0.0
#     cutoff_distance: int, optional
#         DEPRECATED, used for compatibility with version 0.1.X.
#         Since version 0.2.X, it is useless because overlapping boxes are queried using a R-Tree,
#         which is parameter free.
#     tree: str, optional
#         DEPRECATED, used for compatibility with version 0.1.X.
#         Since version 0.2.X, the tree used is a R-Tree.
#     Returns
#     -------
#     np.array
#         Indices of boxes kept, in decreasing order of confidence score.
#     """
#     keep = []
#
#     areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
#
#     # n_kept = 0
#     suppressed = np.full(len(scores), False)
#     order = np.argsort(scores, kind="quicksort")[::-1]
#     for i in range(len(boxes)):
#         if suppressed[i]:
#             continue
#         current_idx = order[i]
#
#         if scores[current_idx] < score_threshold:
#             break
#
#         keep.append(current_idx)
#
#         for j in range(i, len(order), 1):
#             if suppressed[j]:
#                 continue
#             inter = intersection(boxes[current_idx], boxes[order[j]])
#             sc = inter / (areas[current_idx] + areas[order[j]] - inter)
#             suppressed[j] = sc > iou_threshold
#
#     keep = np.array(keep)
#
#     return keep
# def nms(boxes, scores, iou_threshold, top_k=-1, candidate_size=200):
#     """
#     Args:
#         box_scores (N, 5): boxes in corner-form(x1, y1, x2, y2) and probabilities.
#         iou_threshold: intersection over union threshold.
#         top_k: keep top_k results. If k <= 0, keep all the results.
#         candidate_size: only consider the candidates with the highest scores.
#     Returns:
#          picked: a list of indexes of the kept boxes
#     """
#
#     picked = []
#     indexes = np.argsort(scores)
#     indexes = indexes[-candidate_size:]
#     while len(indexes) > 0:
#         current = indexes[-1]
#         picked.append(current)
#         if 0 < top_k == len(picked) or len(indexes) == 1:
#             break
#
#         current_box = boxes[current, :]
#         indexes = indexes[:-1]
#         rest_boxes = boxes[indexes, :]
#         iou = iou_of(
#             rest_boxes,
#             np.expand_dims(current_box, axis=0),
#         )
#         indexes = indexes[iou <= iou_threshold]
#
#     return picked

def non_max_suppression(boxes, probs=None, overlapThresh=0.3):
    # if there are no boxes, return an empty list
    if len(boxes) == 0:
        return []

    # if the bounding boxes are integers, convert them to floats -- this
    # is important since we'll be doing a bunch of divisions
    if boxes.dtype.kind == "i":
        boxes = boxes.astype("float")

    # initialize the list of picked indexes
    pick = []

    # grab the coordinates of the bounding boxes
    x1 = boxes[:, 0]
    y1 = boxes[:, 1]
    x2 = boxes[:, 2]
    y2 = boxes[:, 3]

    # compute the area of the bounding boxes and grab the indexes to sort
    # (in the case that no probabilities are provided, simply sort on the
    # bottom-left y-coordinate)
    area = (x2 - x1 + 1) * (y2 - y1 + 1)
    idxs = y2

    # if probabilities are provided, sort on them instead
    if probs is not None:
        idxs = probs

    # sort the indexes
    idxs = np.argsort(idxs)

    # keep looping while some indexes still remain in the indexes list
    while len(idxs) > 0:
        # grab the last index in the indexes list and add the index value
        # to the list of picked indexes
        last = len(idxs) - 1
        i = idxs[last]
        pick.append(i)

        # find the largest (x, y) coordinates for the start of the bounding
        # box and the smallest (x, y) coordinates for the end of the bounding
        # box
        xx1 = np.maximum(x1[i], x1[idxs[:last]])
        yy1 = np.maximum(y1[i], y1[idxs[:last]])
        xx2 = np.minimum(x2[i], x2[idxs[:last]])
        yy2 = np.minimum(y2[i], y2[idxs[:last]])

        # compute the width and height of the bounding box
        w = np.maximum(0, xx2 - xx1 + 1)
        h = np.maximum(0, yy2 - yy1 + 1)

        # compute the ratio of overlap
        overlap = (w * h) / area[idxs[:last]]

        # delete all indexes from the index list that have overlap greater
        # than the provided overlap threshold
        idxs = np.delete(idxs, np.concatenate(([last],
                                               np.where(overlap > overlapThresh)[0])))

    # return only the bounding boxes that were picked
    return pick
