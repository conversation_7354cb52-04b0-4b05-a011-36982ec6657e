7767517
213 245
Input            images                   0 1 images
Convolution      /model.0/conv/Conv       1 1 images /model.0/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=864
Swish            /model.0/act/Mul         1 1 /model.0/conv/Conv_output_0 /model.0/act/Mul_output_0
Convolution      /model.1/conv/Conv       1 1 /model.0/act/Mul_output_0 /model.1/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=18432
Swish            /model.1/act/Mul         1 1 /model.1/conv/Conv_output_0 /model.1/act/Mul_output_0
Convolution      /model.2/cv1/conv/Conv   1 1 /model.1/act/Mul_output_0 /model.2/cv1/conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Swish            /model.2/cv1/act/Mul     1 1 /model.2/cv1/conv/Conv_output_0 /model.2/cv1/act/Mul_output_0
Split            splitncnn_0              1 2 /model.2/cv1/act/Mul_output_0 /model.2/cv1/act/Mul_output_0_splitncnn_0 /model.2/cv1/act/Mul_output_0_splitncnn_1
Crop             /model.2/Slice           1 1 /model.2/cv1/act/Mul_output_0_splitncnn_1 /model.2/Slice_output_0 -23309=1,32 -23310=1,2147483647 -23311=1,0
Split            splitncnn_1              1 2 /model.2/Slice_output_0 /model.2/Slice_output_0_splitncnn_0 /model.2/Slice_output_0_splitncnn_1
Convolution      /model.2/m.0/cv1/conv/Conv 1 1 /model.2/Slice_output_0_splitncnn_1 /model.2/m.0/cv1/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Swish            /model.2/m.0/cv1/act/Mul 1 1 /model.2/m.0/cv1/conv/Conv_output_0 /model.2/m.0/cv1/act/Mul_output_0
Convolution      /model.2/m.0/cv2/conv/Conv 1 1 /model.2/m.0/cv1/act/Mul_output_0 /model.2/m.0/cv2/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Swish            /model.2/m.0/cv2/act/Mul 1 1 /model.2/m.0/cv2/conv/Conv_output_0 /model.2/m.0/cv2/act/Mul_output_0
BinaryOp         /model.2/m.0/Add         2 1 /model.2/Slice_output_0_splitncnn_0 /model.2/m.0/cv2/act/Mul_output_0 /model.2/m.0/Add_output_0 0=0
Concat           /model.2/Concat          2 1 /model.2/cv1/act/Mul_output_0_splitncnn_0 /model.2/m.0/Add_output_0 /model.2/Concat_output_0 0=0
Convolution      /model.2/cv2/conv/Conv   1 1 /model.2/Concat_output_0 /model.2/cv2/conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=6144
Swish            /model.2/cv2/act/Mul     1 1 /model.2/cv2/conv/Conv_output_0 /model.2/cv2/act/Mul_output_0
Convolution      /model.3/conv/Conv       1 1 /model.2/cv2/act/Mul_output_0 /model.3/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=73728
Swish            /model.3/act/Mul         1 1 /model.3/conv/Conv_output_0 /model.3/act/Mul_output_0
Convolution      /model.4/cv1/conv/Conv   1 1 /model.3/act/Mul_output_0 /model.4/cv1/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
Swish            /model.4/cv1/act/Mul     1 1 /model.4/cv1/conv/Conv_output_0 /model.4/cv1/act/Mul_output_0
Split            splitncnn_2              1 2 /model.4/cv1/act/Mul_output_0 /model.4/cv1/act/Mul_output_0_splitncnn_0 /model.4/cv1/act/Mul_output_0_splitncnn_1
Crop             /model.4/Slice           1 1 /model.4/cv1/act/Mul_output_0_splitncnn_1 /model.4/Slice_output_0 -23309=1,64 -23310=1,2147483647 -23311=1,0
Split            splitncnn_3              1 2 /model.4/Slice_output_0 /model.4/Slice_output_0_splitncnn_0 /model.4/Slice_output_0_splitncnn_1
Convolution      /model.4/m.0/cv1/conv/Conv 1 1 /model.4/Slice_output_0_splitncnn_1 /model.4/m.0/cv1/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            /model.4/m.0/cv1/act/Mul 1 1 /model.4/m.0/cv1/conv/Conv_output_0 /model.4/m.0/cv1/act/Mul_output_0
Convolution      /model.4/m.0/cv2/conv/Conv 1 1 /model.4/m.0/cv1/act/Mul_output_0 /model.4/m.0/cv2/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            /model.4/m.0/cv2/act/Mul 1 1 /model.4/m.0/cv2/conv/Conv_output_0 /model.4/m.0/cv2/act/Mul_output_0
BinaryOp         /model.4/m.0/Add         2 1 /model.4/Slice_output_0_splitncnn_0 /model.4/m.0/cv2/act/Mul_output_0 /model.4/m.0/Add_output_0 0=0
Split            splitncnn_4              1 3 /model.4/m.0/Add_output_0 /model.4/m.0/Add_output_0_splitncnn_0 /model.4/m.0/Add_output_0_splitncnn_1 /model.4/m.0/Add_output_0_splitncnn_2
Convolution      /model.4/m.1/cv1/conv/Conv 1 1 /model.4/m.0/Add_output_0_splitncnn_2 /model.4/m.1/cv1/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            /model.4/m.1/cv1/act/Mul 1 1 /model.4/m.1/cv1/conv/Conv_output_0 /model.4/m.1/cv1/act/Mul_output_0
Convolution      /model.4/m.1/cv2/conv/Conv 1 1 /model.4/m.1/cv1/act/Mul_output_0 /model.4/m.1/cv2/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            /model.4/m.1/cv2/act/Mul 1 1 /model.4/m.1/cv2/conv/Conv_output_0 /model.4/m.1/cv2/act/Mul_output_0
BinaryOp         /model.4/m.1/Add         2 1 /model.4/m.0/Add_output_0_splitncnn_1 /model.4/m.1/cv2/act/Mul_output_0 /model.4/m.1/Add_output_0 0=0
Concat           /model.4/Concat          3 1 /model.4/cv1/act/Mul_output_0_splitncnn_0 /model.4/m.0/Add_output_0_splitncnn_0 /model.4/m.1/Add_output_0 /model.4/Concat_output_0 0=0
Convolution      /model.4/cv2/conv/Conv   1 1 /model.4/Concat_output_0 /model.4/cv2/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
Swish            /model.4/cv2/act/Mul     1 1 /model.4/cv2/conv/Conv_output_0 /model.4/cv2/act/Mul_output_0
Split            splitncnn_5              1 2 /model.4/cv2/act/Mul_output_0 /model.4/cv2/act/Mul_output_0_splitncnn_0 /model.4/cv2/act/Mul_output_0_splitncnn_1
Convolution      /model.5/conv/Conv       1 1 /model.4/cv2/act/Mul_output_0_splitncnn_1 /model.5/conv/Conv_output_0 0=256 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=294912
Swish            /model.5/act/Mul         1 1 /model.5/conv/Conv_output_0 /model.5/act/Mul_output_0
Convolution      /model.6/cv1/conv/Conv   1 1 /model.5/act/Mul_output_0 /model.6/cv1/conv/Conv_output_0 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
Swish            /model.6/cv1/act/Mul     1 1 /model.6/cv1/conv/Conv_output_0 /model.6/cv1/act/Mul_output_0
Split            splitncnn_6              1 2 /model.6/cv1/act/Mul_output_0 /model.6/cv1/act/Mul_output_0_splitncnn_0 /model.6/cv1/act/Mul_output_0_splitncnn_1
Crop             /model.6/Slice           1 1 /model.6/cv1/act/Mul_output_0_splitncnn_1 /model.6/Slice_output_0 -23309=1,128 -23310=1,2147483647 -23311=1,0
Split            splitncnn_7              1 2 /model.6/Slice_output_0 /model.6/Slice_output_0_splitncnn_0 /model.6/Slice_output_0_splitncnn_1
Convolution      /model.6/m.0/cv1/conv/Conv 1 1 /model.6/Slice_output_0_splitncnn_1 /model.6/m.0/cv1/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.6/m.0/cv1/act/Mul 1 1 /model.6/m.0/cv1/conv/Conv_output_0 /model.6/m.0/cv1/act/Mul_output_0
Convolution      /model.6/m.0/cv2/conv/Conv 1 1 /model.6/m.0/cv1/act/Mul_output_0 /model.6/m.0/cv2/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.6/m.0/cv2/act/Mul 1 1 /model.6/m.0/cv2/conv/Conv_output_0 /model.6/m.0/cv2/act/Mul_output_0
BinaryOp         /model.6/m.0/Add         2 1 /model.6/Slice_output_0_splitncnn_0 /model.6/m.0/cv2/act/Mul_output_0 /model.6/m.0/Add_output_0 0=0
Split            splitncnn_8              1 3 /model.6/m.0/Add_output_0 /model.6/m.0/Add_output_0_splitncnn_0 /model.6/m.0/Add_output_0_splitncnn_1 /model.6/m.0/Add_output_0_splitncnn_2
Convolution      /model.6/m.1/cv1/conv/Conv 1 1 /model.6/m.0/Add_output_0_splitncnn_2 /model.6/m.1/cv1/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.6/m.1/cv1/act/Mul 1 1 /model.6/m.1/cv1/conv/Conv_output_0 /model.6/m.1/cv1/act/Mul_output_0
Convolution      /model.6/m.1/cv2/conv/Conv 1 1 /model.6/m.1/cv1/act/Mul_output_0 /model.6/m.1/cv2/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.6/m.1/cv2/act/Mul 1 1 /model.6/m.1/cv2/conv/Conv_output_0 /model.6/m.1/cv2/act/Mul_output_0
BinaryOp         /model.6/m.1/Add         2 1 /model.6/m.0/Add_output_0_splitncnn_1 /model.6/m.1/cv2/act/Mul_output_0 /model.6/m.1/Add_output_0 0=0
Concat           /model.6/Concat          3 1 /model.6/cv1/act/Mul_output_0_splitncnn_0 /model.6/m.0/Add_output_0_splitncnn_0 /model.6/m.1/Add_output_0 /model.6/Concat_output_0 0=0
Convolution      /model.6/cv2/conv/Conv   1 1 /model.6/Concat_output_0 /model.6/cv2/conv/Conv_output_0 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=131072
Swish            /model.6/cv2/act/Mul     1 1 /model.6/cv2/conv/Conv_output_0 /model.6/cv2/act/Mul_output_0
Split            splitncnn_9              1 2 /model.6/cv2/act/Mul_output_0 /model.6/cv2/act/Mul_output_0_splitncnn_0 /model.6/cv2/act/Mul_output_0_splitncnn_1
Convolution      /model.7/conv/Conv       1 1 /model.6/cv2/act/Mul_output_0_splitncnn_1 /model.7/conv/Conv_output_0 0=512 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=1179648
Swish            /model.7/act/Mul         1 1 /model.7/conv/Conv_output_0 /model.7/act/Mul_output_0
Convolution      /model.8/cv1/conv/Conv   1 1 /model.7/act/Mul_output_0 /model.8/cv1/conv/Conv_output_0 0=512 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=262144
Swish            /model.8/cv1/act/Mul     1 1 /model.8/cv1/conv/Conv_output_0 /model.8/cv1/act/Mul_output_0
Split            splitncnn_10             1 2 /model.8/cv1/act/Mul_output_0 /model.8/cv1/act/Mul_output_0_splitncnn_0 /model.8/cv1/act/Mul_output_0_splitncnn_1
Crop             /model.8/Slice           1 1 /model.8/cv1/act/Mul_output_0_splitncnn_1 /model.8/Slice_output_0 -23309=1,256 -23310=1,2147483647 -23311=1,0
Split            splitncnn_11             1 2 /model.8/Slice_output_0 /model.8/Slice_output_0_splitncnn_0 /model.8/Slice_output_0_splitncnn_1
Convolution      /model.8/m.0/cv1/conv/Conv 1 1 /model.8/Slice_output_0_splitncnn_1 /model.8/m.0/cv1/conv/Conv_output_0 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=589824
Swish            /model.8/m.0/cv1/act/Mul 1 1 /model.8/m.0/cv1/conv/Conv_output_0 /model.8/m.0/cv1/act/Mul_output_0
Convolution      /model.8/m.0/cv2/conv/Conv 1 1 /model.8/m.0/cv1/act/Mul_output_0 /model.8/m.0/cv2/conv/Conv_output_0 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=589824
Swish            /model.8/m.0/cv2/act/Mul 1 1 /model.8/m.0/cv2/conv/Conv_output_0 /model.8/m.0/cv2/act/Mul_output_0
BinaryOp         /model.8/m.0/Add         2 1 /model.8/Slice_output_0_splitncnn_0 /model.8/m.0/cv2/act/Mul_output_0 /model.8/m.0/Add_output_0 0=0
Concat           /model.8/Concat          2 1 /model.8/cv1/act/Mul_output_0_splitncnn_0 /model.8/m.0/Add_output_0 /model.8/Concat_output_0 0=0
Convolution      /model.8/cv2/conv/Conv   1 1 /model.8/Concat_output_0 /model.8/cv2/conv/Conv_output_0 0=512 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=393216
Swish            /model.8/cv2/act/Mul     1 1 /model.8/cv2/conv/Conv_output_0 /model.8/cv2/act/Mul_output_0
Convolution      /model.9/cv1/conv/Conv   1 1 /model.8/cv2/act/Mul_output_0 /model.9/cv1/conv/Conv_output_0 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=131072
Swish            /model.9/cv1/act/Mul     1 1 /model.9/cv1/conv/Conv_output_0 /model.9/cv1/act/Mul_output_0
Split            splitncnn_12             1 2 /model.9/cv1/act/Mul_output_0 /model.9/cv1/act/Mul_output_0_splitncnn_0 /model.9/cv1/act/Mul_output_0_splitncnn_1
Pooling          /model.9/m/MaxPool       1 1 /model.9/cv1/act/Mul_output_0_splitncnn_1 /model.9/m/MaxPool_output_0 0=0 1=5 11=5 2=1 12=1 3=2 13=2 14=2 15=2 5=1
Split            splitncnn_13             1 2 /model.9/m/MaxPool_output_0 /model.9/m/MaxPool_output_0_splitncnn_0 /model.9/m/MaxPool_output_0_splitncnn_1
Pooling          /model.9/m_1/MaxPool     1 1 /model.9/m/MaxPool_output_0_splitncnn_1 /model.9/m_1/MaxPool_output_0 0=0 1=5 11=5 2=1 12=1 3=2 13=2 14=2 15=2 5=1
Split            splitncnn_14             1 2 /model.9/m_1/MaxPool_output_0 /model.9/m_1/MaxPool_output_0_splitncnn_0 /model.9/m_1/MaxPool_output_0_splitncnn_1
Pooling          /model.9/m_2/MaxPool     1 1 /model.9/m_1/MaxPool_output_0_splitncnn_1 /model.9/m_2/MaxPool_output_0 0=0 1=5 11=5 2=1 12=1 3=2 13=2 14=2 15=2 5=1
Concat           /model.9/Concat          4 1 /model.9/cv1/act/Mul_output_0_splitncnn_0 /model.9/m/MaxPool_output_0_splitncnn_0 /model.9/m_1/MaxPool_output_0_splitncnn_0 /model.9/m_2/MaxPool_output_0 /model.9/Concat_output_0 0=0
Convolution      /model.9/cv2/conv/Conv   1 1 /model.9/Concat_output_0 /model.9/cv2/conv/Conv_output_0 0=512 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=524288
Swish            /model.9/cv2/act/Mul     1 1 /model.9/cv2/conv/Conv_output_0 /model.9/cv2/act/Mul_output_0
Split            splitncnn_15             1 2 /model.9/cv2/act/Mul_output_0 /model.9/cv2/act/Mul_output_0_splitncnn_0 /model.9/cv2/act/Mul_output_0_splitncnn_1
Interp           /model.10/Resize         1 1 /model.9/cv2/act/Mul_output_0_splitncnn_1 /model.10/Resize_output_0 0=1 1=2.000000e+00 2=2.000000e+00 3=0 4=0 6=0
Concat           /model.11/Concat         2 1 /model.10/Resize_output_0 /model.6/cv2/act/Mul_output_0_splitncnn_0 /model.11/Concat_output_0 0=0
Convolution      /model.12/cv1/conv/Conv  1 1 /model.11/Concat_output_0 /model.12/cv1/conv/Conv_output_0 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=196608
Swish            /model.12/cv1/act/Mul    1 1 /model.12/cv1/conv/Conv_output_0 /model.12/cv1/act/Mul_output_0
Split            splitncnn_16             1 2 /model.12/cv1/act/Mul_output_0 /model.12/cv1/act/Mul_output_0_splitncnn_0 /model.12/cv1/act/Mul_output_0_splitncnn_1
Crop             /model.12/Slice          1 1 /model.12/cv1/act/Mul_output_0_splitncnn_1 /model.12/Slice_output_0 -23309=1,128 -23310=1,2147483647 -23311=1,0
Convolution      /model.12/m.0/cv1/conv/Conv 1 1 /model.12/Slice_output_0 /model.12/m.0/cv1/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.12/m.0/cv1/act/Mul 1 1 /model.12/m.0/cv1/conv/Conv_output_0 /model.12/m.0/cv1/act/Mul_output_0
Convolution      /model.12/m.0/cv2/conv/Conv 1 1 /model.12/m.0/cv1/act/Mul_output_0 /model.12/m.0/cv2/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.12/m.0/cv2/act/Mul 1 1 /model.12/m.0/cv2/conv/Conv_output_0 /model.12/m.0/cv2/act/Mul_output_0
Concat           /model.12/Concat         2 1 /model.12/cv1/act/Mul_output_0_splitncnn_0 /model.12/m.0/cv2/act/Mul_output_0 /model.12/Concat_output_0 0=0
Convolution      /model.12/cv2/conv/Conv  1 1 /model.12/Concat_output_0 /model.12/cv2/conv/Conv_output_0 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=98304
Swish            /model.12/cv2/act/Mul    1 1 /model.12/cv2/conv/Conv_output_0 /model.12/cv2/act/Mul_output_0
Split            splitncnn_17             1 2 /model.12/cv2/act/Mul_output_0 /model.12/cv2/act/Mul_output_0_splitncnn_0 /model.12/cv2/act/Mul_output_0_splitncnn_1
Interp           /model.13/Resize         1 1 /model.12/cv2/act/Mul_output_0_splitncnn_1 /model.13/Resize_output_0 0=1 1=2.000000e+00 2=2.000000e+00 3=0 4=0 6=0
Concat           /model.14/Concat         2 1 /model.13/Resize_output_0 /model.4/cv2/act/Mul_output_0_splitncnn_0 /model.14/Concat_output_0 0=0
Convolution      /model.15/cv1/conv/Conv  1 1 /model.14/Concat_output_0 /model.15/cv1/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=49152
Swish            /model.15/cv1/act/Mul    1 1 /model.15/cv1/conv/Conv_output_0 /model.15/cv1/act/Mul_output_0
Split            splitncnn_18             1 2 /model.15/cv1/act/Mul_output_0 /model.15/cv1/act/Mul_output_0_splitncnn_0 /model.15/cv1/act/Mul_output_0_splitncnn_1
Crop             /model.15/Slice          1 1 /model.15/cv1/act/Mul_output_0_splitncnn_1 /model.15/Slice_output_0 -23309=1,64 -23310=1,2147483647 -23311=1,0
Convolution      /model.15/m.0/cv1/conv/Conv 1 1 /model.15/Slice_output_0 /model.15/m.0/cv1/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            /model.15/m.0/cv1/act/Mul 1 1 /model.15/m.0/cv1/conv/Conv_output_0 /model.15/m.0/cv1/act/Mul_output_0
Convolution      /model.15/m.0/cv2/conv/Conv 1 1 /model.15/m.0/cv1/act/Mul_output_0 /model.15/m.0/cv2/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            /model.15/m.0/cv2/act/Mul 1 1 /model.15/m.0/cv2/conv/Conv_output_0 /model.15/m.0/cv2/act/Mul_output_0
Concat           /model.15/Concat         2 1 /model.15/cv1/act/Mul_output_0_splitncnn_0 /model.15/m.0/cv2/act/Mul_output_0 /model.15/Concat_output_0 0=0
Convolution      /model.15/cv2/conv/Conv  1 1 /model.15/Concat_output_0 /model.15/cv2/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=24576
Swish            /model.15/cv2/act/Mul    1 1 /model.15/cv2/conv/Conv_output_0 /model.15/cv2/act/Mul_output_0
Split            splitncnn_19             1 5 /model.15/cv2/act/Mul_output_0 /model.15/cv2/act/Mul_output_0_splitncnn_0 /model.15/cv2/act/Mul_output_0_splitncnn_1 /model.15/cv2/act/Mul_output_0_splitncnn_2 /model.15/cv2/act/Mul_output_0_splitncnn_3 /model.15/cv2/act/Mul_output_0_splitncnn_4
Convolution      /model.16/conv/Conv      1 1 /model.15/cv2/act/Mul_output_0_splitncnn_4 /model.16/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.16/act/Mul        1 1 /model.16/conv/Conv_output_0 /model.16/act/Mul_output_0
Concat           /model.17/Concat         2 1 /model.16/act/Mul_output_0 /model.12/cv2/act/Mul_output_0_splitncnn_0 /model.17/Concat_output_0 0=0
Convolution      /model.18/cv1/conv/Conv  1 1 /model.17/Concat_output_0 /model.18/cv1/conv/Conv_output_0 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=98304
Swish            /model.18/cv1/act/Mul    1 1 /model.18/cv1/conv/Conv_output_0 /model.18/cv1/act/Mul_output_0
Split            splitncnn_20             1 2 /model.18/cv1/act/Mul_output_0 /model.18/cv1/act/Mul_output_0_splitncnn_0 /model.18/cv1/act/Mul_output_0_splitncnn_1
Crop             /model.18/Slice          1 1 /model.18/cv1/act/Mul_output_0_splitncnn_1 /model.18/Slice_output_0 -23309=1,128 -23310=1,2147483647 -23311=1,0
Convolution      /model.18/m.0/cv1/conv/Conv 1 1 /model.18/Slice_output_0 /model.18/m.0/cv1/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.18/m.0/cv1/act/Mul 1 1 /model.18/m.0/cv1/conv/Conv_output_0 /model.18/m.0/cv1/act/Mul_output_0
Convolution      /model.18/m.0/cv2/conv/Conv 1 1 /model.18/m.0/cv1/act/Mul_output_0 /model.18/m.0/cv2/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.18/m.0/cv2/act/Mul 1 1 /model.18/m.0/cv2/conv/Conv_output_0 /model.18/m.0/cv2/act/Mul_output_0
Concat           /model.18/Concat         2 1 /model.18/cv1/act/Mul_output_0_splitncnn_0 /model.18/m.0/cv2/act/Mul_output_0 /model.18/Concat_output_0 0=0
Convolution      /model.18/cv2/conv/Conv  1 1 /model.18/Concat_output_0 /model.18/cv2/conv/Conv_output_0 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=98304
Swish            /model.18/cv2/act/Mul    1 1 /model.18/cv2/conv/Conv_output_0 /model.18/cv2/act/Mul_output_0
Split            splitncnn_21             1 4 /model.18/cv2/act/Mul_output_0 /model.18/cv2/act/Mul_output_0_splitncnn_0 /model.18/cv2/act/Mul_output_0_splitncnn_1 /model.18/cv2/act/Mul_output_0_splitncnn_2 /model.18/cv2/act/Mul_output_0_splitncnn_3
Convolution      /model.19/conv/Conv      1 1 /model.18/cv2/act/Mul_output_0_splitncnn_3 /model.19/conv/Conv_output_0 0=256 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=589824
Swish            /model.19/act/Mul        1 1 /model.19/conv/Conv_output_0 /model.19/act/Mul_output_0
Concat           /model.20/Concat         2 1 /model.19/act/Mul_output_0 /model.9/cv2/act/Mul_output_0_splitncnn_0 /model.20/Concat_output_0 0=0
Convolution      /model.21/cv1/conv/Conv  1 1 /model.20/Concat_output_0 /model.21/cv1/conv/Conv_output_0 0=512 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=393216
Swish            /model.21/cv1/act/Mul    1 1 /model.21/cv1/conv/Conv_output_0 /model.21/cv1/act/Mul_output_0
Split            splitncnn_22             1 2 /model.21/cv1/act/Mul_output_0 /model.21/cv1/act/Mul_output_0_splitncnn_0 /model.21/cv1/act/Mul_output_0_splitncnn_1
Crop             /model.21/Slice          1 1 /model.21/cv1/act/Mul_output_0_splitncnn_1 /model.21/Slice_output_0 -23309=1,256 -23310=1,2147483647 -23311=1,0
Convolution      /model.21/m.0/cv1/conv/Conv 1 1 /model.21/Slice_output_0 /model.21/m.0/cv1/conv/Conv_output_0 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=589824
Swish            /model.21/m.0/cv1/act/Mul 1 1 /model.21/m.0/cv1/conv/Conv_output_0 /model.21/m.0/cv1/act/Mul_output_0
Convolution      /model.21/m.0/cv2/conv/Conv 1 1 /model.21/m.0/cv1/act/Mul_output_0 /model.21/m.0/cv2/conv/Conv_output_0 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=589824
Swish            /model.21/m.0/cv2/act/Mul 1 1 /model.21/m.0/cv2/conv/Conv_output_0 /model.21/m.0/cv2/act/Mul_output_0
Concat           /model.21/Concat         2 1 /model.21/cv1/act/Mul_output_0_splitncnn_0 /model.21/m.0/cv2/act/Mul_output_0 /model.21/Concat_output_0 0=0
Convolution      /model.21/cv2/conv/Conv  1 1 /model.21/Concat_output_0 /model.21/cv2/conv/Conv_output_0 0=512 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=393216
Swish            /model.21/cv2/act/Mul    1 1 /model.21/cv2/conv/Conv_output_0 /model.21/cv2/act/Mul_output_0
Split            splitncnn_23             1 3 /model.21/cv2/act/Mul_output_0 /model.21/cv2/act/Mul_output_0_splitncnn_0 /model.21/cv2/act/Mul_output_0_splitncnn_1 /model.21/cv2/act/Mul_output_0_splitncnn_2
Convolution      /model.22/proto/cv1/conv/Conv 1 1 /model.15/cv2/act/Mul_output_0_splitncnn_3 /model.22/proto/cv1/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.22/proto/cv1/act/Mul 1 1 /model.22/proto/cv1/conv/Conv_output_0 /model.22/proto/cv1/act/Mul_output_0
Deconvolution    /model.22/proto/upsample/ConvTranspose 1 1 /model.22/proto/cv1/act/Mul_output_0 /model.22/proto/upsample/ConvTranspose_output_0 0=128 1=2 11=2 2=1 12=1 3=2 13=2 4=0 14=0 15=0 16=0 5=1 6=65536
Convolution      /model.22/proto/cv2/conv/Conv 1 1 /model.22/proto/upsample/ConvTranspose_output_0 /model.22/proto/cv2/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.22/proto/cv2/act/Mul 1 1 /model.22/proto/cv2/conv/Conv_output_0 /model.22/proto/cv2/act/Mul_output_0
Convolution      /model.22/proto/cv3/conv/Conv 1 1 /model.22/proto/cv2/act/Mul_output_0 /model.22/proto/cv3/conv/Conv_output_0 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Swish            /model.22/proto/cv3/act/Mul 1 1 /model.22/proto/cv3/conv/Conv_output_0 /model.22/proto/cv3/act/Mul_output_0
Convolution      /model.22/cv4.0/cv4.0.0/conv/Conv 1 1 /model.15/cv2/act/Mul_output_0_splitncnn_2 /model.22/cv4.0/cv4.0.0/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            /model.22/cv4.0/cv4.0.0/act/Mul 1 1 /model.22/cv4.0/cv4.0.0/conv/Conv_output_0 /model.22/cv4.0/cv4.0.0/act/Mul_output_0
Convolution      /model.22/cv4.0/cv4.0.1/conv/Conv 1 1 /model.22/cv4.0/cv4.0.0/act/Mul_output_0 /model.22/cv4.0/cv4.0.1/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Swish            /model.22/cv4.0/cv4.0.1/act/Mul 1 1 /model.22/cv4.0/cv4.0.1/conv/Conv_output_0 /model.22/cv4.0/cv4.0.1/act/Mul_output_0
Convolution      /model.22/cv4.0/cv4.0.2/Conv 1 1 /model.22/cv4.0/cv4.0.1/act/Mul_output_0 /model.22/cv4.0/cv4.0.2/Conv_output_0 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1024
Reshape          /model.22/Reshape        1 1 /model.22/cv4.0/cv4.0.2/Conv_output_0 /model.22/Reshape_output_0 0=-1 1=32
Convolution      /model.22/cv4.1/cv4.1.0/conv/Conv 1 1 /model.18/cv2/act/Mul_output_0_splitncnn_2 /model.22/cv4.1/cv4.1.0/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=73728
Swish            /model.22/cv4.1/cv4.1.0/act/Mul 1 1 /model.22/cv4.1/cv4.1.0/conv/Conv_output_0 /model.22/cv4.1/cv4.1.0/act/Mul_output_0
Convolution      /model.22/cv4.1/cv4.1.1/conv/Conv 1 1 /model.22/cv4.1/cv4.1.0/act/Mul_output_0 /model.22/cv4.1/cv4.1.1/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Swish            /model.22/cv4.1/cv4.1.1/act/Mul 1 1 /model.22/cv4.1/cv4.1.1/conv/Conv_output_0 /model.22/cv4.1/cv4.1.1/act/Mul_output_0
Convolution      /model.22/cv4.1/cv4.1.2/Conv 1 1 /model.22/cv4.1/cv4.1.1/act/Mul_output_0 /model.22/cv4.1/cv4.1.2/Conv_output_0 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1024
Reshape          /model.22/Reshape_1      1 1 /model.22/cv4.1/cv4.1.2/Conv_output_0 /model.22/Reshape_1_output_0 0=-1 1=32
Convolution      /model.22/cv4.2/cv4.2.0/conv/Conv 1 1 /model.21/cv2/act/Mul_output_0_splitncnn_2 /model.22/cv4.2/cv4.2.0/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.22/cv4.2/cv4.2.0/act/Mul 1 1 /model.22/cv4.2/cv4.2.0/conv/Conv_output_0 /model.22/cv4.2/cv4.2.0/act/Mul_output_0
Convolution      /model.22/cv4.2/cv4.2.1/conv/Conv 1 1 /model.22/cv4.2/cv4.2.0/act/Mul_output_0 /model.22/cv4.2/cv4.2.1/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Swish            /model.22/cv4.2/cv4.2.1/act/Mul 1 1 /model.22/cv4.2/cv4.2.1/conv/Conv_output_0 /model.22/cv4.2/cv4.2.1/act/Mul_output_0
Convolution      /model.22/cv4.2/cv4.2.2/Conv 1 1 /model.22/cv4.2/cv4.2.1/act/Mul_output_0 /model.22/cv4.2/cv4.2.2/Conv_output_0 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1024
Reshape          /model.22/Reshape_2      1 1 /model.22/cv4.2/cv4.2.2/Conv_output_0 /model.22/Reshape_2_output_0 0=-1 1=32
Concat           /model.22/Concat         3 1 /model.22/Reshape_output_0 /model.22/Reshape_1_output_0 /model.22/Reshape_2_output_0 /model.22/Concat_output_0 0=1
Convolution      /model.22/cv2.0/cv2.0.0/conv/Conv 1 1 /model.15/cv2/act/Mul_output_0_splitncnn_1 /model.22/cv2.0/cv2.0.0/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=73728
Swish            /model.22/cv2.0/cv2.0.0/act/Mul 1 1 /model.22/cv2.0/cv2.0.0/conv/Conv_output_0 /model.22/cv2.0/cv2.0.0/act/Mul_output_0
Convolution      /model.22/cv2.0/cv2.0.1/conv/Conv 1 1 /model.22/cv2.0/cv2.0.0/act/Mul_output_0 /model.22/cv2.0/cv2.0.1/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            /model.22/cv2.0/cv2.0.1/act/Mul 1 1 /model.22/cv2.0/cv2.0.1/conv/Conv_output_0 /model.22/cv2.0/cv2.0.1/act/Mul_output_0
Convolution      /model.22/cv2.0/cv2.0.2/Conv 1 1 /model.22/cv2.0/cv2.0.1/act/Mul_output_0 /model.22/cv2.0/cv2.0.2/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Convolution      /model.22/cv3.0/cv3.0.0/conv/Conv 1 1 /model.15/cv2/act/Mul_output_0_splitncnn_0 /model.22/cv3.0/cv3.0.0/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.22/cv3.0/cv3.0.0/act/Mul 1 1 /model.22/cv3.0/cv3.0.0/conv/Conv_output_0 /model.22/cv3.0/cv3.0.0/act/Mul_output_0
Convolution      /model.22/cv3.0/cv3.0.1/conv/Conv 1 1 /model.22/cv3.0/cv3.0.0/act/Mul_output_0 /model.22/cv3.0/cv3.0.1/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.22/cv3.0/cv3.0.1/act/Mul 1 1 /model.22/cv3.0/cv3.0.1/conv/Conv_output_0 /model.22/cv3.0/cv3.0.1/act/Mul_output_0
Convolution      /model.22/cv3.0/cv3.0.2/Conv 1 1 /model.22/cv3.0/cv3.0.1/act/Mul_output_0 /model.22/cv3.0/cv3.0.2/Conv_output_0 0=2 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=256
Concat           /model.22/Concat_1       2 1 /model.22/cv2.0/cv2.0.2/Conv_output_0 /model.22/cv3.0/cv3.0.2/Conv_output_0 /model.22/Concat_1_output_0 0=0
Convolution      /model.22/cv2.1/cv2.1.0/conv/Conv 1 1 /model.18/cv2/act/Mul_output_0_splitncnn_1 /model.22/cv2.1/cv2.1.0/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.22/cv2.1/cv2.1.0/act/Mul 1 1 /model.22/cv2.1/cv2.1.0/conv/Conv_output_0 /model.22/cv2.1/cv2.1.0/act/Mul_output_0
Convolution      /model.22/cv2.1/cv2.1.1/conv/Conv 1 1 /model.22/cv2.1/cv2.1.0/act/Mul_output_0 /model.22/cv2.1/cv2.1.1/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            /model.22/cv2.1/cv2.1.1/act/Mul 1 1 /model.22/cv2.1/cv2.1.1/conv/Conv_output_0 /model.22/cv2.1/cv2.1.1/act/Mul_output_0
Convolution      /model.22/cv2.1/cv2.1.2/Conv 1 1 /model.22/cv2.1/cv2.1.1/act/Mul_output_0 /model.22/cv2.1/cv2.1.2/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Convolution      /model.22/cv3.1/cv3.1.0/conv/Conv 1 1 /model.18/cv2/act/Mul_output_0_splitncnn_0 /model.22/cv3.1/cv3.1.0/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=294912
Swish            /model.22/cv3.1/cv3.1.0/act/Mul 1 1 /model.22/cv3.1/cv3.1.0/conv/Conv_output_0 /model.22/cv3.1/cv3.1.0/act/Mul_output_0
Convolution      /model.22/cv3.1/cv3.1.1/conv/Conv 1 1 /model.22/cv3.1/cv3.1.0/act/Mul_output_0 /model.22/cv3.1/cv3.1.1/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.22/cv3.1/cv3.1.1/act/Mul 1 1 /model.22/cv3.1/cv3.1.1/conv/Conv_output_0 /model.22/cv3.1/cv3.1.1/act/Mul_output_0
Convolution      /model.22/cv3.1/cv3.1.2/Conv 1 1 /model.22/cv3.1/cv3.1.1/act/Mul_output_0 /model.22/cv3.1/cv3.1.2/Conv_output_0 0=2 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=256
Concat           /model.22/Concat_2       2 1 /model.22/cv2.1/cv2.1.2/Conv_output_0 /model.22/cv3.1/cv3.1.2/Conv_output_0 /model.22/Concat_2_output_0 0=0
Convolution      /model.22/cv2.2/cv2.2.0/conv/Conv 1 1 /model.21/cv2/act/Mul_output_0_splitncnn_1 /model.22/cv2.2/cv2.2.0/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=294912
Swish            /model.22/cv2.2/cv2.2.0/act/Mul 1 1 /model.22/cv2.2/cv2.2.0/conv/Conv_output_0 /model.22/cv2.2/cv2.2.0/act/Mul_output_0
Convolution      /model.22/cv2.2/cv2.2.1/conv/Conv 1 1 /model.22/cv2.2/cv2.2.0/act/Mul_output_0 /model.22/cv2.2/cv2.2.1/conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            /model.22/cv2.2/cv2.2.1/act/Mul 1 1 /model.22/cv2.2/cv2.2.1/conv/Conv_output_0 /model.22/cv2.2/cv2.2.1/act/Mul_output_0
Convolution      /model.22/cv2.2/cv2.2.2/Conv 1 1 /model.22/cv2.2/cv2.2.1/act/Mul_output_0 /model.22/cv2.2/cv2.2.2/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Convolution      /model.22/cv3.2/cv3.2.0/conv/Conv 1 1 /model.21/cv2/act/Mul_output_0_splitncnn_0 /model.22/cv3.2/cv3.2.0/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=589824
Swish            /model.22/cv3.2/cv3.2.0/act/Mul 1 1 /model.22/cv3.2/cv3.2.0/conv/Conv_output_0 /model.22/cv3.2/cv3.2.0/act/Mul_output_0
Convolution      /model.22/cv3.2/cv3.2.1/conv/Conv 1 1 /model.22/cv3.2/cv3.2.0/act/Mul_output_0 /model.22/cv3.2/cv3.2.1/conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            /model.22/cv3.2/cv3.2.1/act/Mul 1 1 /model.22/cv3.2/cv3.2.1/conv/Conv_output_0 /model.22/cv3.2/cv3.2.1/act/Mul_output_0
Convolution      /model.22/cv3.2/cv3.2.2/Conv 1 1 /model.22/cv3.2/cv3.2.1/act/Mul_output_0 /model.22/cv3.2/cv3.2.2/Conv_output_0 0=2 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=256
Concat           /model.22/Concat_3       2 1 /model.22/cv2.2/cv2.2.2/Conv_output_0 /model.22/cv3.2/cv3.2.2/Conv_output_0 /model.22/Concat_3_output_0 0=0
Reshape          /model.22/Reshape_3      1 1 /model.22/Concat_1_output_0 /model.22/Reshape_3_output_0 0=-1 1=66
Reshape          /model.22/Reshape_4      1 1 /model.22/Concat_2_output_0 /model.22/Reshape_4_output_0 0=-1 1=66
Reshape          /model.22/Reshape_5      1 1 /model.22/Concat_3_output_0 /model.22/Reshape_5_output_0 0=-1 1=66
Concat           /model.22/Concat_4       3 1 /model.22/Reshape_3_output_0 /model.22/Reshape_4_output_0 /model.22/Reshape_5_output_0 /model.22/Concat_4_output_0 0=1
Concat           /model.22/Concat_5       2 1 /model.22/Concat_4_output_0 /model.22/Concat_output_0 /model.22/Concat_5_output_0 0=0
Permute          /model.22/Transpose      1 1 /model.22/Concat_5_output_0 output0 0=1
Reshape          /model.22/Reshape_6      1 1 /model.22/proto/cv3/act/Mul_output_0 output1 0=-1 1=32
