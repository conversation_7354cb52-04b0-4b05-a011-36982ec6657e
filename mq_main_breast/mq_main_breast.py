import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2,50))
from multiprocessing import freeze_support
import pefile
from websocket import WebSocketApp
import json
from loguru import logger
import threading
import time
os.environ['PATH'] = os.environ['PATH'] + f';{os.path.abspath(os.getcwd())}' #一定要放在导入cuda,tensorrt前
from breast_infer import Yolov5Seg as InferEngine
import yaml
import traceback
def set_log(app_name):
    # logger.add(f"{app_name}_Run_LOG.txt", rotation="10 MB",
    # retention="10 days", encoding="utf-8"   # 保留最近10天的日志文件
    # )
    logger.add(f"{app_name}_Run_LOG.txt",filter=lambda record: "Keep" not in record["message"], rotation="20 MB",
               retention="10 days", encoding="utf-8"   # 保留最近10天的日志文件
               )
    logger.add(f"{app_name}_heartbeat.txt",filter=lambda record: "Keep" in record["message"], rotation="50 MB",
        retention = "10 days", encoding="utf-8"   # 保留最近10天的日志文件
        )
    return logger

class Predictor(object):
    def __init__(self, app_name="App_name"):
        # APP_name="singleclone"
        self.APP_name = app_name
        self.config = None
        self.predicter = None
        self.log=set_log(self.APP_name)
        self.APP_EXE = f"mq_main_{self.APP_name}.exe"
        self.root_url = 'ws://localhost:12318/ws'  # 这里输入websocket的url
        self.config_yaml = f"{self.APP_name}_APP/config.yaml"
        self.get_version(self.APP_EXE)
        self.ws = None
        self.isConnected = False
        self.isStarted = False
        self.timeOut = 10
        self.wslock = threading.RLock()
        self.msgId = 0
        self.msgList = {}
        self.current_tid_cost_time = None
        self.isBusy = False

    def get_version(self, path):
        def loword_opera(dword):
            return dword & 0x0000ffff

        def hiword_opera(dword):
            return dword >> 16

        try:
            pe = pefile.PE(path)
            ms = pe.VS_FIXEDFILEINFO[0].ProductVersionMS
            ls = pe.VS_FIXEDFILEINFO[0].ProductVersionLS
            version = f'{hiword_opera(ms)}.{loword_opera(ms)}.{hiword_opera(ls)}.{loword_opera(ls)}'
            self.log.info(f"{self.APP_name} AI算法版本:{version}")
        except Exception as e:
            version = f"获取 {self.APP_name} AI算法版本信息出错:{e}"
            self.log.error(f"获取{self.APP_name} AI算法版本信息出错:{e}")
        return version
    def load_config(self):
        try:
            if self.config is None:
                with open(self.config_yaml, encoding='utf-8') as f:
                    self.config = yaml.load(f, Loader=yaml.FullLoader)
                self.log.info(f"load {self.config_yaml} success\n {self.config}")
        except Exception as e:
            error_message = traceback.format_exc()
            self.log.error(f"load config yaml fail {error_message}")

    def load_model(self):
        try:
            if self.predicter is None:
                self.predicter = InferEngine(model_file_path=self.config[f"{self.APP_name}"]["engine"])
                self.predicter.root_url = self.root_url
                self.log.info(f"load model success")
        except Exception as e:
            error_message = traceback.format_exc()
            self.log.error(f"load model fail {error_message}")

    def monitor_model(self, msg_id,task_id, cut_info):
        if self.predicter is None:
            self.load_config()
            self.load_model()
        self.predicter.cut_info = cut_info
        self.predicter.task_id = task_id
        self.predicter.msg_id = msg_id
        self.predicter.ws = self.ws
        self.predicter.send_response=self.send_response
        self.current_tid_cost_time = {task_id: 600}
    def start_msg(self, msg):
        exc_start_time = time.time()
        message = msg.get("Data")
        msg_id = msg.get("ID")
        task_id = message.get("tid")
        status = "success"
        return_msg = ""
        predictors = self.predicter
        log=self.log
        config = self.config
        try:
            exp_type = message["exp_type"]
            try:
                cut_info = message["cut_info"]
            except:
                cut_info = None
                log.info(f"received message not found cut_info,use default value {cut_info}")
            self.monitor_model(msg_id=msg_id,task_id=task_id, cut_info=cut_info)
            if exp_type == "version":
                return_msg = self.get_version(self.APP_EXE)
            elif exp_type == self.APP_name:
                log.info(f"{exp_type}-{task_id}")
                input_image_path = message["input_image_path"]
                try:
                    input_labelme_path = message["input_labelme_path"]
                except Exception as e:
                    input_labelme_path = ""
                    log.info(f"input_labelme_path not found,use default config value ''")
                try:
                    image_save_path = message["image_save_path"]
                except:
                    image_save_path = None
                    log.info(f"image_save_path not found,use default config value {image_save_path}")
                try:
                    mask_save_path = message["mask_save_path"]
                except:
                    mask_save_path = None
                    log.info(f"mask_save_path not found,use default config value {mask_save_path}")
                data_save_path = message["data_save_path"]
                try:
                    conf_thrd = float(message["conf_thrd"])
                except Exception as e:
                    conf_thrd = config[f"{self.APP_name}"]["conf_thrd"]
                    log.info(f"conf_thrd not found,use default config value {conf_thrd}")
                try:
                    iou_thrd = float(message["iou_thrd"])
                    if iou_thrd <= 0:
                        default_iou_value = 0.1
                        log.error(f"iou_thrd not set {iou_thrd},use default iou value {default_iou_value}")
                        iou_thrd = default_iou_value
                except Exception as e:
                    iou_thrd = config[f"{self.APP_name}"]["iou_thrd"]
                    log.info(f"iou_thrd not found,use default config value {iou_thrd}")
                try:
                    gray_thrd = int(message["gray_thrd"])
                except Exception as e:
                    gray_thrd = config[f"{self.APP_name}"]["gray_thrd"]
                    log.info(f"gray_thrd not found,use default config value {gray_thrd}")
                try:
                    Sharpen_thrd = float(message["sharpen_thrd"])
                except Exception as e:
                    Sharpen_thrd = config[f"{self.APP_name}"]["sharpen_thrd"]
                    log.info(f"sharpen_thrd not found,use default config value {Sharpen_thrd}")
                try:
                    slice_pred_thed_max = int(message["slice_pred_thed_max"])
                except Exception as e:
                    slice_pred_thed_max = config[f"{self.APP_name}"]["slice_pred_thed_max"]
                    log.info(f"slice_pred_thed_max not found,use default config value {slice_pred_thed_max}")
                try:
                    max_shape = int(message["max_shape"])
                except Exception as e:
                    max_shape = config[f"{self.APP_name}"]["max_shape"]
                    log.info(f"max_shape not found,use default config value {max_shape}")
                try:
                    fcs_save_path = message["fcs_save_path"]
                except Exception as e:
                    fcs_save_path = ""
                    log.info(f"fcs_save_path not found,use default config value {fcs_save_path}")
                try:
                    ret = predictors.slice_detect(input_img=input_image_path,
                                                  conf_thres=conf_thrd,
                                                  iou_thres=iou_thrd,
                                                  max_shape=max_shape,
                                                  slice_pred_thed_max=slice_pred_thed_max,
                                                  gray_thred=gray_thrd,
                                                  sharpne_thresh=Sharpen_thrd,
                                                  output_img_path=image_save_path,
                                                  data_save_path=data_save_path,
                                                  fcs_save_path=fcs_save_path,
                                                  mask_save_path=mask_save_path,
                                                  input_labelme_path=input_labelme_path)
                except Exception as errormsg:
                    log.error(f"[{self.APP_name} error]: " + str(errormsg), exc_info=True)
                    return_msg = str(errormsg)
                    status = "fail"
            else:
                status = "fail"
                return_msg = f"实验类型 {exp_type} 不存在,本实验应用为 {self.APP_name} 请检查是否匹配"
        except:
            error_message = traceback.format_exc()
            return_msg = str(error_message)
            status = "fail"
            log.error(f"[{self.APP_name} error] - 任务ID: {task_id}: {error_message}")
        elapsed_time = time.time() - exc_start_time
        log.info(f"[{self.APP_name}] - 任务ID: {task_id}-算法执行总耗时: {int(elapsed_time // 60)} 分 {int(elapsed_time % 60)} 秒")
        self.send_response("MsgDone", msg_id, {"tid": task_id, "status": status, "msg": return_msg})

    def on_open(self, ws):
        self.log.info(f'与 {self.root_url} WS连接建立!')
        self.isConnected = True
        # 登录（注册）
        self.send_msg("SignIn", {"AppName":"AICounter", "Service": f"AICounter.{self.APP_name}", "KeepAlive": True})

    def on_message(self, ws, message):
        # 对收到的message进行解析
        print(f"Receive:{message}!")
        rmsg = json.loads(message)
        msgtype = rmsg.get('Type')
        data = rmsg.get('Data')

        # 这是登录消息的回应,服务端会带一个服务端设定的超时时间TimeOut,小于这个间隔发心跳包,下面的代码中用的是 TimeOut/2
        if msgtype == "SignIn":
            self.log.info(f"Receive:{message}!")
            timeOut = data.get('TimeOut')
            self.timeOut = timeOut
            if self.isStarted == False:
                self.isStarted = True
                # 登录完成开始发心跳包
                self.start_keep()
                self.load_config()
                self.load_model()


        # 新的任务消息
        elif msgtype == "NewMsg":
            self.log.info(f"Receive:{message}!")
            if self.isBusy:
                self.deny_msg(rmsg)
            else:
                self.on_msg(rmsg)

        # 对消息开始的回应
        elif msgtype == "MsgStartResponse":
            self.log.info(f"Receive:{message}!")
            self.on_msg_start(rmsg)

        # 对消息时间设置的回应
        elif msgtype == "MsgTimeResponse":
            self.log.info(f"Receive:{message}!")
            self.on_msg_time(rmsg)

        # 对消息完成的回应
        elif msgtype == "MsgDoneResponse":
            self.log.info(f"Receive:{message}!")
            self.on_msg_done(rmsg)

    def on_error(self, ws, error):
        self.log.error(f"WebSocket connection error: {error}")
        self.ws_error(ws)

    def on_close(self, ws, close_status_code, close_msg):
        self.log.info(f'关闭连接! 状态码: {close_status_code}, 消息: {close_msg}')
        self.isConnected = False
        self.close(ws)
        time.sleep(3)
        self.start()

    def start(self):
        self.log.info(f"尝试连接到 {self.root_url}...")
        ws = WebSocketApp(
            self.root_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
        )
        self.ws = ws
        self.ws.run_forever()

    def close(self, ws):
        self.wslock.acquire()
        if ws is not None:
            try:
                ws.close(status=1000, reason="Normal closure")  # 显式关闭连接
            except Exception as e:
                self.log.error(f"显式关闭连接时出错: {e}")
            ws.on_open = None
            ws.on_message = None
            ws.on_error = None
            ws.on_close = None
            self.log.info("连接注销")
        self.wslock.release()

    def ws_error(self, ws):
        if ws == self.ws:
            self.isConnected = False
            self.ws = None
            self.start()
        self.close(ws)

    def send_msg(self, type, data):
        self.msgId += 1
        msg = {"ID": f"{self.msgId}", "Type": type, "Data": data}
        msgLine = json.dumps(msg)
        self.ws.send(msgLine)
        self.log.info(f"Send:{msgLine}")

    def send_response(self, type, id, data):
        msg = {"ID": f"{id}", "Type": type, "Data": data}
        if type=="MsgTime":
            self.current_tid_cost_time=data
        msgLine = json.dumps(msg)
        self.ws.send(msgLine)
        self.log.info(f"Send:{msgLine}")

    def start_keep(self):
        keepThread = threading.Thread(target=self.sendKeep, args=())
        keepThread.start()

    def sendKeep(self):
        while self.isStarted:
            time.sleep(float(self.timeOut / 2))
            self.wslock.acquire()
            try:
                if self.ws is not None and self.isConnected:
                    self.send_msg("Keep", {})
            except Exception as e:
                self.log.error(f"发送keep时出错: {e}")
                if self.isConnected:
                    self.ws_error(self.ws)
            finally:
                self.wslock.release()

    def on_msg(self, msg):
        data = msg.get("Data")
        msg_id = msg.get("ID")
        tid = data.get("tid",None)
        self.msgList[msg_id] = msg
        # 01 收到消息后,回复开始处理
        self.send_response("MsgStart", msg_id, {"tid": tid})
        self.isBusy = True
    def deny_msg(self, msg):
        data = msg.get("Data")
        msg_id = msg.get("ID")
        tid = data.get("tid",None)
        self.msgList[msg_id] = msg
        # 01 收到消息后,回复开始处理
        self.send_response("MsgDeny", msg_id, {"tid": tid,"reson":"已有有任务在处理",
                                               "items":[self.current_tid_cost_time]})
        self.isBusy = False

    def on_msg_start(self, resp):
        self.isBusy = True
        try:
            msg_id = resp.get("ID")
            msg = self.msgList[msg_id]
            if msg is None:
                self.log.error(f"收到的消息没有对应{msg_id}")
            else:
                ok = resp.get("Data").get("Result")
                if ok:
                    # 开始处理消息
                    self.start_msg(msg)
                else:
                    # 如果服务端返回为false,则不能处理消息
                    del self.msgList[msg_id]
                    self.log.info(f"消息处理取消，服务端返回false[{msg_id}]")
        except Exception as e:
            self.log.error(f"on_msg_start 处理消息时出错:{e}")
        finally:
            self.isBusy = False



    def on_msg_time(self, resp):
        msg_id = resp.get("ID")
        msg = self.msgList[msg_id]
        if msg is None:
            self.log.error(f"收到的消息没有对应{msg_id}")
        else:
            ok = resp.get("Data").get("Result")
            if ok:
                self.log.info(f"设置消息时间成功:[{msg_id}]")
            else:
                self.log.error(f"设置消息时间失败:[{msg_id}]")

    def on_msg_done(self, resp):
        msg_id = resp.get("ID")
        msg = self.msgList[msg_id]
        if msg is None:
            self.log.error(f"收到的消息没有对应{msg_id}")
        else:
            ok = resp.get("Data").get("Result")
            self.log.info(f"收到消息完成回应:[{msg_id}]-{ok}")
            del self.msgList[msg_id]


if __name__ == '__main__':
    freeze_support()
    APP_name = "breast"
    Predictor(APP_name).start()


# pyinstaller -D mq_main_singleclone.spec --noconfirm
