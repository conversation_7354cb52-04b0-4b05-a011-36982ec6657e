"""
Copyright 2021, Dana-Farber Cancer Institute and Weill Cornell Medicine
License: GNU GPL 2.0
"""
import numpy as np
from tqdm import tqdm
import copy
import matplotlib.pyplot as plt
from matplotlib import cm
import torch
from torch.optim.lr_scheduler import StepLR
import albumentations as A
try:
    from pathml.ml.hovernet import HoVerNet, post_process_batch_hovernet
except:
    from pathml.ml.hovernet import HoVerNet, post_process_batch_hovernet
from pathml.ml.utils import wrap_transform_multichannel, dice_score
from pathml.utils import plot_segmentation
import os
import re
import shutil
import zipfile
from pathlib import Path

import cv2
import numpy as np
import torch
import torch.utils.data as data
from loguru import logger
from PIL import Image
try:
    from pathml.datasets.base_data_module import BaseDataModule
except:
    from pathml.datasets.base_data_module import BaseDataModule
from pathml.datasets.utils import pannuke_multiclass_mask_to_nucleus_mask
from pathml.ml.hovernet import compute_hv_map,loss_hovernet

batch_size=4
crop_size=1024
n_epochs = 1000
val_step = 20

scale_rate=0.25
data_dir="/mnt/pathml/1_merge2_tifclass/"
cell_flag="1_merge2_tifclass"
# data augmentation transform
hover_transform = A.Compose(
    [
     A.ShiftScaleRotate(scale_limit=(-scale_rate,scale_rate),p=0.5),
     A.SafeRotate(limit=45, p=0.5),
     A.VerticalFlip(p=0.5),
     A.HorizontalFlip(p=0.5),
     #A.GaussianBlur(p=0.5),
     #A.MedianBlur(p=0.5, blur_limit=5),
     A.OpticalDistortion(distort_limit=0.1, shift_limit=0.1,p=0.5),
     A.PadIfNeeded(min_height=crop_size, min_width=crop_size, border_mode=cv2.BORDER_REFLECT,value=0, always_apply=True),#BORDER_CONSTANT
     A.CropNonEmptyMaskIfExists(height=crop_size, width=crop_size, always_apply=True),
]
)
val_transform =A.Compose([
                A.ShiftScaleRotate(scale_limit=(-scale_rate,scale_rate),p=0.5),
                A.SafeRotate(limit=45, p=0.5),
                A.VerticalFlip(p=0.5),
                A.HorizontalFlip(p=0.5),
                A.OpticalDistortion(distort_limit=0.1, shift_limit=0.1,p=0.5),
                A.PadIfNeeded(min_height=crop_size, min_width=crop_size, border_mode=cv2.BORDER_REFLECT, value=0, always_apply=True),#BORDER_CONSTANT
                A.CropNonEmptyMaskIfExists(height=crop_size, width=crop_size, always_apply=True),
])
transform = hover_transform #wrap_transform_multichannel(hover_transform)
class Cell_Dataset(data.Dataset):

    def __init__(
        self,
        data_dir,
        fold_ix=None,
        transforms=None,
        nucleus_type_labels=False,
        hovernet_preprocess=False,
    ):
        self.data_dir = data_dir
        self.fold_ix = fold_ix
        self.transforms = transforms
        self.nucleus_type_labels = nucleus_type_labels
        self.hovernet_preprocess = hovernet_preprocess

        data_dir = Path(data_dir)

        # dirs for images, masks
        self.sub_dir = data_dir / self.fold_ix

        # stop if the images and masks directories don't already exist
        assert self.sub_dir.is_dir(), f"Error: 'images' directory not found: {self.sub_dir}"

        if self.fold_ix is None:
            paths = list(self.sub_dir.glob("*_masks.tif"))
        else:
            paths = list(self.sub_dir.glob("*_masks.tif"))

        self.paths = [p.stem for p in paths]
        print(self.sub_dir,len(self.paths),self.paths)
    def __len__(self):
        return len(self.paths)
    def convert_from_image_to_cv2(self,img: Image) -> np.ndarray:
        return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

    def __getitem__(self, ix):
        stem = self.paths[ix]
        impath = self.sub_dir / f"{stem[:-6]}.tif"
        maskpath = self.sub_dir / f"{stem}.tif"
        tissue_type = "cell"
        #print(impath,maskpath)
        im = self.convert_from_image_to_cv2(Image.open(str(impath)))
        mask = np.array(Image.open(str(maskpath)))

        if self.transforms is not None:
            #print(im.shape,mask.shape)
            transformed = self.transforms(image=im, mask=mask)
            im = transformed["image"]
            mask = transformed["mask"]
        # compute hv map
        if self.hovernet_preprocess:
            if self.nucleus_type_labels:
                # sum across mask channels to squash mask channel dim to size 1
                # don't sum the last channel, which is background!
                mask_1c = pannuke_multiclass_mask_to_nucleus_mask(mask)
            else:
                mask_1c = mask
            hv_map = compute_hv_map(mask_1c)
        # swap channel dim to pytorch standard (C, H, W)
        im = im.transpose((2, 0, 1))
        if self.nucleus_type_labels is False:
                # only look at "background" mask in last channel
                zero_mask = np.zeros_like(mask)
                # invert so that ones are nuclei pixels
                zero_mask[mask>0]=1
                mask=zero_mask
                #mask=np.expand_dims(zero_mask, axis=0)
                #mask=zero_mask[None,...]

        if self.hovernet_preprocess:
            out = (
                torch.from_numpy(im),
                torch.from_numpy(mask),
                torch.from_numpy(hv_map),
                tissue_type,
            )
        else:
            out = torch.from_numpy(im), torch.from_numpy(mask), tissue_type

        return out

class CellDataModule(BaseDataModule):
    def __init__(
        self,
        data_dir,
        shuffle=True,
        transforms=None,
        nucleus_type_labels=False,
        split=None,
        batch_size=4,
        hovernet_preprocess=False,
    ):
        self.data_dir = Path(data_dir)
        self.shuffle = shuffle
        self.transforms = transforms
        self.nucleus_type_labels = nucleus_type_labels
        self.batch_size = batch_size
        self.hovernet_preprocess = hovernet_preprocess
        self.split=split

    def _get_dataset(self, fold_ix, augment=True):
        if augment:
            transforms = self.transforms
        else:
            transforms = val_transform
        return Cell_Dataset(
            data_dir=self.data_dir,
            fold_ix=fold_ix,
            transforms=transforms,
            nucleus_type_labels=self.nucleus_type_labels,
            hovernet_preprocess=self.hovernet_preprocess,
        )

    @property
    def train_dataloader(self):
        """
        Dataloader for training set.
        Yields (image, mask, tissue_type), or (image, mask, hv, tissue_type) for HoVer-Net
        """
        return data.DataLoader(
            dataset=self._get_dataset(fold_ix=self.split[0], augment=True),
            batch_size=self.batch_size,
            shuffle=self.shuffle,
            pin_memory=True,
        )

    @property
    def valid_dataloader(self):
        """
        Dataloader for validation set.
        Yields (image, mask, tissue_type), or (image, mask, hv, tissue_type) for HoVer-Net
        """
        return data.DataLoader(
            self._get_dataset(fold_ix=self.split[1], augment=False),
            batch_size=self.batch_size,
            shuffle=self.shuffle,
            pin_memory=True,
        )

    @property
    def test_dataloader(self):
        """
        Dataloader for test set.
        Yields (image, mask, tissue_type), or (image, mask, hv, tissue_type) for HoVer-Net
        """
        return data.DataLoader(
            self._get_dataset(fold_ix=self.split[2], augment=False),
            batch_size=self.batch_size,
            shuffle=self.shuffle,
            pin_memory=True,
        )
pannuke = CellDataModule(
    data_dir=data_dir,
    nucleus_type_labels=False,
    batch_size=batch_size,
    hovernet_preprocess=True,
    split=["train","test","test"],
    transforms=transform
)

train_dataloader = pannuke.train_dataloader
valid_dataloader = pannuke.valid_dataloader
test_dataloader = pannuke.test_dataloader
images, masks, hvs, types = next(iter(train_dataloader))
print(images.shape,masks.shape,hvs.shape,types)
n = batch_size
fig, ax = plt.subplots(nrows=n, ncols=4, figsize = (8, 8))

cm_mask = copy.copy(cm.get_cmap("tab10"))
cm_mask.set_bad(color='white')

for i in range(n):
    im = images[i, ...].numpy()
    ax[i, 0].imshow(np.moveaxis(im, 0, 2))
    m = masks[i, ...]
    m = np.ma.masked_where(m == 0, m)
    ax[i, 1].imshow(m, cmap = cm_mask)
    ax[i, 2].imshow(hvs[i, 0, ...], cmap = 'coolwarm')
    ax[i, 3].imshow(hvs[i, 1, ...], cmap = 'coolwarm')

for a in ax.ravel(): a.axis("off")
for c,v in enumerate(["Image", "Cell Types", "Horizontal Map", "Vertical Map"]):
    ax[0, c].set_title(v)

plt.tight_layout()
plt.show()
print(f"GPUs used:\t{torch.cuda.device_count()}")
device = torch.device("cuda:0")
print(f"Device:\t\t{device}")
n_classes_pannuke = None

# load the model
hovernet = HoVerNet(n_classes=n_classes_pannuke)

# wrap model to use multi-GPU
hovernet = torch.nn.DataParallel(hovernet)
# set up optimizer
opt = torch.optim.Adam(hovernet.parameters(), lr = 1e-4)
# learning rate scheduler to reduce LR by factor of 10 each 25 epochs
scheduler = StepLR(opt, step_size=25, gamma=0.1)
# send model to GPU
hovernet.to(device);


# print performance metrics every n epochs
print_every_n_epochs = None

# evaluating performance on a random subset of validation mini-batches
# this saves time instead of evaluating on the entire validation set
n_minibatch_valid = 50

epoch_train_losses = {}
epoch_valid_losses = {}
epoch_train_dice = {}
epoch_valid_dice = {}

best_epoch = 0

# main training loop
for i in tqdm(range(n_epochs)):
    minibatch_train_losses = []
    minibatch_train_dice = []

    # put model in training mode
    hovernet.train()

    for data in train_dataloader:
        # send the data to the GPU
        images = data[0].float().to(device)
        masks = data[1].to(device)
        hv = data[2].float().to(device)
        tissue_type = data[3]
        #print(masks.shape)
        if masks.dim() == 3:
            masks = masks.unsqueeze(1)
        #print(masks.shape)
        # zero out gradient
        opt.zero_grad()

        # forward pass
        outputs = hovernet(images)

        # compute loss
        loss = loss_hovernet(outputs = outputs, ground_truth = [masks, hv], n_classes=n_classes_pannuke)

        # track loss
        minibatch_train_losses.append(loss.item())

        # also track dice score to measure performance
        preds_detection = post_process_batch_hovernet(outputs, n_classes=None)
        truth_binary = masks[:, -1, :, :] == 0
        dice = dice_score(preds_detection, truth_binary.cpu().numpy())
        minibatch_train_dice.append(dice)

        # compute gradients
        loss.backward()

        # step optimizer and scheduler
        opt.step()

    #step LR scheduler
    scheduler.step()

    # evaluate on random subset of validation data
    hovernet.eval()
    minibatch_valid_losses = []
    minibatch_valid_dice = []
    # randomly choose minibatches for evaluating
    #n_minibatch_valid = min(n_minibatch_valid, len(valid_dataloader))
    #minibatch_ix = np.random.choice(range(len(valid_dataloader)), replace=False, size=n_minibatch_valid)
    with torch.no_grad():
        for _ in range(val_step):
            for j, data in enumerate(valid_dataloader):
                # send the data to the GPU
                images = data[0].float().to(device)
                masks = data[1].to(device)
                hv = data[2].float().to(device)
                tissue_type = data[3]

                if masks.dim() == 3:
                    masks = masks.unsqueeze(1)

                # forward pass
                outputs = hovernet(images)

                # compute loss
                loss = loss_hovernet(outputs=outputs, ground_truth=[masks, hv], n_classes=n_classes_pannuke)

                # track loss
                minibatch_valid_losses.append(loss.item())

                # also track dice score to measure performance
                preds_detection = post_process_batch_hovernet(outputs, n_classes=n_classes_pannuke)
                truth_binary = masks[:, -1, :, :] == 0

                dice = dice_score(preds_detection, truth_binary.cpu().numpy())
                minibatch_valid_dice.append(dice)

    # average performance metrics over minibatches
    mean_train_loss = np.mean(minibatch_train_losses)
    mean_valid_loss = np.mean(minibatch_valid_losses)
    mean_train_dice = np.mean(minibatch_train_dice)
    mean_valid_dice = np.mean(minibatch_valid_dice)

    # save the model with best performance
    if i != 0:
        if mean_valid_loss < min(epoch_valid_losses.values()):
            best_epoch = i
            torch.save(hovernet.state_dict(), f"{cell_flag}_hovernet_best_perf_crop_size{crop_size}.pt")

    # track performance over training epochs
    epoch_train_losses.update({i : mean_train_loss})
    epoch_valid_losses.update({i : mean_valid_loss})
    epoch_train_dice.update({i : mean_train_dice})
    epoch_valid_dice.update({i : mean_valid_dice})

    if print_every_n_epochs is not None:
        if i % print_every_n_epochs == print_every_n_epochs - 1:
            print(f"Epoch {i+1}/{n_epochs}:")
            print(f"\ttraining loss: {np.round(mean_train_loss, 4)}\tvalidation loss: {np.round(mean_valid_loss, 4)}")
            print(f"\ttraining dice: {np.round(mean_train_dice, 4)}\tvalidation dice: {np.round(mean_valid_dice, 4)}")

# save fully trained model
torch.save(hovernet.state_dict(), f"{cell_flag}_hovernet_fully_trained_crop_size{crop_size}.pt")
print(f"\nEpoch with best validation performance: {best_epoch}")
fix, ax = plt.subplots(nrows=1, ncols=2, figsize = (10, 4))

ax[0].plot(epoch_train_losses.keys(), epoch_train_losses.values(), label = "Train")
ax[0].plot(epoch_valid_losses.keys(), epoch_valid_losses.values(), label = "Validation")
ax[0].scatter(x=best_epoch, y=epoch_valid_losses[best_epoch], label = "Best Model",
              color = "green", marker="*")
ax[0].set_title("Training: Loss")
ax[0].set_xlabel("Epoch")
ax[0].set_ylabel("Loss")
ax[0].legend()

ax[1].plot(epoch_train_dice.keys(), epoch_train_dice.values(), label = "Train")
ax[1].plot(epoch_valid_dice.keys(), epoch_valid_dice.values(), label = "Validation")
ax[1].scatter(x=best_epoch, y=epoch_valid_dice[best_epoch], label = "Best Model",
              color = "green", marker="*")
ax[1].set_title("Training: Dice Score")
ax[1].set_xlabel("Epoch")
ax[1].set_ylabel("Dice Score")
ax[1].legend()
plt.show()
# load the latest model
checkpoint = torch.load(f"{cell_flag}_hovernet_fully_trained_crop_size{crop_size}.pt")
hovernet.load_state_dict(checkpoint)
hovernet.eval()
dummy_input = torch.randn(1, 3, crop_size, crop_size).to(device)
input_names=["input"]
output_names = ["out_np","out_hv"]
onnx_path=f"{cell_flag}_hovernet_fully_trained_crop_size{crop_size}.onnx"
torch.onnx.export(hovernet.module, dummy_input, onnx_path, input_names=input_names,
                  output_names=output_names, opset_version=11)
dynamic_axes = {'input': {2: 'height', 3: 'width'},
                    'out_np': {2: 'height', 3: 'width'},
                    'out_hv': {2: 'height', 3: 'width'}}
torch.onnx.export(hovernet.module, dummy_input, f"{cell_flag}_hovernet_fully_trained_crop_size{crop_size}_dynamic.onnx", input_names=input_names,
                      output_names=output_names, opset_version=11, dynamic_axes=dynamic_axes)
# load the best model
checkpoint = torch.load(f"{cell_flag}_hovernet_best_perf_crop_size{crop_size}.pt")
hovernet.load_state_dict(checkpoint)
hovernet.eval()
dummy_input = torch.randn(1, 3, crop_size, crop_size).to(device)
input_names=["input"]
output_names = ["out_np","out_hv"]
onnx_path=f"{cell_flag}_hovernet_best_perf_crop_size{crop_size}.onnx"
torch.onnx.export(hovernet.module, dummy_input, onnx_path, input_names=input_names,
                  output_names=output_names, opset_version=11)
torch.onnx.export(hovernet.module, dummy_input, f"{cell_flag}_hovernet_best_perf_crop_size{crop_size}_dynamic.onnx", input_names=input_names,
                      output_names=output_names, opset_version=11, dynamic_axes=dynamic_axes)