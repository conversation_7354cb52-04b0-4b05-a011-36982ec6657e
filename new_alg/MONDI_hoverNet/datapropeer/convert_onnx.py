import os
from argparse import ArgumentParser

import torch
from monai.networks.nets import HoVerNet
from monai.utils import set_determinism
def run(cfg):
    set_determinism(seed=cfg["seed"])
    if cfg["mode"].lower() == "original":
        cfg["patch_size"] = [270, 270]
        cfg["out_size"] = [80, 80]
    elif cfg["mode"].lower() == "fast":
        cfg["patch_size"] = [256, 256]
        cfg["out_size"] = [164, 164]
    device = torch.device("cuda" if cfg["use_gpu"] else "cpu")
    hovernet_model = HoVerNet(
        mode=cfg["mode"],
        in_channels=3,
        out_classes=cfg["out_classes"],
    ).to(device)
    hovernet_model.load_state_dict(
        torch.load(cfg["ckpt"], map_location=device)["model"])
    hovernet_model.eval()
    if cfg["out_classes"] > 1:
        out_names = ["np", "hv", "nc"]
        dynamic_axes_name = {'input': {2: 'height', 3: 'width'},
                             'np': {2: 'height', 3: 'width'},
                             'hv': {2: 'height', 3: 'width'},
                             'nc': {2: 'height', 3: 'width'}
                             }
    else:
        out_names = ["np", "hv"]
        dynamic_axes_name = {'input': {2: 'height', 3: 'width'},
                             'np': {2: 'height', 3: 'width'},
                             'hv': {2: 'height', 3: 'width'}
                             }
    torch.onnx.export(
        hovernet_model,
        torch.randn([1, 3, 256, 256]).to(device),
        os.path.join(cfg["ckpt"].replace(".pt",".onnx").replace(".pth",".onnx")),
        export_params=True,
        input_names=["input"],
        output_names=out_names,
        opset_version=13,
    )
    torch.onnx.export(
        hovernet_model,
        torch.randn([1, 3, 256, 256]).to(device),
        os.path.join(cfg["ckpt"].replace(".pt","_dynamic.onnx").replace(".pth","_dynamic.onnx")),
        export_params=True,
        input_names=["input"],
        output_names=out_names,
        dynamic_axes=dynamic_axes_name,
        opset_version=13,
    )
def main():
    parser = ArgumentParser(description="Tumor detection on whole slide pathology images.")
    parser.add_argument("--mode", type=str, default="fast", help="choose either `original` or `fast`")
    parser.add_argument("-s", "--seed", type=int, default=24)
    parser.add_argument("--classes", type=int, default=2, dest="out_classes", help="output classes")
    parser.add_argument("--no-gpu", action="store_false", dest="use_gpu", help="deactivate use of gpu")
    parser.add_argument("--ckpt", type=str, dest="ckpt", help="model checkpoint path")

    args = parser.parse_args()
    cfg = vars(args)
    print(cfg)
    run(cfg)

if __name__ == "__main__":
    main()