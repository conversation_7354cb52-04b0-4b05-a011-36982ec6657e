import json
import time
from pathlib import Path
import os
from PIL import ImageDraw
from PIL import Image
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
import pycuda.driver as cuda
import tensorrt as trt
import cupy as cp
import cv2
from tqdm import tqdm, trange
import sys
from skimage.segmentation import find_boundaries

debug=False
FILE = Path(__file__).absolute()
sys.path.append(FILE.parents[0].as_posix())
from loguru import logger as log
import traceback

import numpy as np
from omnipose_post_processor_cupy import post_proc_omnipose
def get_bounding_box(img):
    """Get bounding box coordinate information."""
    rows = np.any(img, axis=1)
    cols = np.any(img, axis=0)
    rmin, rmax = np.where(rows)[0][[0, -1]]
    cmin, cmax = np.where(cols)[0][[0, -1]]
    # due to python indexing, need to add 1 to max
    # else accessing will be 1px in the box, not out
    rmax += 1
    cmax += 1
    return [rmin, rmax, cmin, cmax]

class omnipose:
    def __init__(self, model_file_path=None):
        super().__init__()
        cuda.init()
        self.device = cuda.Device(0)
        self.cuda_driver_context = self.device.retain_primary_context()
        self.trt_logger = trt.Logger(trt.Logger.ERROR)
        self.batch = 1
        self.pre_process_type = None
        self.fix_engine_shape = 512
        self.model_dynamic_flag = False
        self.model_file = dict()
        self.model_file["omnipose"] = model_file_path
        self.model_load()
        self.classification=False
    def model_load(self):
        self.process_type = "omnipose"
        self.init_engine(self.model_file[self.process_type])
    def init_engine(self, model_file_path):
        self.engine = None
        self.context = None
        if not os.path.exists(model_file_path):
            log.error(f"model engine file is not exists in {model_file_path}")
        try:
            with open(model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                engine = runtime.deserialize_cuda_engine(f.read())
            assert engine
        except:
            log.error("load model error")
            from export_trt import EngineBuilder
            if os.path.exists(model_file_path.replace(".engine", ".onnx")):
                log.info("find onnx model,try to load onnx model convert to engine")
                builder = EngineBuilder(verbose=False)
                builder.get_engine(model_file_path.replace(".engine", ".onnx"), model_file_path)
            else:
                log.error(
                    f"{model_file_path.replace('.engine', '.onnx')} file is not exists,not convert model to engine,pls check")
            with open(model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                engine = runtime.deserialize_cuda_engine(f.read())
        try:
            assert engine
            context = engine.create_execution_context()
            assert context
        except:
            with open(model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                engine = runtime.deserialize_cuda_engine(f.read())
            context = engine.create_execution_context()
            error_message = traceback.format_exc()
            log.error(f"infer engine error and reload engine:{error_message}")
        if context.get_binding_shape(0)[-1] == -1:
            self.model_dynamic_flag = True
            log.info(
                f"{self.process_type} dynamic shape:{context.get_binding_shape(0)} model_dynamic_flag {self.model_dynamic_flag}")
        else:
            self.model_dynamic_flag = False
            self.fix_engine_shape = int(context.get_binding_shape(0)[-1])
            log.info(
                f"{self.process_type} fix shape:{context.get_binding_shape(0)} model_dynamic_flag {self.model_dynamic_flag} fix_engine_shape {self.fix_engine_shape}")

        return engine, context

    def allocate_buffers(self, input_shape=None):
        self.inputs = []
        self.outputs = []
        self.allocations = []
        self.batch_size = input_shape[0]
        self.cuda_driver_context.push()
        for i in range(self.engine.num_bindings):
            name = self.engine.get_binding_name(i)
            dtype = self.engine.get_binding_dtype(i)
            if self.engine.binding_is_input(i):
                if -1 in tuple(self.engine.get_binding_shape(i)):  # dynamic
                    self.context.set_binding_shape(i, tuple(input_shape))
            shape = tuple(self.context.get_binding_shape(i))
            size = np.dtype(trt.nptype(dtype)).itemsize
            for s in shape:
                size *= s
            allocation = cuda.mem_alloc(size)
            binding = {
                'index': i,
                'name': name,
                'dtype': np.dtype(trt.nptype(dtype)),
                'shape': list(shape),
                'allocation': allocation,
            }
            self.allocations.append(allocation)
            if self.engine.binding_is_input(i):
                self.inputs.append(binding)
            else:
                self.outputs.append(binding)
        self.cuda_driver_context.pop()
        #print(self.outputs)
        assert self.batch_size > 0
        assert len(self.inputs) > 0
        assert len(self.outputs) > 0
        assert len(self.allocations) > 0
    def resize(self,im, target_size=608, interp=cv2.INTER_LINEAR):
        if isinstance(target_size, list) or isinstance(target_size, tuple):
            w = target_size[0]
            h = target_size[1]
        else:
            w = target_size
            h = target_size
        im = cv2.resize(im, (w, h), interpolation=interp)
        return im
    def preprocess(self, image):
        # print("image.shape", image.shape)
        im_info=[]
        if image.shape[0]!= self.fix_engine_shape or image.shape[1]!= self.fix_engine_shape:
            im_info = [('resize', image.shape[:2])]
            image = self.resize(image, self.fix_engine_shape)
        mean = np.array([0.5, 0.5, 0.5])
        std = np.array([0.5, 0.5, 0.5])
        image = image/255.0
        image -= mean
        image /= std
        input_image = np.transpose(image, (2, 0, 1))
        input_image = np.expand_dims(input_image, axis=0)
        input_image = input_image.astype('float32')
        return input_image,im_info
    def tensort_infer(self, input_image):
        print("input_image.shape", input_image.shape)
        preprocessed_image,im_info = self.preprocess(input_image)
        print("preprocessed_image.shape", preprocessed_image.shape)
        self.allocate_buffers(preprocessed_image.shape)
        cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(preprocessed_image))
        self.context.execute_v2(self.allocations)
        assert len(self.outputs) in {2, 3}, (
            f"outputs has size {len(self.outputs)}. Must have size 2 (for segmentation) or 3 (for "
            f"classification)"
        )
        for i in range(len(self.outputs)):
            name = self.outputs[i]['name']
            print(i,name,"shape", self.outputs[i]['shape'])
            if name in ["omnipose"]:
                out_omnipose = np.zeros(self.outputs[i]['shape'], dtype=self.outputs[i]['dtype'])
                cuda.memcpy_dtoh(out_omnipose, self.outputs[i]['allocation'])
            if name in ["type"]:
                out_type = np.zeros(self.outputs[i]['shape'], dtype=self.outputs[i]['dtype'])
                cuda.memcpy_dtoh(out_type, self.outputs[i]['allocation'])
        ##concatenate
        outputs = np.concatenate([out_omnipose, out_type], axis=1)
        #print(outputs.shape)
        outputs = np.transpose(outputs, (0, 2, 3, 1))
        #print(outputs.shape)
        outputs = np.ascontiguousarray(outputs)[0]
        for info in im_info[::-1]:
            if info[0] == 'resize':
                w, h = info[1][1], info[1][0]
                outputs = self.resize(outputs, target_size=(w, h), interp=cv2.INTER_NEAREST)
        return outputs

    def read_image(self, input_src):
        jyh_img = cv2.imdecode(np.fromfile(input_src, dtype=np.uint8), -1)
        try:
            if "_cut.jpg" in input_src:
                img0 = cv2.imdecode(np.fromfile(input_src[:-8] + "_merge_src.jpg", dtype=np.uint8), cv2.IMREAD_COLOR)
            else:
                img0 = cv2.imdecode(np.fromfile(input_src, dtype=np.uint8), cv2.IMREAD_COLOR)
        except Exception as e:
            img0 = jyh_img
            error_message = traceback.format_exc()
            log.error(f"不存在均一化图，使用原图进行分析 {error_message}")
        print("img0.shape", img0.shape)
        CutoutConfig = f"{os.sep.join(os.path.dirname(input_src).split(os.sep)[:-1])}\CutoutConfig.txt"
        if os.path.exists(
                CutoutConfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp', 'imagesource')):
            CutoutConfig = CutoutConfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp',
                                                                                        'imagesource')
        log.info(f"CutoutConfig.txt {CutoutConfig.replace('imageTemp', 'imagesource')}")
        try:
            if os.path.exists(CutoutConfig.replace('imageTemp', 'imagesource')):
                with open(CutoutConfig.replace('imageTemp', 'imagesource'), "r") as f:
                    cut_info = f.read().split(",")
            else:
                log.error("不存在CutoutConfig.txt")
                cut_info = self.cut_info.split(",")
            log.info(f"cut info {cut_info}")
            # crop_hole_mask = crop_hole(img0, kong_num=cut_info[0], lens=cut_info[1],
            #                            view_judge=int(cut_info[2]))
            # crop_hole_image = cv2.bitwise_and(img0, img0, mask=crop_hole_mask)
            crop_hole_mask = np.ones((img0.shape[0], img0.shape[1]), dtype=np.uint8)
            crop_hole_image = cv2.bitwise_and(img0, img0, mask=crop_hole_mask)
        except Exception as e:
            crop_hole_mask = np.ones((img0.shape[0], img0.shape[1]), dtype=np.uint8)
            crop_hole_image = img0
            log.info(f"crop hole error {e}")
        self.crop_bbox = [0, 0, img0.shape[1], img0.shape[0]]
        return jyh_img, img0, crop_hole_mask, crop_hole_image, img0.shape

    def omnipose_predict(self, src_x,tile_size=512,pad_size=128,scale_facer=1):
        self.process_type = "omnipose"
        log.info("omnipose")
        if self.engine is not None:
            if self.pre_process_type != self.process_type:
                log.info(f"切换 {self.process_type} engine from {self.model_file[self.process_type]}")
                self.engine, self.context = self.init_engine(self.model_file[self.process_type])
        else:
            log.info(f"初始化 engine from {self.model_file[self.process_type]}")
            self.engine, self.context = self.init_engine(self.model_file[self.process_type])
        self.pre_process_type = self.process_type
        start_time = time.time()
        image = src_x
        if scale_facer != 1:
            image = cv2.resize(src_x, None, fx=1 / scale_facer, fy=1 / scale_facer)
            log.info(f"{src_x.shape} -resize-> {image.shape}")
        if self.model_dynamic_flag is False:
            tile_size=self.fix_engine_shape
            pad_size=int(self.fix_engine_shape/4)
        if image.shape[0]==self.fix_engine_shape and image.shape[1]==self.fix_engine_shape:
            pred_full = self.tensort_infer(image)
        else:
            tile_size=[tile_size,tile_size]
            pad_size=[pad_size,pad_size]
            log.info(f"tile_size:{tile_size} pad_size:{pad_size}")
            height, width, channel = image.shape
            image_tile_list = list()
            pred_full = np.zeros((height, width,4),dtype=np.float32)
            if pad_size[0] > 0:
                left_pad = cv2.flip(image[0:height, 0:pad_size[0], :], 1)
                right_pad = cv2.flip(image[0:height, -pad_size[0]:width, :], 1)
                padding_image = cv2.hconcat([left_pad, image])
                padding_image = cv2.hconcat([padding_image, right_pad])
            else:
                import copy
                padding_image = copy.deepcopy(image)
            # Padding along the upper and lower sides
            padding_height, padding_width, _ = padding_image.shape
            if pad_size[1] > 0:
                upper_pad = cv2.flip(
                    padding_image[0:pad_size[1], 0:padding_width, :], 0)
                lower_pad = cv2.flip(
                    padding_image[-pad_size[1]:padding_height, 0:padding_width, :],
                    0)
                padding_image = cv2.vconcat([upper_pad, padding_image])
                padding_image = cv2.vconcat([padding_image, lower_pad])
            # crop the padding image into tile pieces
            padding_height, padding_width, _ = padding_image.shape
            for h_id in range(0, height // tile_size[1] + 1):
                for w_id in range(0, width // tile_size[0] + 1):
                    left = w_id * tile_size[0]
                    upper = h_id * tile_size[1]
                    right = min(left + tile_size[0] + pad_size[0] * 2,
                                padding_width)
                    lower = min(upper + tile_size[1] + pad_size[1] * 2,
                                padding_height)
                    image_tile = padding_image[upper:lower, left:right, :]
                    image_tile_list.append(image_tile)
            num_tiles = len(image_tile_list)
            log.info(f"num_tiles:{num_tiles}")
            for i in tqdm(range(0, num_tiles)):
                if i % 10 == 0:
                    log.info(f"predict tile:{i} image_tile_list{i}.shape:{image_tile_list[i].shape} {i}/{num_tiles}")
                tile_label_map=self.tensort_infer(image_tile_list[i])
                h_id = i // (width // tile_size[0] + 1)
                w_id = i % (width // tile_size[0] + 1)
                left = w_id * tile_size[0]
                upper = h_id * tile_size[1]
                right = min((w_id + 1) * tile_size[0], width)
                lower = min((h_id + 1) * tile_size[1], height)
                tile_upper = pad_size[1]
                tile_lower = tile_label_map.shape[0] - pad_size[1]
                tile_left = pad_size[0]
                tile_right = tile_label_map.shape[1] - pad_size[0]
                pred_full[upper:lower, left:right] = tile_label_map[tile_upper:tile_lower, tile_left:tile_right]
        pred_full=pred_full.transpose(2, 0, 1)
        print(f"pred_full shape {pred_full.shape}")
        omnipose_map=pred_full[:2, ...]
        inst_map = np.argmax(pred_full[2:,...],axis=0)
        print(inst_map.shape, omnipose_map.shape)
        masks = post_proc_omnipose(inst_map,omnipose_map)
        print(np.unique(masks))
        print(f"cell number {np.unique(masks).size-1}")
        outlines = find_boundaries(masks, mode="inner")
        outY, outX = np.nonzero(outlines)
        # -1是轮廓，0是背景，1开始是细胞
        masks = masks.astype(np.int32)
        #计算mask对象面积均值
        masks[outY, outX] = np.array([-1])
        if masks.shape[0] != src_x.shape[0]:
            masks = cv2.resize(masks, (src_x.shape[1], src_x.shape[0]), interpolation=cv2.INTER_NEAREST)
        print(masks.shape, src_x.shape)
        log.info("结束omnipose")
        log.info(f"cost time {time.time() - start_time} s")
        return masks

    def color_marker(self, markersend):
        markers_cp = markersend.copy()
        markers_cp = cp.array(markers_cp)
        num_markers = cp.unique(markers_cp).size - 1
        if num_markers > 0:
            colors_cp = cp.random.randint(0, 255, size=(num_markers, 3))
            color_mask_cp = colors_cp[markers_cp]
            clor_mask = color_mask_cp.get()
        else:
            clor_mask = np.zeros((markersend.shape[0], markersend.shape[1], 3), dtype=np.uint8)
        clor_mask[markersend == -1] = [255, 255, 255]
        clor_mask[markersend == 0] = [0, 0, 0]
        return clor_mask.astype(np.uint8)

    def get_color_mask(self, markersend, custome_color=None):
        color_mask = np.zeros((markersend.shape[0], markersend.shape[1], 3), dtype=np.uint8)
        if custome_color:
            color_mask[markersend >0] = custome_color  #
            color_mask[markersend == -1] = [255, 255, 255]
        else:
            color_mask = self.color_marker(markersend)
        return color_mask

    def brightened_image(self, img, channel=0):
        channels = cv2.split(img)
        # 提取指定通道，并merge成三通道
        merge_img = cv2.merge([channels[channel], channels[channel], channels[channel]])
        return merge_img


    def save_resultimage(self,img, save_path):
        try:
            if save_path[-4:] not in [".jpg", ".png"]:
                save_path = os.path.join(save_path, "1.png")
            if not os.path.exists(os.path.dirname(save_path)):
                os.makedirs(os.path.dirname(save_path))
            cv2.imencode('.jpg', img)[1].tofile(save_path)
            log.info(f"save_resultimage sucess {save_path}")
        except Exception as e:
            log.info(f"save_resultimage error {e}")

    def predict(self,
                input_image_path,
                image_save_path,
                mask_save_path,
                data_save_path,
                fcs_save_path,
                input_labelme_path="",
                scale_size=3661,
                flow_threshold=0.4,
                custome_color=None,
                ):
        if custome_color is None:
            custome_color = [255, 0, 0]
        log.info(f"处理input_img {input_image_path}")
        self.bf_jyh_img, bf_src, bf_hole_mask, bf_crop_image, origin_shape = self.read_image(input_image_path)
        #cv2.imwrite(image_save_path, bf_crop_image)
        #scale_size=int(max(bf_crop_image.shape)/2)
        bf_scale_facer = max(int(max(bf_crop_image.shape) / scale_size), 1)
        log.info(f"scale_size {scale_size} scale_facer {bf_scale_facer}")
        bf_markers=np.zeros_like(bf_hole_mask)
        if os.path.exists(input_labelme_path):
            try:
                log.info(f"use {input_labelme_path} result computer info")
                with open(input_labelme_path, "r") as f:
                    labelme_json = json.load(f)
                shapes = labelme_json["shapes"]
                mask = np.zeros(bf_src.shape[:2], dtype=np.int32)
                mask = Image.fromarray(mask)
                draw = ImageDraw.Draw(mask)
                start_num = 2
                for i in range(len(shapes)):
                    if labelme_json["shapes"][i]["shape_type"] == "polygon":
                        points = labelme_json["shapes"][i]["points"]
                        xy = [tuple(point) for point in points]
                        draw.polygon(xy=xy, outline=-1, fill=start_num, width=2)
                        start_num += 1
                bf_markers = np.array(mask, dtype=np.int32)
            except:
                error_message = traceback.format_exc()
                log.error(f"labelme_json error:{error_message}")
        else:
            try:
                bf_markers = self.omnipose_predict(bf_crop_image,scale_facer=1)
                bf_clor_mask = self.get_color_mask(bf_markers, custome_color=custome_color)
                # print(bf_jyh_img.shape, bf_clor_mask.shape)
                bf_clor_map = cv2.add(self.bf_jyh_img, (bf_clor_mask * 0.5).astype(np.uint8))
                cv2.imencode(".jpg", bf_clor_map)[1].tofile(image_save_path)
            except:
                error_message = traceback.format_exc()
                log.error(f"omnipose predict error:{error_message}")

if __name__ == '__main__':
    debug = True
    model = omnipose(model_file_path=r"convnext_small_omnipose_20241104huizhong_tifclass_crop_size512_scale/convnext_small_omnipose_20241104huizhong_tifclass_crop_size512_omnipose_best_crop_size512.engine")
    #model = omnipose(model_file_path=r"convnext_small_omnipose_20241104huizhong_tifclass_crop_size512/convnext_small_omnipose_20241104huizhong_tifclass_crop_size512_omnipose_best_crop_size512.engine")
    model.cut_info="25,4,1"
    flage =""
    # model.predict(input_image_path="A1_30_01_01.jpg", image_save_path=f"result.jpg", data_save_path="H:\_result.csv",
    #               fcs_save_path="H:\_fcs_result.csv", mask_save_path=None)
    # model.predict(input_image_path="A1_46_01_01.jpg", image_save_path=f"result2.jpg",
    #               data_save_path="H:\_result.csv",
    #               fcs_save_path="H:\_fcs_result.csv", mask_save_path=None)
    # model.predict(input_image_path="A2_01_source_BF_3.jpg", image_save_path=f"result3.jpg",
    #               data_save_path="H:\_result.csv",
    #               fcs_save_path="H:\_fcs_result.csv", mask_save_path=None)
    # model.predict(input_image_path="C10_01_BF_merge_51.jpg", image_save_path=f"result4.jpg",
    #               data_save_path="H:\_result.csv",
    #               fcs_save_path="H:\_fcs_result.csv", mask_save_path=None)
    from imutils.paths import list_images
    for impath in list_images(r"H:\分割计数\1"):
        if "_result" in impath:
            continue
        print(impath)
        model.predict(input_image_path=impath, image_save_path=impath[:-4]+f"_result_ominipose{flage}.jpg",
                      data_save_path="H:\_result.csv",
                      fcs_save_path="H:\_fcs_result.csv", mask_save_path=None)