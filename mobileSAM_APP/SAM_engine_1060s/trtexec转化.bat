C:\TensorRT-8.5.1.7\bin\trtexec.exe --onnx=vit_t_embedding_simplify.onnx --workspace=2048 --saveEngine=vit_t_embedding_simplify.engine

C:\TensorRT-8.5.1.7\bin\trtexec.exe --onnx=mobile_sam_simplify.onnx --workspace=2048 --shapes=image_embeddings:1x256x64x64,point_coords:1x1x2,point_labels:1x1,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2 --minShapes=image_embeddings:1x256x64x64,point_coords:1x1x2,point_labels:1x1,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2 --optShapes=image_embeddings:1x256x64x64,point_coords:1x10x2,point_labels:1x10,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2 --maxShapes=image_embeddings:1x256x64x64,point_coords:1x20x2,point_labels:1x20,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2 --saveEngine=mobile_sam_simplify.engine

D:\TensorRT-8.5.1.7cuda11.7\bin\trtexec.exe --onnx=vit_t_embedding_simplify.onnx --workspace=2048 --saveEngine=vit_t_embedding_simplify.engine

D:\TensorRT-8.5.1.7cuda11.7\bin\trtexec.exe --onnx=mobile_sam_simplify.onnx --workspace=2048 --shapes=image_embeddings:1x256x64x64,point_coords:1x1x2,point_labels:1x1,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2 --minShapes=image_embeddings:1x256x64x64,point_coords:1x1x2,point_labels:1x1,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2 --optShapes=image_embeddings:1x256x64x64,point_coords:1x10x2,point_labels:1x10,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2 --maxShapes=image_embeddings:1x256x64x64,point_coords:1x20x2,point_labels:1x20,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2 --saveEngine=mobile_sam_simplify.engine