import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
import pycuda.driver as cuda #cuda和tensorrt放在cupy之前,否则运行会出错
from pycuda.tools import clear_context_caches
import gc
from loguru import logger as log


class Colors:
    # Ultralytics color palette https://ultralytics.com/
    def __init__(self):
        # hex = matplotlib.colors.TABLEAU_COLORS.values()
        hexs = ('FF3838', 'FF9D97', 'FF701F', 'FFB21D', 'CFD231', '48F90A', '92CC17', '3DDB86', '1A9334', '00D4BB',
                '2C99A8', '00C2FF', '344593', '6473FF', '0018EC', '8438FF', '520085', 'CB38FF', 'FF95C8', 'FF37C7')
        self.palette = [self.hex2rgb(f'#{c}') for c in hexs]
        self.n = len(self.palette)

    def __call__(self, i, bgr=False):
        c = self.palette[int(i) % self.n]
        return (c[2], c[1], c[0]) if bgr else c

    @staticmethod
    def hex2rgb(h):  # rgb order (PIL)
        return tuple(int(h[1 + i:1 + i + 2], 16) for i in (0, 2, 4))

class GPUPredictorUtil(object):
    def __init__(self):
        self.gpu_id = 0
        self.allocations = None
        self.engine = None
        self.context = None
        self.inputs = None
        self.outputs = None
        self.device = None
        self.ctx = None

        self.model_file_path = None
        self.SAMmodel=None
        self.task_id = ""
        self.cut_info = None
        self.msg_id = None
        self.ws = None
        self.root_url = 'ws://localhost:12318/ws'  # 这里输入websocket的url
        self.send_response=None
        os.environ['CUDA_VISIBLE_DEVICES'] = f"{self.gpu_id}"



    def release(self):
        self.context.__del__()
        self.engine.__del__()
        if self.allocations:
            for allocation in self.allocations:
                allocation.free()
        clear_context_caches()
        self.allocations = None
        self.inputs = None
        self.outputs = None
        self.engine = None
        self.context = None
        self.SAMmodel=None
        self.gpu_id = 0
        self.ctx = None
        cuda.init()
        self.device = cuda.Device(self.gpu_id)
        self.ctx = self.device.retain_primary_context()
        gc.collect()
        log.info("release model{}".format(self.model_file_path))
        return None
