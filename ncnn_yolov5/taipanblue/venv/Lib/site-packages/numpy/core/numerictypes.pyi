from typing import TypeVar, Optional, Type, Union, Tuple, Sequence, overload, Any

from numpy import generic, ndarray, dtype
from numpy.typing import DTypeLike

_DefaultType = TypeVar("_DefaultType")

def maximum_sctype(t: DTypeLike) -> dtype: ...
def issctype(rep: object) -> bool: ...
@overload
def obj2sctype(rep: object) -> Optional[generic]: ...
@overload
def obj2sctype(rep: object, default: None) -> Optional[generic]: ...
@overload
def obj2sctype(
    rep: object, default: Type[_DefaultType]
) -> Union[generic, Type[_DefaultType]]: ...
def issubclass_(arg1: object, arg2: Union[object, Tuple[object, ...]]) -> bool: ...
def issubsctype(
    arg1: Union[ndarray, DTypeLike], arg2: Union[ndarray, DTypeLike]
) -> bool: ...
def issubdtype(arg1: DType<PERSON>ike, arg2: DTypeLike) -> bool: ...
def sctype2char(sctype: object) -> str: ...
def find_common_type(
    array_types: Sequence[DTypeLike], scalar_types: Sequence[DTypeLike]
) -> dtype: ...

# TODO: Add annotations for the following objects:
# typeDict, nbytes, cast, ScalarType & typecodes
