7767517
176 212
Input            images                   0 1 images
YoloV5Focus  	 focus                    1 1 images 167
Convolution      Conv_41                  1 1 167 168 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=3456
Swish            Mul_43                   1 1 168 170
Convolution      Conv_44                  1 1 170 171 0=64 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=18432
Swish            Mul_46                   1 1 171 173
Split            splitncnn_0              1 2 173 173_splitncnn_0 173_splitncnn_1
Convolution      Conv_47                  1 1 173_splitncnn_1 174 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2048
Swish            Mul_49                   1 1 174 176
Split            splitncnn_1              1 2 176 176_splitncnn_0 176_splitncnn_1
Convolution      Conv_50                  1 1 176_splitncnn_1 177 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1024
Swish            Mul_52                   1 1 177 179
Convolution      Conv_53                  1 1 179 180 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Swish            Mul_55                   1 1 180 182
BinaryOp         Add_56                   2 1 176_splitncnn_0 182 183 0=0
Convolution      Conv_57                  1 1 173_splitncnn_0 184 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2048
Swish            Mul_59                   1 1 184 186
Concat           Concat_60                2 1 183 186 187 0=0
Convolution      Conv_61                  1 1 187 188 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Swish            Mul_63                   1 1 188 190
Convolution      Conv_64                  1 1 190 191 0=128 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=73728
Swish            Mul_66                   1 1 191 193
Split            splitncnn_2              1 2 193 193_splitncnn_0 193_splitncnn_1
Convolution      Conv_67                  1 1 193_splitncnn_1 194 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=8192
Swish            Mul_69                   1 1 194 196
Split            splitncnn_3              1 2 196 196_splitncnn_0 196_splitncnn_1
Convolution      Conv_70                  1 1 196_splitncnn_1 197 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Swish            Mul_72                   1 1 197 199
Convolution      Conv_73                  1 1 199 200 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            Mul_75                   1 1 200 202
BinaryOp         Add_76                   2 1 196_splitncnn_0 202 203 0=0
Split            splitncnn_4              1 2 203 203_splitncnn_0 203_splitncnn_1
Convolution      Conv_77                  1 1 203_splitncnn_1 204 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Swish            Mul_79                   1 1 204 206
Convolution      Conv_80                  1 1 206 207 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            Mul_82                   1 1 207 209
BinaryOp         Add_83                   2 1 203_splitncnn_0 209 210 0=0
Split            splitncnn_5              1 2 210 210_splitncnn_0 210_splitncnn_1
Convolution      Conv_84                  1 1 210_splitncnn_1 211 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Swish            Mul_86                   1 1 211 213
Convolution      Conv_87                  1 1 213 214 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            Mul_89                   1 1 214 216
BinaryOp         Add_90                   2 1 210_splitncnn_0 216 217 0=0
Convolution      Conv_91                  1 1 193_splitncnn_0 218 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=8192
Swish            Mul_93                   1 1 218 220
Concat           Concat_94                2 1 217 220 221 0=0
Convolution      Conv_95                  1 1 221 222 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
Swish            Mul_97                   1 1 222 224
Split            splitncnn_6              1 2 224 224_splitncnn_0 224_splitncnn_1
Convolution      Conv_98                  1 1 224_splitncnn_1 225 0=256 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=294912
Swish            Mul_100                  1 1 225 227
Split            splitncnn_7              1 2 227 227_splitncnn_0 227_splitncnn_1
Convolution      Conv_101                 1 1 227_splitncnn_1 228 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
Swish            Mul_103                  1 1 228 230
Split            splitncnn_8              1 2 230 230_splitncnn_0 230_splitncnn_1
Convolution      Conv_104                 1 1 230_splitncnn_1 231 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
Swish            Mul_106                  1 1 231 233
Convolution      Conv_107                 1 1 233 234 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            Mul_109                  1 1 234 236
BinaryOp         Add_110                  2 1 230_splitncnn_0 236 237 0=0
Split            splitncnn_9              1 2 237 237_splitncnn_0 237_splitncnn_1
Convolution      Conv_111                 1 1 237_splitncnn_1 238 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
Swish            Mul_113                  1 1 238 240
Convolution      Conv_114                 1 1 240 241 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            Mul_116                  1 1 241 243
BinaryOp         Add_117                  2 1 237_splitncnn_0 243 244 0=0
Split            splitncnn_10             1 2 244 244_splitncnn_0 244_splitncnn_1
Convolution      Conv_118                 1 1 244_splitncnn_1 245 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
Swish            Mul_120                  1 1 245 247
Convolution      Conv_121                 1 1 247 248 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            Mul_123                  1 1 248 250
BinaryOp         Add_124                  2 1 244_splitncnn_0 250 251 0=0
Convolution      Conv_125                 1 1 227_splitncnn_0 252 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
Swish            Mul_127                  1 1 252 254
Concat           Concat_128               2 1 251 254 255 0=0
Convolution      Conv_129                 1 1 255 256 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
Swish            Mul_131                  1 1 256 258
Split            splitncnn_11             1 2 258 258_splitncnn_0 258_splitncnn_1
Convolution      Conv_132                 1 1 258_splitncnn_1 259 0=512 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=1179648
Swish            Mul_134                  1 1 259 261
Convolution      Conv_135                 1 1 261 262 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=131072
Swish            Mul_137                  1 1 262 264
Split            splitncnn_12             1 4 264 264_splitncnn_0 264_splitncnn_1 264_splitncnn_2 264_splitncnn_3
Pooling          MaxPool_138              1 1 264_splitncnn_3 265 0=0 1=5 11=5 2=1 12=1 3=2 13=2 14=2 15=2 5=1
Pooling          MaxPool_139              1 1 264_splitncnn_2 266 0=0 1=9 11=9 2=1 12=1 3=4 13=4 14=4 15=4 5=1
Pooling          MaxPool_140              1 1 264_splitncnn_1 267 0=0 1=13 11=13 2=1 12=1 3=6 13=6 14=6 15=6 5=1
Concat           Concat_141               4 1 264_splitncnn_0 265 266 267 268 0=0
Convolution      Conv_142                 1 1 268 269 0=512 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=524288
Swish            Mul_144                  1 1 269 271
Split            splitncnn_13             1 2 271 271_splitncnn_0 271_splitncnn_1
Convolution      Conv_145                 1 1 271_splitncnn_1 272 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=131072
Swish            Mul_147                  1 1 272 274
Convolution      Conv_148                 1 1 274 275 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
Swish            Mul_150                  1 1 275 277
Convolution      Conv_151                 1 1 277 278 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=589824
Swish            Mul_153                  1 1 278 280
Convolution      Conv_154                 1 1 271_splitncnn_0 281 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=131072
Swish            Mul_156                  1 1 281 283
Concat           Concat_157               2 1 280 283 284 0=0
Convolution      Conv_158                 1 1 284 285 0=512 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=262144
Swish            Mul_160                  1 1 285 287
Convolution      Conv_161                 1 1 287 288 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=131072
Swish            Mul_163                  1 1 288 290
Split            splitncnn_14             1 2 290 290_splitncnn_0 290_splitncnn_1
Interp           Resize_165               1 1 290_splitncnn_1 295 0=1 1=2.000000e+00 2=2.000000e+00 3=0 4=0 6=0
Concat           Concat_166               2 1 295 258_splitncnn_0 296 0=0
Split            splitncnn_15             1 2 296 296_splitncnn_0 296_splitncnn_1
Convolution      Conv_167                 1 1 296_splitncnn_1 297 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
Swish            Mul_169                  1 1 297 299
Convolution      Conv_170                 1 1 299 300 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
Swish            Mul_172                  1 1 300 302
Convolution      Conv_173                 1 1 302 303 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            Mul_175                  1 1 303 305
Convolution      Conv_176                 1 1 296_splitncnn_0 306 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
Swish            Mul_178                  1 1 306 308
Concat           Concat_179               2 1 305 308 309 0=0
Convolution      Conv_180                 1 1 309 310 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
Swish            Mul_182                  1 1 310 312
Convolution      Conv_183                 1 1 312 313 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
Swish            Mul_185                  1 1 313 315
Split            splitncnn_16             1 2 315 315_splitncnn_0 315_splitncnn_1
Interp           Resize_187               1 1 315_splitncnn_1 320 0=1 1=2.000000e+00 2=2.000000e+00 3=0 4=0 6=0
Concat           Concat_188               2 1 320 224_splitncnn_0 321 0=0
Split            splitncnn_17             1 2 321 321_splitncnn_0 321_splitncnn_1
Convolution      Conv_189                 1 1 321_splitncnn_1 322 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
Swish            Mul_191                  1 1 322 324
Convolution      Conv_192                 1 1 324 325 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Swish            Mul_194                  1 1 325 327
Convolution      Conv_195                 1 1 327 328 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
Swish            Mul_197                  1 1 328 330
Convolution      Conv_198                 1 1 321_splitncnn_0 331 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
Swish            Mul_200                  1 1 331 333
Concat           Concat_201               2 1 330 333 334 0=0
Convolution      Conv_202                 1 1 334 335 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
Swish            Mul_204                  1 1 335 337
Split            splitncnn_18             1 2 337 337_splitncnn_0 337_splitncnn_1
Convolution      Conv_205                 1 1 337_splitncnn_1 338 0=128 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            Mul_207                  1 1 338 340
Concat           Concat_208               2 1 340 315_splitncnn_0 341 0=0
Split            splitncnn_19             1 2 341 341_splitncnn_0 341_splitncnn_1
Convolution      Conv_209                 1 1 341_splitncnn_1 342 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
Swish            Mul_211                  1 1 342 344
Convolution      Conv_212                 1 1 344 345 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
Swish            Mul_214                  1 1 345 347
Convolution      Conv_215                 1 1 347 348 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
Swish            Mul_217                  1 1 348 350
Convolution      Conv_218                 1 1 341_splitncnn_0 351 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
Swish            Mul_220                  1 1 351 353
Concat           Concat_221               2 1 350 353 354 0=0
Convolution      Conv_222                 1 1 354 355 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
Swish            Mul_224                  1 1 355 357
Split            splitncnn_20             1 2 357 357_splitncnn_0 357_splitncnn_1
Convolution      Conv_225                 1 1 357_splitncnn_1 358 0=256 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=589824
Swish            Mul_227                  1 1 358 360
Concat           Concat_228               2 1 360 290_splitncnn_0 361 0=0
Split            splitncnn_21             1 2 361 361_splitncnn_0 361_splitncnn_1
Convolution      Conv_229                 1 1 361_splitncnn_1 362 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=131072
Swish            Mul_231                  1 1 362 364
Convolution      Conv_232                 1 1 364 365 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
Swish            Mul_234                  1 1 365 367
Convolution      Conv_235                 1 1 367 368 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=589824
Swish            Mul_237                  1 1 368 370
Convolution      Conv_238                 1 1 361_splitncnn_0 371 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=131072
Swish            Mul_240                  1 1 371 373
Concat           Concat_241               2 1 370 373 374 0=0
Convolution      Conv_242                 1 1 374 375 0=512 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=262144
Swish            Mul_244                  1 1 375 377
Convolution      Conv_245                 1 1 337_splitncnn_0 378 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3072
Reshape          Reshape_259              1 1 378 396 0=6400 1=8 2=3
Permute          Transpose_260            1 1 396 output 0=1
Convolution      Conv_261                 1 1 357_splitncnn_0 398 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=6144
Reshape          Reshape_275              1 1 398 416 0=1600 1=8 2=3
Permute          Transpose_276            1 1 416 417 0=1
Convolution      Conv_277                 1 1 377 418 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=12288
Reshape          Reshape_291              1 1 418 436 0=400 1=8 2=3
Permute          Transpose_292            1 1 436 437 0=1
