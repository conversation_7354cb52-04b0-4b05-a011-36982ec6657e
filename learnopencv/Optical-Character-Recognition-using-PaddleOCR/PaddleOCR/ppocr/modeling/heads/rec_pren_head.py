# copyright (c) 2022 PaddlePaddle Authors. All Rights Reserve.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

from paddle import nn
from paddle.nn import functional as F


class PRENHead(nn.Layer):
    def __init__(self, in_channels, out_channels, **kwargs):
        super(PRENHead, self).__init__()
        self.linear = nn.Linear(in_channels, out_channels)

    def forward(self, x, targets=None):
        predicts = self.linear(x)

        if not self.training:
            predicts = F.softmax(predicts, axis=2)

        return predicts
