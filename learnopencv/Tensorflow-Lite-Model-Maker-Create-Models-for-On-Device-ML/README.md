# Tensorflow Lite Model Maker: Create Models for On-Device ML

**The repository cointains the code for [Tensorflow Lite Model Maker: Create Models for On-Device ML](https://learnopencv.com/tensorflow-lite-model-maker-create-models-for-on-device-machine-learning/) blogpost**. 

<img src="https://learnopencv.com/wp-content/uploads/2022/05/TensorFlow-Lite-Model-Maker-Create-Models-for-On-Device-Machine-Learning-1024x576.jpg" alt="TFLite Model Maker featureimage" width="950">

### PREREQUISITES

[<img src="https://learnopencv.com/wp-content/uploads/2022/07/download-button-e1657285155454.png" alt="download" width="200">](https://www.dropbox.com/sh/10s6aisjx4c7yor/AAA7zaUGnpSodtHFosMArtVMa?dl=1)


```
pip install -r requirements.txt
```

### Execution

```
streamlit run ./web-app/app.py
```

# AI Courses by OpenCV

Want to become an expert in AI? [AI Courses by OpenCV](https://opencv.org/courses/) is a great place to start. 

<a href="https://opencv.org/courses/">
<p align="center"> 
<img src="https://www.learnopencv.com/wp-content/uploads/2020/04/AI-Courses-By-OpenCV-Github.png">
</p>
</a>
