## Classification With Localization: Convert any Keras Classifier to a Detector.

**This repository contains code for [Classification With Localization: Convert any Keras Classifier to a Detector](https://www.learnopencv.com/classification-with-localization/) blogpost**.

[<img src="https://learnopencv.com/wp-content/uploads/2022/07/download-button-e1657285155454.png" alt="download" width="200">](https://www.dropbox.com/sh/39hx6b0llfhr9q7/AAAvjhW-MFFDvD6e9brvnWt5a?dl=1)

You can also use this ready to run [Colab Notebook](https://colab.research.google.com/drive/1naVE8yU_ryVGvLRxHE_QPR5rjmaVb4ne#scrollTo=wNgVViF5Tz19).

# AI Courses by OpenCV

Want to become an expert in AI? [AI Courses by OpenCV](https://opencv.org/courses/) is a great place to start.

<a href="https://opencv.org/courses/">
<p align="center">
<img src="https://www.learnopencv.com/wp-content/uploads/2020/04/AI-Courses-By-OpenCV-Github.png">
</p>
</a>
