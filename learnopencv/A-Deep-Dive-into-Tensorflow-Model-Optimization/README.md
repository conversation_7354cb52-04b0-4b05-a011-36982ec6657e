# Deep Dive into Tensorflow Model Optimization Toolkit

The repository contains a notebook comparing different model optimization techniques. You can also checkout the [colab notebook](https://colab.research.google.com/github/spmallick/learnopencv/blob/master/A-Deep-Dive-into-Tensorflow-Model-Optimization/TensorFlow_Model_Optimization_Deeper_Dive_into_Model_Optimization.ipynb) here. Find out detailed explanation in the blog post [Deep Dive into tensorFlow Model Optimization Toolkit](https://learnopencv.com/deep-dive-into-tensorflow-model-optimization-toolkit/).

<img src="https://learnopencv.com/wp-content/uploads/2022/05/TFMOT-feature-image.jpg" alt="TFMOT" width="800">

[<img src="https://learnopencv.com/wp-content/uploads/2022/07/download-button-e1657285155454.png" alt="download" width="200">](https://www.dropbox.com/sh/12pxp55xd2jpahq/AAAdb3S-mp5r14eqzdybCONva?dl=1)

## Requirements

```
pip install -r requirements.txt
```

# AI Courses by OpenCV

Want to become an expert in AI? [AI Courses by OpenCV](https://opencv.org/courses/) is a great place to start.

<a href="https://opencv.org/courses/">
<p align="center"> 
<img src="https://www.learnopencv.com/wp-content/uploads/2020/04/AI-Courses-By-OpenCV-Github.png">
</p>
</a>