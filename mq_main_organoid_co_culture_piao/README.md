# organoid_co_culture_piao
## 类器官2色杀伤(PIAO)
### 版本:1.0.0.0
### 调用接口:
|参数名称|参数说明|参数类型|是否必填|备注|
|----|----|----|----|----|
|exp_type|实验类型|String|是|取值为“organoid_co_culture_piao”|
|BF_input_path|输入 BF 图片地址|String|是|绝对路径|
|FL1_input_path|输入 FL1 图片地址|String|是|绝对路径|
|FL2_input_path|输入 FL2 图片地址|String|是|绝对路径|
|BF_labelme_path|输入 BF 的 labelme 文件地址|String|是|绝对路径|
|FL1_labelme_path|输入 FL1 的 labelme 文件地址|String|是|绝对路径|
|FL2_labelme_path|输入 FL2 的 labelme 文件地址|String|是|绝对路径|
|dst_save_path|输出 BF + Mask 图片地址|String|是|绝对路径|
|BF_save_path|输出 BF 图片地址|String|是|绝对路径|
|FL1_save_path|输出 FL1 图片地址|String|是|绝对路径|
|FL2_save_path|输出 FL2 图片地址|String|是|绝对路径|
|merge1_save_path|输出 BF+FL1+FL2 图片地址|String|是|绝对路径|
|merge2_save_path|输出 FL1+FL2 图片地址|String|是|绝对路径|
|data_save_path|统计数据输出地址|String|是|绝对路径,csv 格式|
|fcs_save_path|类流式分析数据输出地址|String|是|绝对路径,csv 格式|
|min_width|类器官最小直径|Int|是|用来过滤小目标.默认:50.范围:大于等于 0.|
|FL2_thresh|FL2 通道图像分割阈值|Int|是|默认:10.范围:0~255.|
|cut_info|抠图参数|String|否|格式:孔类型,倍率,是否抠孔,中间用英文逗号隔开,其中最后一位<br>0:为不抠孔<br>1:为抠孔<br>例子:”25,4,1”,或”21,10,0”|
|param1|预留参数|Float|否|预留参数.|
|param2|预留参数|Float|否|预留参数.|
|param3|预留参数|Float|否|预留参数.|
|param4|预留参数|Float|否|预留参数.|
|param5|预留参数|Float|否|预留参数.|