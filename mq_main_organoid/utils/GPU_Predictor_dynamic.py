import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
import time
import imutils
from PIL import Image
from sahi.slicing import slice_image
from tqdm import tqdm
import numpy as np
import pycuda.driver as cuda #cuda和tensorrt放在cupy之前,否则运行会出错
from pycuda.tools import clear_context_caches
import tensorrt as trt
import cupyx.scipy.ndimage._interpolation as cusi
import cupy as cp
import cv2
from lsnms import nms as large_nms
import gc
from .crop_main_body import crop_hole, crop_muti_image
from loguru import logger as log
import traceback



class Colors:
    # Ultralytics color palette https://ultralytics.com/
    def __init__(self):
        # hex = matplotlib.colors.TABLEAU_COLORS.values()
        hexs = ('FF3838', 'FF9D97', 'FF701F', 'FFB21D', 'CFD231', '48F90A', '92CC17', '3DDB86', '1A9334', '00D4BB',
                '2C99A8', '00C2FF', '344593', '6473FF', '0018EC', '8438FF', '520085', 'CB38FF', 'FF95C8', 'FF37C7')
        self.palette = [self.hex2rgb(f'#{c}') for c in hexs]
        self.n = len(self.palette)

    def __call__(self, i, bgr=False):
        c = self.palette[int(i) % self.n]
        return (c[2], c[1], c[0]) if bgr else c

    @staticmethod
    def hex2rgb(h):  # rgb order (PIL)
        return tuple(int(h[1 + i:1 + i + 2], 16) for i in (0, 2, 4))


def convert_from_cv2_to_image(img: np.ndarray) -> Image:
    return Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))


def convert_from_image_to_cv2(img: Image) -> np.ndarray:
    return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)


class GPUPredictorUtil(object):
    def __init__(self):
        self.gpu_id = 0
        self.allocations = None
        self.engine = None
        self.context = None
        self.inputs = None
        self.outputs = None
        self.device = None
        self.ctx = None

        self.model_file_path = None
        self.SAMmodel=None
        self.task_id = ""
        self.cut_info = None
        self.msg_id = None
        self.ws = None
        self.root_url = 'ws://localhost:12318/ws'  # 这里输入websocket的url
        self.send_response=None
        os.environ['CUDA_VISIBLE_DEVICES'] = f"{self.gpu_id}"



    def release(self):
        self.context.__del__()
        self.engine.__del__()
        if self.allocations:
            for allocation in self.allocations:
                allocation.free()
        clear_context_caches()
        self.allocations = None
        self.inputs = None
        self.outputs = None
        self.engine = None
        self.context = None
        self.SAMmodel=None
        self.gpu_id = 0
        self.ctx = None
        cuda.init()
        self.device = cuda.Device(self.gpu_id)
        self.ctx = self.device.retain_primary_context()
        gc.collect()
        log.info("release model{}".format(self.model_file_path))
        return None


class YoloSegTRT(GPUPredictorUtil):
    def __init__(self, model_file_path):
        super().__init__()

        self.image0 = None
        self.input_data = None
        self.jyh_img = None
        self.img0 = None
        self.crop_hole_image = None
        self.jiaodi_img = None
        self.jyh_img_crop = None
        self.crop_bbox = None
        self.trt_logger = trt.Logger(trt.Logger.ERROR)
        if not os.path.exists(model_file_path):
            log.error("model engine file is not exists in {}".format(model_file_path), exc_info=True)
        else:
            log.info("loading model from {}".format(model_file_path))
        self.model_file_path = model_file_path
        try:
            with open(self.model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                self.engine = runtime.deserialize_cuda_engine(f.read())
            assert self.engine
            log.info(f"context shape {self.engine.create_execution_context().get_binding_shape(0)}")
        except:
            log.error("load model error")
            from .export_trt import EngineBuilder
            if os.path.exists(self.model_file_path.replace(".engine", ".onnx")):
                log.info("find onnx model,try to load onnx model convert to engine")
                builder = EngineBuilder(verbose=False)
                builder.get_engine(self.model_file_path.replace(".engine", ".onnx"),self.model_file_path)
            else:
                log.error(f"{self.model_file_path.replace('.engine', '.onnx')} file is not exists,not convert model to engine,pls check")
            with open(self.model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                self.engine = runtime.deserialize_cuda_engine(f.read())
        cuda.init()
        self.device = cuda.Device(0)
        log.info("Device {}: {}".format(self.gpu_id, self.device.name()))
        log.info("Compute Capability: {}.{}".format(*self.device.compute_capability()))
        log.info("Total Memory: {} GB".format(self.device.total_memory() // (1024 ** 3)))
        log.info(f"GPU-{self.gpu_id} start loading model from {self.model_file_path}")
        self.max_det = 2000
        self.max_shape = 4096
        self.batch_size = 1

        self.ctx = self.device.retain_primary_context()
        try:
            assert self.engine
            self.context = self.engine.create_execution_context()
            assert self.context
            log.info("Device {}: {}".format(self.gpu_id, self.device.name()))
            log.info("Compute Capability: {}.{}".format(*self.device.compute_capability()))
            log.info("Total Memory: {} GB".format(self.device.total_memory() // (1024 ** 3)))
        except Exception as e:
            with open(self.model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                self.engine = runtime.deserialize_cuda_engine(f.read())
            self.context = self.engine.create_execution_context()
            error_message = traceback.format_exc()
            log.error(f"infer engine error and reload engine:{error_message}")

        if -1 in tuple(self.context.get_binding_shape(0)):
            self.dynamic_mode = True
            log.info(f"{self.engine} dynamic_mode:{self.dynamic_mode}")
        else:
            self.dynamic_mode = False
            self.fix_shape = self.context.get_binding_shape(0)[2:]
            self.allocate_buffers(self.context.get_binding_shape(0))
            log.info(f"{self.engine} dynamic_mode:{self.dynamic_mode},shape:{self.fix_shape}")

    def allocate_buffers(self, input_shape=None):
        self.inputs = []
        self.outputs = []
        self.allocations = []
        self.ctx.push()
        for i in range(self.engine.num_bindings):
            name = self.engine.get_binding_name(i)
            dtype = self.engine.get_binding_dtype(i)
            if self.engine.binding_is_input(i):
                if -1 in tuple(self.engine.get_binding_shape(i)):  # dynamic
                    self.context.set_binding_shape(i, tuple(input_shape))
            shape = tuple(self.context.get_binding_shape(i))
            size = np.dtype(trt.nptype(dtype)).itemsize
            for s in shape:
                size *= s
            allocation = cuda.mem_alloc(size)
            binding = {
                'index': i,
                'name': name,
                'dtype': np.dtype(trt.nptype(dtype)),
                'shape': list(shape),
                'allocation': allocation,
            }
            self.allocations.append(allocation)
            if self.engine.binding_is_input(i):
                self.inputs.append(binding)
            else:
                self.outputs.append(binding)
        self.ctx.pop()
        assert len(self.inputs) > 0
        assert len(self.outputs) > 0
        assert len(self.allocations) > 0

    def tensorrt_infer(self, input_image):
        print(input_image.shape)
        try:
            self.allocate_buffers(input_image.shape)
            cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(input_image))
        except Exception as e:
            self.release()
            log.error(f"reboot engine {e} {self.model_file_path}",exc_info=True)
            self.__init__(self.model_file_path)
            self.allocate_buffers(input_image.shape)
            cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(input_image))
        # print("preprocessed_inputs", input_image.shape)
        self.context.execute_v2(self.allocations)
        pred_det = np.zeros(self.outputs[1]['shape'], dtype=self.outputs[1]['dtype'])
        cuda.memcpy_dtoh(pred_det, self.outputs[1]['allocation'])
        proto = np.zeros(self.outputs[0]['shape'], dtype=self.outputs[0]['dtype'])
        cuda.memcpy_dtoh(proto, self.outputs[0]['allocation'])

        print(pred_det.shape, proto.shape)
        return [pred_det, proto]

    def process_mask(self, protos, masks_in, bboxes, shape, upsample=False):
        """
        上采样之前先进行crop裁剪,再上采样（插值法）
        proto_out(ie. protos): [mask_dim, mask_h, mask_w],[32,160,160]
        out_masks(ie. masks_in): [n, mask_dim], n is number of masks after nms,[7,32]
        bboxes: [n, 4], n is number of masks after nms
        shape:input_image_size, (h, w)
        return: h, w, n
        """

        c, mh, mw = protos.shape  # CHW
        ih, iw = shape
        # @就是matmul的另一种写法,torch和numpy都有matmul,
        # masks_in:(3,32)、protos:(32,160,160)
        # 想要的ttt的shape为:[32, 3, 160]
        # ttt = np.matmul(masks_in, protos) #错误
        # 改变维度为c行、1列
        masks = self.sigmoid((masks_in @ protos.astype(np.float32).reshape(c, -1))).reshape(-1, mh, mw)  # CHW
        downsampled_bboxes = bboxes.copy()
        downsampled_bboxes[:, 0] *= mw / iw
        downsampled_bboxes[:, 2] *= mw / iw
        downsampled_bboxes[:, 3] *= mh / ih
        downsampled_bboxes[:, 1] *= mh / ih
        masks = self.crop_mask(masks, downsampled_bboxes)  # CHW
        # tt = masks.transpose(2, 1, 0)  # CHW->HWC,便于opencv的resize操作（仅可用于hwc）
        if upsample:
            # masks = ndimage.zoom(masks[None], (1, 1, 4, 4),
            #                      order=1,
            #                      mode="nearest")[0]
            # masks = masks.transpose(2, 0, 1)  #HWC->CHW #慢的地方
            masks = cusi.zoom(cp.array(masks[None]), (1, 1, 4, 4), order=1, mode="nearest")[0]
            masks = cp.asnumpy(masks)
        tttt = masks.__gt__(0.5).astype(np.float32)
        return tttt  # 大于0.5的

    def process_mask_native(self, protos, masks_in, bboxes, shape):
        """
        Crop after upsample.
        protos: [mask_dim, mask_h, mask_w]
        masks_in: [n, mask_dim], n is number of masks after nms
        bboxes: [n, 4], n is number of masks after nms
        shape: input_image_size, (h, w)

        return: h, w, n
        """
        c, mh, mw = protos.shape  # CHW
        masks = self.sigmoid((masks_in @ protos.astype(np.float32).reshape(c, -1))).reshape(-1, mh, mw)  # CHW
        gain = min(mh / shape[0], mw / shape[1])  # gain  = old / new
        pad = (mw - shape[1] * gain) / 2, (mh - shape[0] * gain) / 2  # wh padding
        top, left = int(pad[1]), int(pad[0])  # y, x
        bottom, right = int(mh - pad[1]), int(mw - pad[0])
        masks = masks[:, top:bottom, left:right]
        segments = []
        for mask in tqdm(masks):
            print(mask.shape)
            mask = cusi.zoom(cp.array(mask[None]), (1, shape[0] / mask.shape[0], shape[1] / mask.shape[1]), order=1,
                             mode="nearest")
            print(shape, mask.shape)
            masks = self.crop_mask(masks, bboxes)  # CHW
            mask = cp.asnumpy(mask).__gt__(0.5).astype(np.float32)
            chips_segments = self.masks2segments(mask)
            del mask
            segments.extend(chips_segments)
        del masks
        return segments  # 大于0.5的

    def letterbox(self, im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True,
                  stride=32):
        # Resize and pad image while meeting stride-multiple constraints
        shape = im.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)
        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:  # only scale down, do not scale up (for better val mAP)
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2
        if shape[::-1] != new_unpad:  # resize
            im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
        return im, ratio, (dw, dh)

    def box_area(self, box):
        # box = xyxy(4,n)
        return (box[2] - box[0]) * (box[3] - box[1])

    def box_iou(self, box1, box2, eps=1e-7):
        (a1, a2), (b1, b2) = np.split(box1[:, None], 2,
                                      axis=2), np.split(box2, 2, axis=1)
        array = np.minimum(a2, b2) - np.maximum(a1, b1)
        inter = array.clip(0)
        inter = inter.prod(2)

        # IoU = inter / (area1 + area2 - inter)
        return inter / (self.box_area(box1.T)[:, None] +
                        self.box_area(box2.T) - inter + eps)

    def empty_like(self, x):
        """Creates empty torch.Tensor or np.ndarray with same shape as input and float32 dtype."""
        return np.empty_like(x, dtype=np.float32) # numpy only
    def xywh2xyxy(self, x):
        """
        Convert bounding box coordinates from (x, y, width, height) format to (x1, y1, x2, y2) format where (x1, y1) is the
        top-left corner and (x2, y2) is the bottom-right corner. Note: ops per 2 channels faster than per channel.

        Args:
            x (np.ndarray): The input bounding box coordinates in (x, y, width, height) format.

        Returns:
            y (np.ndarray): The bounding box coordinates in (x1, y1, x2, y2) format.
        """
        y = self.empty_like(x)  # faster than clone/copy
        xy = x[..., :2]  # centers
        wh = x[..., 2:] / 2  # half width-height
        y[..., :2] = xy - wh  # top left xy
        y[..., 2:] = xy + wh  # bottom right xy
        return y

    def sigmoid(self, x):
        s = 1 / (1 + np.exp(-x))
        return s

    def skimage2opencv(self, src):
        src *= 255
        src.astype(int)
        return src

    def bbox_iou(self, box1, box2):
        """
        Calculate the Intersection of Union (IoU) of two bounding boxes.

        Args:
            box1 (list of float): [x1, y1, x2, y2]
            box2 (list of float): [x1, y1, x2, y2]

        Returns:
            float: The IoU of the two boxes.
        """
        # 计算两个框的面积
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])

        # 计算两个框的交集坐标
        inter_min_x = max(box1[0], box2[0])
        inter_min_y = max(box1[1], box2[1])
        inter_max_x = min(box1[2], box2[2])
        inter_max_y = min(box1[3], box2[3])

        # 计算交集面积
        inter_area = max(inter_max_x - inter_min_x, 0) * max(inter_max_y - inter_min_y, 0)

        # 计算并集面积
        union_area = area1 + area2 - inter_area

        # 计算IoU
        iou = inter_area / union_area if union_area > 0 else 0

        return iou

    def overlap_iou_cell(self, det, iou_threshold=0.5):
        # 定义一个空列表用于存储成对的索引
        paired_indexes = []
        # 定义一个空列表用于存储单个的索引
        single_indexes = []
        # 定义一个阈值
        # 遍历所有的检测结果
        for i in range(det.shape[0]):
            # 遍历当前检测结果之后的所有检测结果
            for j in range(i + 1, det.shape[0]):
                # 计算当前检测结果与之后检测结果的iou
                iou = self.bbox_iou(det[i, :4], det[j, :4])
                # print(f"{i}-{j}-{iou}")
                # 如果iou大于阈值,则将成对的索引加入paired_indexes列表
                if iou > iou_threshold:
                    paired_indexes.append([i, j])
        # 遍历所有的检测结果
        for i in range(det.shape[0]):
            # 如果当前检测结果的索引不在paired_indexes中,则将其加入single_indexes列表
            if not any(i in pair for pair in paired_indexes):
                single_indexes.append(i)
        # 将paired_indexes和single_indexes合并为一个列表
        result = paired_indexes + single_indexes
        # 输出result列表
        return result

    def non_max_suppression(self,
                            prediction,
                            conf_thres=0.25,
                            iou_thres=0.45,
                            nm=32):

        nc = prediction.shape[2] - nm - 5  # number of classes
        xc = prediction[..., 4] > conf_thres  # candidates
        if nc > 1000:
            assert nc == 1000, f"nc={nc} 不是v5模型"
        # Settings
        # min_wh = 2  # (pixels) minimum box width and height
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into cv2.dnn.NMSBoxes
        redundant = True  # require redundant detections
        merge = False  # use merge-NMS,False

        mi = 5 + nc  # mask start index,117中,前面是85（80类cls score, 4box, 1个obj score）,后面是32(mask coeffient)

        # numpy array不支持空数组的调用
        # https://blog.csdn.net/weixin_31866177/article/details/107380707
        # https://bobbyhadz.com/blog/python-indexerror-index-0-is-out-of-bounds-for-axis-0-with-size-0
        # https://blog.csdn.net/weixin_38753213/article/details/106754787
        # 不能对数组的元素赋值,比如 a=np.empty((0,6)),a[0]=1,这样会出错
        # output = np.zeros((0, 6 + nm), np.float32) * bs
        # 因此我们使用列表-》append-》转为numpy array
        output = []
        output_final = []
        for xi, x in enumerate(prediction):  # image index, image inference

            # confidence, xc的shape:(1, 25200), xi = 0, x的shape:(25200, 117)
            # 下面这句话就是筛选出obj score > conf_thres的instances
            # 经过筛选后的x的shape为:(44, 117)
            x = x[xc[xi]]

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Compute conf
            x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf

            # Box/Mask
            # center_x, center_y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(x[:, :4])  # shape[44, 4]
            # zero columns if no masks,从第index=85(第86个数)开始至117
            mask = x[:, mi:]  # mask shape[44, 32]

            # Detections matrix nx6 (xyxy, conf, cls)

            # best class only
            # x[:, 5:mi]是去除了4box + 1obj score的,就是cls score的从5到85
            # 下面这个max的第一个参数1,表示axis=1,就是按照列进行筛选cls中的最大值,且返回索引.
            # keepdim 表示是否需要保持输出的维度与输入一样,keepdim=True表示输出和输入的维度一样,
            # keepdim=False表示输出的维度被压缩了,也就是输出会比输入低一个维度.
            # j:Get the class with the highest confidence
            conf, j = x[:, 5:mi].max(axis=1), x[:, 5:mi].argmax(axis=1)
            conf, j = conf.reshape(-1, 1), j.reshape(-1, 1)

            # x的shape从[44, 38]经过conf.reshape(-1) > conf_thres筛选后变为
            # [43, 38],且:38 = 4box + 1conf + 1类别id + 32coeffients
            x = np.concatenate((box, conf, j.astype(float), mask),
                               axis=1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            elif n > max_nms:  # excess boxes
                x = x[(-x[:, 4]).argsort()[:max_nms]]  # sort by confidence
            else:
                x = x[(-x[:, 4]).argsort()]  # sort by confidence

            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            # boxes (offset by class), scores
            boxes, scores = x[:, :4] + c, x[:, 4]
            # i = cv2.dnn.NMSBoxes(boxes.tolist(), scores.tolist(),
            #                      self.conf_thres, self.iou_thres)  # NMS
            i = large_nms(boxes, scores, score_threshold=conf_thres, iou_threshold=iou_thres)
            # if i.shape[0] > max_det:  # limit detections
            #     i = i[:max_det]
            if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)
                x[i, :4] = np.dot(weights, x[:, :4]).astype(np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy

            # output[xi] = x[i]
            output.append(x[i])
            # if (time.time() - t) > time_limit:
            #     print(f'WARNING ⚠️ NMS time limit {time_limit:.3f}s exceeded')
            #     break  # time limit exceeded

        output = np.array(output).reshape(-1, 6 + nm)
        output_final.append(output)

        return output_final

    def non_max_suppression_v8(self,
                               prediction,
                               conf_thres=0.25,
                               iou_thres=0.45,
                               nm=32):
        assert 0 <= conf_thres <= 1, f'Invalid Confidence threshold {conf_thres}, valid values are between 0.0 and 1.0'
        assert 0 <= iou_thres <= 1, f'Invalid IoU {iou_thres}, valid values are between 0.0 and 1.0'
        if isinstance(prediction,
                      (list, tuple)):  # YOLOv8 model in validation model, output = (inference_out, loss_out)
            prediction = prediction[0]  # select only inference output
        bs = prediction.shape[0]  # batch size
        nc = prediction.shape[1] - nm - 4  # number of classes
        mi = 4 + nc  # mask start index
        # xc = prediction[:, 4:mi].max(1) > conf_thres  # candidates
        xc = prediction[:, 4:mi].max(1) > conf_thres
        # Settings
        # min_wh = 2  # (pixels) minimum box width and height
        redundant = True  # require redundant detections
        merge = False  # use merge-NMS
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into torchvision.ops.nms()

        # output = [torch.zeros((0, 6), device=prediction.device)] * bs
        output = [np.zeros((0, 6 + nm))] * bs
        for xi, x in enumerate(prediction):  # image index, image inference
            # Apply constraints
            # x[((x[..., 2:4] < min_wh) | (x[..., 2:4] > max_wh)).any(1), 4] = 0  # width-height
            x = np.transpose(x)[xc[xi]]  # confidence

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Detections matrix nx6 (xyxy, conf, cls)
            box, cls, mask = x[:, :4], x[:, 4:nc + 4], x[:, nc + 4:]

            # Box (center x, center y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(box)

            # Detections matrix nx6 (xyxy, conf, cls)
            conf = cls.max(1, keepdims=True)
            j_argmax = cls.argmax(1)
            j = j_argmax if j_argmax.shape == x[:, 5:].shape else \
                np.expand_dims(j_argmax, 1)  # for argmax(axis, keepdims=True)
            x = np.concatenate((box, conf, j.astype(np.float32), mask), 1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            # elif n > max_nms:  # excess boxes
            #     x_argsort = np.argsort(x[:, 4])[:max_nms] # sort by confidence
            #     x = x[x_argsort]
            x_argsort = np.argsort(x[:, 4])[::-1][:max_nms]  # sort by confidence
            x = x[x_argsort]
            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores

            #############################################
            # i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
            i = large_nms(boxes, scores, iou_thres)
            ############################################

            # if i.shape[0] > max_det:  # limit detections
            #     i = i[:max_det]
            if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)
                x[i, :4] = np.dot(weights, x[:, :4]).astype(np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy

            output[xi] = x[i]

        return output

    def clip_boxes(self, boxes, shape):
        # Clip boxes (xyxy) to image shape (height, width)
        if isinstance(boxes, np.ndarray):  # faster individually
            # np.array (faster grouped)
            boxes[:, [0, 2]] = boxes[:, [0, 2]].clip(0, shape[1])  # x1, x2
            boxes[:, [1, 3]] = boxes[:, [1, 3]].clip(0, shape[0])  # y1, y2
        else:
            print("type wont be supported")

    def scale_boxes(self, img1_shape, boxes, img0_shape, ratio_pad=None):
        # Rescale boxes (xyxy) from img1_shape to img0_shape
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0],
                       img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (
                    img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]

        boxes[:, [0, 2]] -= pad[0]  # x padding
        boxes[:, [1, 3]] -= pad[1]  # y padding
        boxes[:, :4] /= gain
        self.clip_boxes(boxes, img0_shape)
        return boxes

    def crop_mask(self, masks, boxes):
        """
        "Crop" predicted masks by zeroing out everything not in the predicted bbox.
        Vectorized by Chong (thanks Chong).
        Args:
            - masks should be a size [h, w, n] ndarray of masks
            - boxes should be a size [n, 4] ndarray of bbox coords in relative point form
        """

        n, h, w = masks.shape
        x1, y1, x2, y2 = np.split(boxes[:, :, None], 4, axis=1)  # x1 shape(n,1,1)
        r = np.arange(w, dtype=x1.dtype)[None, None, :]  # rows shape(1,w,1)
        c = np.arange(h, dtype=x1.dtype)[None, :, None]  # cols shape(h,1,1)
        return masks * ((r >= x1) * (r < x2) * (c >= y1) * (c < y2))

    def preprocess(self, img_src_bgr):
        """_summary_
        Args:
            img_src_bgr (numpy array uint8): bgr

        """
        self.image0 = img_src_bgr
        # shape = max(640, max(self.image0.shape) - max(self.image0.shape) % 32)
        shape = max(640, max(self.image0.shape))
        if shape % 32 != 0:
            shape = int(shape / 32) * 32
        shape = min(shape, self.max_shape)
        letterbox_img = self.letterbox(self.image0, shape, stride=64, auto=False)[0]  # padded resize
        img = letterbox_img.transpose(2, 0, 1)[::-1]  # hwc->chw,bgr->rgb
        self.input_data = np.ascontiguousarray(img)
        self.input_data = self.input_data.astype('float32')
        self.input_data /= 255
        if len(self.input_data.shape) == 3:
            self.input_data = self.input_data[None]  # expand for batch dim
        return self.input_data

    def find_cricel_mask(self, mask, conuter):
        mask = mask.astype(np.uint8)
        _, _, bw, bh = cv2.boundingRect(conuter)
        kennel_size = int(min(bw, bh) / 5) if (min(bw, bh) / 5) > 3 else 3  # 滤波器大小
        kennel_size |= 1
        kennel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kennel_size, kennel_size))
        kennel3 = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        eroded_mask = cv2.erode(mask, kennel)
        dilated_mask = cv2.dilate(mask, kennel3)
        cricle_mask = dilated_mask - eroded_mask
        return cricle_mask

    def apply_mask(self, image, mask, color, alpha=0.5):
        """Apply the given mask to the image.
        """
        for c in range(3):
            image[:, :, c] = np.where(mask == 1,
                                      image[:, :, c] *
                                      (1 - alpha) + alpha * color[c],
                                      image[:, :, c])
        return image

    def masks2segments(self, masks, strategy='largest'):
        segments = []
        for x in masks:
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            # # mask = cv2.dilate(x.astype("uint8"), kernel,2)MORPH_OPEN MORPH_OPEN
            # # mask = cv2.morphologyEx(x.astype("uint8"), cv2.MORPH_CLOSE, kernel)
            x = cv2.morphologyEx(x.astype("uint8"), cv2.MORPH_OPEN, kernel)
            # c = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
            mask = np.squeeze(x)
            mask = mask.astype(np.uint8)
            mask = cv2.copyMakeBorder(mask, 1, 1, 1, 1, cv2.BORDER_CONSTANT, value=0)
            c = cv2.findContours(mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE, offset=(-1, -1))[0]
            # x,y,w,h=cv2.boundingRect(c)
            # kernel_size=max(w,h)/3
            # kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
            # x = cv2.morphologyEx(x.astype("uint8"), cv2.MORPH_OPEN, kernel)
            # mask = np.squeeze(x)
            # mask = mask.astype(np.uint8)
            # c = cv2.findContours(mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE, offset=(-1, -1))[0]
            # c=[cv2.approxPolyDP(x, (1e-100) * cv2.arcLength(x, True), True) for x in c] #直线变曲线
            # c = cv2.findContours(x.astype("uint8"), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
            if c:
                if strategy == 'concat':  # concatenate all segments
                    c = np.concatenate([x.reshape(-1, 2) for x in c])
                elif strategy == 'largest':  # select largest segment
                    c = np.array(c[np.array([len(x) for x in c]).argmax()]).reshape(-1, 2)
            else:
                c = np.zeros((0, 2))  # no segments found
            segments.append(c.astype('float32'))
        return segments

    def clip_segments(self, boxes, shape):
        # Clip segments (xy1,xy2,...) to image shape (height, width)
        boxes[:, 0] = boxes[:, 0].clip(0, shape[1])  # x
        boxes[:, 1] = boxes[:, 1].clip(0, shape[0])  # y

    def scale_segments(self, img1_shape, segments, img0_shape, shift_amount=None, ratio_pad=None):
        # Rescale coords (xyxy) from img1_shape to img0_shape
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]
        segments[:, 0] -= pad[0]  # x padding
        segments[:, 1] -= pad[1]  # y padding
        segments /= gain
        self.clip_segments(segments, img0_shape)
        if shift_amount is not None:
            segments[:, 0] += shift_amount[0]  # x padding
            segments[:, 1] += shift_amount[1]  # y padding
        return segments

    def mask_crop_processing(self, img0, object_prediction_list, conf_thres=0.1, iou_thres=0.1):
        object_prediction_list = [object_prediction for object_prediction in object_prediction_list if
                                  object_prediction]
        if not object_prediction_list:
            return img0
        det = np.concatenate([x[0] for x in object_prediction_list], axis=0)
        old_det = np.concatenate([x[2] for x in object_prediction_list], axis=0)
        keep = large_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
        # print('keep:', keep)  # [...]
        proto_info = []
        [proto_info.extend(x[1]) for x in object_prediction_list]
        end_segments = []
        # new_input_shape=(new_h,new_w)
        for keep_number in tqdm(keep):
            # print(f'+++++++++++++++++++++++++++/{list(keep).index(keep_number)}/+++++++++++++++++++++++++++')
            probo = proto_info[keep_number][0]
            input_data_shape = proto_info[keep_number][1]  # [np, (1280, 1280), (7146, 6776, 3), [0, 0]]
            masks = self.process_mask(
                probo,
                np.expand_dims(old_det[keep_number, 6:], axis=0),
                np.expand_dims(old_det[keep_number, :4], axis=0),
                input_data_shape,
                upsample=True)  # CHW,[instances_num, 640, 640]
            segments = self.masks2segments(masks)  # mask to findContours points [[]]
            del masks
            # findContours points do padding to ori img
            segments = [self.scale_segments(proto_info[keep_number][1], x, proto_info[keep_number][2],
                                            proto_info[keep_number][3]) for num, x in enumerate(segments)]  # list
            end_segments.extend(segments)
        if len(end_segments) == 0:
            return img0
        img_copy = img0.copy()
        print("len(end_segments)", len(end_segments))
        for cell_num in range(len(end_segments)):
            if len(np.unique(end_segments[cell_num])) < 3:
                continue
            img_copy = cv2.drawContours(img_copy, np.array([end_segments[cell_num]]).astype(int), -1, (255, 255, 255),
                                        -1)
        # cv2.imwrite("test.jpg",img_copy)
        return img_copy

    def read_image(self, input_src, jiaodi_corp=False, slice_pred_thed_max=4218):
        if isinstance(input_src, str):
            self.jyh_img = cv2.imdecode(np.fromfile(input_src, dtype=np.uint8), cv2.IMREAD_COLOR)
            try:
                if "_cut.jpg" in input_src:
                    self.img0 = cv2.imdecode(np.fromfile(input_src[:-8] + "_merge_src.jpg", dtype=np.uint8),
                                             cv2.IMREAD_COLOR)
                else:
                    self.img0 = cv2.imdecode(np.fromfile(input_src[:-4] + "_src.jpg", dtype=np.uint8), cv2.IMREAD_COLOR)
            except Exception as e:
                self.img0 = self.jyh_img
                log.error(f"不存在均一化图,使用原图进行分析 {e}")
            print("img0.shape", self.img0.shape)
            log.info(f"img0.shape {self.img0.shape}")
            try:
                CutoutConfig = os.path.join(os.sep.join(os.path.dirname(input_src).split(os.sep)[:-1]),
                                            "CutoutConfig.txt")
                if os.path.exists(
                        CutoutConfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp', 'imagesource')):
                    CutoutConfig = CutoutConfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp',
                                                                                                'imagesource')
                log.info(f"检查CutoutConfig.txt {CutoutConfig.replace('imageTemp', 'imagesource')}")
                if os.path.exists(CutoutConfig.replace('imageTemp', 'imagesource')):
                    with open(CutoutConfig.replace('imageTemp', 'imagesource'), "r") as f:
                        cut_info = f.read().split(",")
                else:
                    log.error("不存在CutoutConfig.txt")
                    cut_info = self.cut_info.split(",")
                log.info(f"cut info {cut_info}")
                self.crop_hole_image = cv2.bitwise_and(self.img0, self.img0,
                                                       mask=crop_hole(self.img0, kong_num=cut_info[0],
                                                                      lens=cut_info[1],
                                                                      view_judge=int(cut_info[2])))
            except Exception as e:
                self.crop_hole_image = self.img0
                log.error(f"crop hole fail {e}")
            if jiaodi_corp:
                if max(self.img0.shape) > slice_pred_thed_max:
                    print('------------------------------crop img-------------------------------------')
                    try:
                        self.crop_bbox = crop_muti_image(self.img0)[0]
                        self.jiaodi_img = self.crop_hole_image[self.crop_bbox[1]:self.crop_bbox[3],
                                          self.crop_bbox[0]:self.crop_bbox[2]]
                        self.jyh_img_crop = self.jyh_img[self.crop_bbox[1]:self.crop_bbox[3],
                                            self.crop_bbox[0]:self.crop_bbox[2]]
                        print("crop_main_image ", self.crop_bbox, self.img0.shape, self.jiaodi_img.shape)
                    except Exception as e:
                        self.jiaodi_img = self.crop_hole_image
                        log.error(f"crop jiaodi fail {e}")
                        self.crop_bbox = [0, 0, self.img0.shape[1], self.img0.shape[0]]
                        self.jyh_img_crop = self.jyh_img
                else:
                    log.info('no crop hole')
                    self.jiaodi_img = self.crop_hole_image
                    self.jyh_img_crop = self.jyh_img
                    self.crop_bbox = [0, 0, self.img0.shape[1], self.img0.shape[0]]
        else:
            log.error(f"img error {input_src}")

    def process_segments(self, protos, masks_in, bboxes, shape, upsample=False):
        """
        上采样之前先进行crop裁剪,再上采样（插值法）
        proto_out(ie. protos): [mask_dim, mask_h, mask_w],[32,160,160]
        out_masks(ie. masks_in): [n, mask_dim], n is number of masks after nms,[7,32]
        bboxes: [n, 4], n is number of masks after nms
        shape:input_image_size, (h, w)
        return: h, w, n
        """

        c, mh, mw = protos.shape  # CHW
        ih, iw = shape
        # @就是matmul的另一种写法,torch和numpy都有matmul,
        # masks_in:(3,32)、protos:(32,160,160)
        # 想要的ttt的shape为:[32, 3, 160]
        # ttt = np.matmul(masks_in, protos) #错误
        masks = self.sigmoid((masks_in @ protos.astype(np.float32).reshape(c, -1))).reshape(-1, mh, mw)  # CHW

        downsampled_bboxes = bboxes.copy()
        downsampled_bboxes[:, 0] *= mw / iw
        downsampled_bboxes[:, 2] *= mw / iw
        downsampled_bboxes[:, 3] *= mh / ih
        downsampled_bboxes[:, 1] *= mh / ih

        masks = self.crop_mask(masks, downsampled_bboxes)  # CHW
        # tt = masks.transpose(2, 1, 0)  # CHW->HWC,便于opencv的resize操作（仅可用于hwc）
        segments = []
        if upsample:
            # masks = ndimage.zoom(masks[None], (1, 1, 4, 4),
            #                      order=1,
            #                      mode="nearest")[0]
            # 使用numba的resize函数来优化upsample操作
            try:
                masks_resize = cusi.zoom(cp.array(masks[None]), (1, 1, 4, 4), order=1, mode="nearest")[0]
                masks_resize = cp.asnumpy(masks_resize)
                masks_resize = masks_resize.__gt__(0.5).astype(np.float32)
                segments = self.masks2segments(masks_resize)
                segments = [self.scale_segments(self.input_data.shape[2:], x, self.image0.shape).round() for x in
                            segments]
                del masks_resize
            except Exception as e:
                error_msg=traceback.format_exc()
                log.error(error_msg)
                log.info(f"masks too large Out of GPU memory start")
                for mask in tqdm(masks):
                    mask = cusi.zoom(cp.array(mask[None]), (1, 4, 4), order=1, mode="nearest")
                    mask = cp.asnumpy(mask).__gt__(0.5).astype(np.float32)
                    chips_segments = self.masks2segments(mask)
                    del mask
                    segments.extend(
                        [self.scale_segments(self.input_data.shape[2:], x, self.image0.shape).round() for x in
                         chips_segments])
                log.info("masks too large Out of GPU memory end")
            del masks
        return segments  # 大于0.5的

    def get_prediction(self, image,
                       conf_threshold,
                       iou_threshold,
                       shift_amount=None):
        if shift_amount is None:
            shift_amount = [0, 0]
        net_input_data = self.preprocess(image)
        pred_det, proto = self.tensorrt_infer(net_input_data)
        object_prediction_list = []
        try:
            pred = self.non_max_suppression(pred_det,
                                            conf_threshold,
                                            iou_threshold)
        except Exception as e:
            print("切换v8解析",e)
            pred = self.non_max_suppression_v8(pred_det,
                                               conf_threshold,
                                               iou_threshold)
        # print("单张的nms处理完成")
        for i, det in enumerate(pred):
            if len(det):
                old_det = det.copy()
                proto_mask = [proto[i], self.input_data.shape[2:], self.image0.shape, shift_amount]
                det[:, :4] = self.scale_boxes(self.input_data.shape[2:], det[:, :4], self.image0.shape).round()
                det[:, 0] = det[:, 0] + shift_amount[0]
                det[:, 1] = det[:, 1] + shift_amount[1]
                det[:, 2] = det[:, 2] + shift_amount[0]
                det[:, 3] = det[:, 3] + shift_amount[1]
                object_prediction_list.append([det, [proto_mask for _ in range(det.shape[0])], old_det])
        return object_prediction_list

    def fullshape_instance_predict(self, image, conf_threshold, iou_threshold, durations_in_seconds):
        time_start = time.time()
        log.info(f"max_shape {self.max_shape}", )
        net_input_data = self.preprocess(image)
        pred_det, proto = self.tensorrt_infer(net_input_data)
        detection_start = time.time()
        durations_in_seconds["detection"] = detection_start - time_start
        try:
            pred = self.non_max_suppression(pred_det,
                                            conf_threshold,
                                            iou_threshold)
        except Exception as e:
            log.info(f"切换成v8解析")
            pred = self.non_max_suppression_v8(pred_det,
                                               conf_threshold,
                                               iou_threshold)
        nmsstart = time.time()
        durations_in_seconds["NMS"] = nmsstart - detection_start
        det = []
        segments = []
        for i, det in enumerate(pred):
            if len(det):
                segments = self.process_segments(
                    proto[i],
                    det[:, 6:],
                    det[:, :4],
                    self.input_data.shape[2:],
                    upsample=True)  # CHW,[instances_num, 640, 640]
                # Rescale boxes from img_size to im0 size
                # 就是将当前在letterbox等处理后的图像上检测的box结果,映射返回原图大小上
                det[:, :4] = self.scale_boxes(self.input_data.shape[2:], det[:, :4], self.image0.shape).round()
                # print(det.shape[0],len(segments))
                log.info(f"cell number {det.shape[0]}")
        try:
            self.send_response("MsgTime", self.msg_id,{"tid": self.task_id, "Time": int(len(segments) * 0.3) + 300})
        except Exception as e:
            log.error(f"send MsgTime error: {e}")
        get_maskstart = time.time()
        durations_in_seconds["get_mask"] = get_maskstart - nmsstart
        log.info(durations_in_seconds)
        return det, segments

    def salice_instance_predict(self, image, conf_thres, iou_thres, max_shape, slice_pred_thed_max,
                                durations_in_seconds):
        perform_full_pred = True if max(image.shape) < slice_pred_thed_max else False  # whole pic det
        log.info(f"perform_full: {perform_full_pred} max_shape {self.max_shape}")
        time_start = time.time()
        # perform sliced prediction
        object_prediction_list = []
        if perform_full_pred:
            # perform full prediction
            log.info('----------------------------------只整图预测---------------------------------')
            for i_shap in list(set(list([960, 1280, self.max_shape]))):
                self.max_shape = i_shap
                log.info(f"max_shape: {self.max_shape}")
                prediction_result = self.get_prediction(
                    image=image,
                    conf_threshold=conf_thres,
                    iou_threshold=iou_thres,
                    shift_amount=None,
                )
                object_prediction_list.extend(prediction_result)
        else:
            log.info('-------------------------------------先整图预测-------------------------------------')
            for i_shap in [640, 1280]:
                self.max_shape = i_shap
                log.info(f"max_shape: {self.max_shape}")
                prediction_result = self.get_prediction(
                    image=image,
                    conf_threshold=conf_thres,
                    iou_threshold=iou_thres,
                    shift_amount=None
                )
                object_prediction_list.extend(prediction_result)
            masked_img0 = self.mask_crop_processing(image, object_prediction_list, conf_thres, iou_thres)
            log.info('---------------------------------再切片预测--------------------------------')
            num_batch = 1
            self.max_shape = max_shape
            slice_image_result = slice_image(
                image=convert_from_cv2_to_image(masked_img0),
                slice_height=self.max_shape,
                slice_width=self.max_shape,
                overlap_height_ratio=0.5,
                overlap_width_ratio=0.5
            )
            num_slices = len(slice_image_result)
            log.info(f"Number of slices: {num_slices}")
            slice_start = time.time()
            durations_in_seconds["slice"] = slice_start - time_start
            num_group = int(num_slices / num_batch)
            for group_ind in range(num_group):
                image_list = []
                shift_amount_list = []
                for image_ind in range(num_batch):
                    image_list.append(slice_image_result.images[group_ind * num_batch + image_ind])
                    shift_amount_list.append(slice_image_result.starting_pixels[group_ind * num_batch + image_ind])
                    print(f"总{num_slices}片处理第{group_ind + 1}片--", image_list[0].shape, shift_amount_list[0])
                    for i_shape in [640, 960]:
                        self.max_shape = i_shape
                        prediction_result = self.get_prediction(
                            image=convert_from_image_to_cv2(image_list[0]),
                            conf_threshold=conf_thres,
                            iou_threshold=iou_thres,
                            shift_amount=shift_amount_list[0],
                        )
                        object_prediction_list.extend(prediction_result)

        detection_start = time.time()
        durations_in_seconds["detection"] = detection_start - time_start
        # remove empty predictions
        object_prediction_list = [object_prediction for object_prediction in object_prediction_list if
                                  object_prediction]
        end_det = []
        end_segments = []
        if len(object_prediction_list) > 0:
            det = np.concatenate([x[0] for x in object_prediction_list], axis=0)
            old_det = np.concatenate([x[2] for x in object_prediction_list], axis=0)
            try:
                keep = large_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
                end_det = det[keep]
                log.info(f"cell_number {end_det.shape[0]}")
                nmsstart = time.time()
                durations_in_seconds["NMS"] = nmsstart - detection_start
                proto_info = []
                [proto_info.extend(x[1]) for x in object_prediction_list]
                for keep_number in tqdm(keep):
                    probo = proto_info[keep_number][0]
                    input_data_shape = proto_info[keep_number][1]  # [np, (1280, 1280), (7146, 6776, 3), [0, 0]]
                    masks = self.process_mask(
                        probo,
                        np.expand_dims(old_det[keep_number, 6:], axis=0),
                        np.expand_dims(old_det[keep_number, :4], axis=0),
                        input_data_shape,
                        upsample=True)  # CHW,[instances_num, 640, 640]
                    segments = self.masks2segments(masks)  # mask to findContours points [[]]
                    del masks
                    segments = [self.scale_segments(proto_info[keep_number][1], x, proto_info[keep_number][2],
                                                    proto_info[keep_number][3]) for num, x in
                                enumerate(segments)]  # list
                    end_segments.extend(segments)
                    maskend = time.time()
                    durations_in_seconds["mask"] = maskend - nmsstart
            except Exception as e:
                error_msg = traceback.format_exc()
                log.error(error_msg)
        log.info(durations_in_seconds)
        try:
            self.send_response("MsgTime", self.msg_id,{"tid": self.task_id, "Time": int(len(end_segments) * 0.3) + 300})
        except Exception as e:
            log.error(f"send MsgTime error: {e}")
        return end_det, end_segments

    def find_conuter(self, gray):
        try:
            cnts = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cnts = imutils.grab_contours(cnts)
            maxc = max(cnts, key=cv2.contourArea)
        except Exception as e:
            maxc = []
        return maxc
